package com.jdh.o2oservice.core.domain.medpromise.model;

import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseAggregateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 检测单
 * @Author: wangpengfei144
 * @Date: 2024/4/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicalPromise implements Aggregate<MedicalPromiseIdentifier> {



    /** 主键 */
    private Long id;

    /** 检测单ID */
    private Long medicalPromiseId;
    /**
     * 供应商预约单号
     */
    private String outerId;

    /** 垂直业务身份编码 */
    private String verticalCode;

    /** 服务类型 */
    private String serviceType;
    /**
     * 服务ID，快检情况下是skuNo
     */
    private String serviceId;

    /** 用户pin */
    private String userPin;

    /** 履约单ID */
    private Long promiseId;

    /** 服务单ID */
    private Long voucherId;

    /** 用户唯一ID */
    private Long promisePatientId;

    /** 检测单状态：待检测/检测中/已出报告 */
    private Integer status;

    /** 样本条码 */
    private String specimenCode;

    /** 检测项目信息 */
    private String serviceItemId;

    /** 检测项目名称 */
    private String serviceItemName;

    /** 供应商渠道编码 */
    private Long providerId;

    /** 服务地点id */
    private String stationId;

    /** 服务地点详细地址 */
    private String stationAddress;

    /** 服务地点名称 */
    private String stationName;

    /** 服务地点联系方式 */
    private String stationPhone;

    /** 是否有效 1有效 0无效 */
    private Integer yn;

    /** 版本号 */
    private Integer version;

    /** 数据来源分支 */
    private String branch;

    /** 创建人 */
    private String createUser;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateUser;

    /** 修改时间 */
    private Date updateTime;

    /**
     * 是否冻结， 1冻结 0未冻结
     */
    private Integer freeze;
    /**
     * 结算状态 0-结算，1-已结算
     */
    private Integer settleStatus;
    /**
     * 检测单IDs
     */
    private List<Long> medicalPromiseIds;
    /**
     * 检测时间
     */
    private Date checkTime;

    /**
     * 报告状态，0.未出报告，1.已出报告
     */
    private Integer reportStatus;

    /**
     * 服务站Id
     */
    private String angelStationId;

    /**
     * 服务站名称
     */
    private String angelStationName;

    /**
     * 序号
     */
    private String serialNum;

    /**
     * 报告时间
     */
    private Date reportTime;
    /**
     * 检测状态
     */
    private Integer checkStatus;

    /**
     * 检测单特殊标记（0.普通订单，1.加项订单）
     */
    private String flag;

    /** 合管检测单ID */
    private Long mergeMedicalId;

    /**
     * 待收样超时绝对时间
     */
    private Date waitingTestTimeOutDate;

    /**
     * 是否待收样超时
     * 0 - 否 1 - 是
     */
    private Integer waitingTestTimeOutStatus;

    /**
     * 检测超时绝对时间
     */
    private Date testingTimeOutDate;

    /**
     * 是否检测超时
     * 0 - 否 1 - 是
     */
    private Integer testingTimeOutStatus;


    /**
     * 是否测试单 1是 0 null 不是
     */
    private Integer isTest;

    /**
     * 报告展示类型，1.结构化，2.PDF
     */
    @Builder.Default
    private Integer reportShowType = 1;

    /**
     * 检测单配送步骤
     */
    private String deliveryStepFlow;

    /**
     * 流转码
     */
    private String circulationCode;

    /**
     * 检测单的子状态
     */
    private Integer subStatus;

    /**
     * 异常记录信息
     */
    private String exceptionRecord;






    /**
     * 聚合所属领域编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.MED_PROMISE;
    }

    /**
     * 获取聚合编码
     *
     * @return {@link AggregateCode}
     */
    @Override
    public AggregateCode getAggregateCode() {
        return MedPromiseAggregateEnum.MED_PROMISE;
    }

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        version++;
    }

    /**
     * 获取标识符
     *
     * @return {@link}
     */
    @Override
    public MedicalPromiseIdentifier getIdentifier() {
        return new MedicalPromiseIdentifier(this.medicalPromiseId);
    }

    /**
     * 作废（作废的同时需要解冻）
     */
    public void invalid(){
        this.status = MedicalPromiseStatusEnum.INVALID.getStatus();
        if (Objects.equals(freeze, 1)){
            unFreeze();
        }
    }
    /**
     * 作废（作废的同时需要解冻）
     */
    public Boolean isFreeze(){
        return Objects.equals(freeze, YnStatusEnum.YES.getCode());
    }

    /**
     * 冻结
     */
    public void freeze(){
        this.setFreeze(1);
    }
    /**
     * 冻结
     */
    public void unFreeze(){
        this.setFreeze(0);
    }


    /**
     * 重置报告状态
     */
    public void resetReportStatus(){
        this.reportStatus = YnStatusEnum.NO.getCode();
    }

    /**
     * 重置报告状态
     */
    public void resetSpecimenCode(String freshCode){
        this.specimenCode = freshCode;
    }

    /**
     *
     * @return
     */
    public MedicalPromise copyInstance(){
        return JdhDispatchConvert.INSTANCE.copy(this);
    }

    /**
     *
     */
    @Mapper
    public interface JdhDispatchConvert{
        /** */
        JdhDispatchConvert INSTANCE = Mappers.getMapper(JdhDispatchConvert.class);
        /** */
        MedicalPromise copy(MedicalPromise dispatch);
    }
}
