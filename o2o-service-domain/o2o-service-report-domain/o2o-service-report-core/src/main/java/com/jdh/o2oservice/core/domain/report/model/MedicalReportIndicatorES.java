package com.jdh.o2oservice.core.domain.report.model;

import lombok.Data;

import java.util.Date;

/**
 * 报告指标实体
 * <AUTHOR>
 * @date 2024-11-13 13:32
 */
@Data
public class MedicalReportIndicatorES {

    /**
     * id
     */
    private Long id;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 实验室ID
     */
    private String stationId;

    /**
     * 报告id
     */
    private String reportId;

    /**
     * 检测单id
     */
    private Long medicalPromiseId;

    /**
     * 检测项目信息
     */
    private String serviceItemId;

    /**
     * 指标id
     */
    private Long indicatorId;

    /**
     * 指标检查结果
     */
    private String indicatorResult;

    /**
     * 指标异常标记类型，未知：-1，正常：0，低于正常值：1，高于正常值：2，异常：3
     */
    private Integer abnormalMarkType;

    /**
     * ct值
     */
    private String ctValue;

    /**
     * 用户省，京标
     */
    private Integer patientProvinceCode;

    /**
     * 用户市，京标
     */
    private Integer patientCityCode;

    /**
     * 用户区，京标
     */
    private Integer patientCountyCode;

    /**
     * 用户镇，京标
     */
    private Integer patientTownCode;

    /**
     * 到检时间
     */
    private Date checkTime;

    /**
     * 患者Id
     */
    private Long patientId;

    /**
     * 患者性别
     */
    private Integer patientGender;

    /**
     * 患者升级
     */
    private Date patientBirthday;

    /**
     * 是否有效
     */
    private Integer yn;

}
