package com.jdh.o2oservice.core.domain.report.model;

import lombok.Data;

import java.util.Date;

/**
 * MedicalReportIndicatorFlushCmd
 * <AUTHOR>
 * @date 2024-11-14 10:17
 */
@Data
public class MedicalReportIndicatorQueryPageBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 报告id
     */
    private String reportId;

    /**
     * 实验室ID
     */
    private String stationId;

    /**
     * 患者Id
     */
    private Long patientId;

    /**
     * 指标id
     */
    private Long indicatorId;

    /**
     * 创建时间
     */
    private Date createTimeStart;

    /**
     * 创建时间
     */
    private Date createTimeEnd;

    /**
     * 修改时间
     */
    private Date updateTimeStart;

    /**
     * 修改时间
     */
    private Date updateTimeEnd;

    /**
     * 是否忽略yn
     */
    private Boolean ignoreYn;

    /**
     * 主键
     */
    private Long minId;

    /**
     * 主键
     */
    private Long maxId;

    /** */
    private int pageNum = 1;


    /** */
    private int pageSize = 20;

}
