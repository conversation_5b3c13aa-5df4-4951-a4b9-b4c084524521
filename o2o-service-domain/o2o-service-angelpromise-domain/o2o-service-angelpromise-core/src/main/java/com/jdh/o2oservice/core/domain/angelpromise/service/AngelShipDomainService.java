package com.jdh.o2oservice.core.domain.angelpromise.service;

import com.jdh.o2oservice.common.enums.AngelDetailTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelRealTrackBo;
import com.jdh.o2oservice.core.domain.angelpromise.bo.UserAndStationPositionBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.*;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShipTrack;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.support.ship.param.CommonCheckPreCreateOrderBo;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryOrderDetailResponse;

import java.util.Map;

/**
 * @InterfaceName:AngelShipDomainService
 * @Description: 服务者运单消息处理
 *
 * @Author: yaoqinghai
 * @Date: 2024/4/21 22:55
 * @Vserion: 1.0
 **/
public interface AngelShipDomainService {

    /**
     * 接收服务者运单消息
     *
     * @param callBackContext
     * @return
     */
    boolean receiveAngelShipCallback(AngelShipCallBackContext callBackContext);

    /**
     * 创建运单
     *
     * @param shipCreateContext
     * @return
     */
    AngelShip createAngelShip(AngelWorkShipCreateContext shipCreateContext);

    /**
     * 自配送完成
     *
     * @return
     */
    boolean selfDeliveryFinish(DeliverContext deliverContext);

    /**
     * 取消运单
     *
     * @param cancelShipContext
     */
    boolean cancelShip(AngelWorkShipCancelContext cancelShipContext);

    /**
     * 查询骑手轨迹
     *
     * @param trackContext 骑手轨迹上下文
     */
    AngelShipTrack getTransferTrack(AngelWorkTransferTrackContext trackContext);

    /**
     * 转换运力供应商状态回传参数
     *
     * @param callbackMap
     * @param providerType
     * @return
     */
    AngelShipCallBackContext parseToShipCallbackContext(Map<String, Object> callbackMap, AngelDetailTypeEnum providerType, AngelShip angelShip);

    /**
     * 查询骑手实时轨迹
     *
     * @param angelShipTrackContext
     * @return
     */
    AngelRealTrackBo getTransferRealTrack(AngelShipTrackContext angelShipTrackContext);

    /**
     * 获取用户和实验室的经纬度
     *
     * @param angelShipTrackContext
     */
    UserAndStationPositionBo getUserAndStationPosition(AngelWork angelWork, AngelShipTrackContext angelShipTrackContext, AngelRealTrackBo angelRealTrackBo);

    /**
     * 查询运单订单详情
     *
     * @param detailContext
     * @return
     */
    DeliveryOrderDetailResponse getShipOrderDetail(AngelShipOrderDetailContext detailContext);

    /**
     * 根据work获取有效的运单信息
     * @param angelWork
     * @return
     */
    DeliveryOrderDetailResponse getShipOrderDetailByWork(Long shipId,AngelWork angelWork, JdhVerticalBusiness business);


    CommonCheckPreCreateOrderBo queryAvailableTime(QueryAvailableTimeContext queryAvailableTimeContext);

    /**
     * 保存运力经纬度
     * @param angelShipPositionContext
     * @return
     */
    Boolean savePosition(AngelShipPositionContext angelShipPositionContext);

    /**
     * 查询运力经纬度
     * @param angelShipPositionContext
     * @return
     */
    String getPosition(AngelShipPositionContext angelShipPositionContext);
}
