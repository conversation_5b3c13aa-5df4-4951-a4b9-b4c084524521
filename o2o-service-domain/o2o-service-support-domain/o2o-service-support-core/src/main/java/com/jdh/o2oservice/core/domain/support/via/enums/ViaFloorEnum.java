package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * 页面楼层枚举
 *
 * <AUTHOR>
 * @date 2024/01/17
 */
@Getter
public enum ViaFloorEnum {
    /**
     * 页面楼层 聚合页
     */
    GETHER_SUMMARY_INFO(ViaPageEnum.APPOINT_GETHER,"summaryInfo","预约信息概要"),
    GETHER_APPOINT_USER_INFO(ViaPageEnum.APPOINT_GETHER,"appointUserInfo","预约人信息"),
    GETHER_NOTICE_INFO(ViaPageEnum.APPOINT_GETHER,"noticeInfo","提示语信息"),
    GETHER_APPOINT_FORM_INFO(ViaPageEnum.APPOINT_GETHER,"appointFormInfo","预约人表单"),
    GETHER_APPOINT_INFO(ViaPageEnum.APPOINT_GETHER,"appointInfo","预约信息"),
    GETHER_APPOINT_OTHER_INFO(ViaPageEnum.APPOINT_GETHER,"appointOtherInfo","预约其他信息"),
    GETHER_KINDLY_TIP(ViaPageEnum.APPOINT_GETHER,"kindlyTip","温馨提示"),
    GETHER_FOOTER_BUTTONS(ViaPageEnum.APPOINT_GETHER,"footerButtons","吸底按钮"),

    /**
     * 页面楼层 详情页
     */
    DETAIL_APPOINT_STATUS_INFO(ViaPageEnum.APPOINT_DETAIL,"appointStatusInfo","预约状态"),
    DETAIL_PROMISE_CODE_INFO(ViaPageEnum.APPOINT_DETAIL,"promiseCodeInfo","消费码信息"),
    DETAIL_APPOINT_INFO(ViaPageEnum.APPOINT_DETAIL,"appointInfo","预约信息"),
    DETAIL_KINDLY_TIP(ViaPageEnum.APPOINT_DETAIL,"kindlyTip","温馨提示"),
    DETAIL_FOOTER_BUTTONS(ViaPageEnum.APPOINT_DETAIL,"footerButtons","吸底按钮"),



    /** 京麦楼层 */
    JM_APPOINT_PAGE_CONDITION(ViaPageEnum.JM_PROMISE_PAGE,"jmPromisePageCondition","京麦预约单列表筛选条件"),
    JM_APPOINT_PAGE_HEADER(ViaPageEnum.JM_PROMISE_PAGE,"jmPromisePageHeader","京麦预约单列表"),

    /** 到店小程序表头 */
    STORE_PROGRAM_APPOINT_PAGE_HEADER(ViaPageEnum.STORE_PROGRAM_PROMISE_PAGE,"storeProgramPromisePageHeader","到店小程序预约单表头"),

    /**
     * 到家项目履约详情（订详）楼层
     */
    //概要 summaryInfo
    PROMISE_SUMMARY_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"summaryInfo","概要信息"),
    //步骤条 stepGuideInfo
    PROMISE_STEP_GUIDE_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"stepGuideInfo","步骤条"),
    //履约信息 - 标题 titleInfo
    PROMISE_TITLE_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"promiseTitleInfo","履约信息 - 标题"),
    //履约信息 - 服务者 angelInfo
    PROMISE_ANGEL_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"promiseAngelInfo","履约信息 - 服务者"),
    //履约信息 - 消费码 codeInfo
    PROMISE_CODE_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"promiseCodeInfo","履约信息 - 消费码"),
    //履约信息 - 被服务者 patientInfo
    PROMISE_PATIENT_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"promisePatientInfo","履约信息 - 被服务者"),
    //样本信息 materialInfo
    PROMISE_MATERIAL_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"materialInfo","样本信息"),
    // 新样本信息 materialInfo（2025心智之战新增楼层信息）
    NEW_PROMISE_MATERIAL_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"newMaterialInfo","新样本信息", "materialInfoFloor"),
    //履约信息楼层 - 卡片按钮 promiseFooterButtons
    PROMISE_FLOOR_FOOTER_BUTTONS(ViaPageEnum.HOME_ORDER_DETAIL,"promiseFooterButtons","履约信息楼层 - 卡片按钮"),
    //采样教程 sampleCourseInfo
    PROMISE_SAMPLE_COURSE_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"sampleCourseInfo","采样教程"),
    PROMISE_MAP_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"promiseMapInfo","服务地图"),

    PROMISE_SHIP_INFO(ViaPageEnum.HOME_PROMISE_DETAIL,"shipInfo","物流信息"),
    PROMISE_NOTES(ViaPageEnum.HOME_PROMISE_DETAIL,"promiseNotes","注意事项"),
    ENTER_CODE_PATIENT(ViaPageEnum.HOME_PROMISE_DETAIL,"enterCodePatient","录入样本和检测人"),

    //履约信息 - 服务者及服务地图聚合楼层 promiseAngelInfoMergeMapInfo
    PROMISE_ANGEL_MERGE_MAP_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"promiseAngelInfoMergeMapInfo","履约信息 - 服务者及服务地图聚合楼层"),

    SERVICE_SCIENCE_KNOWLEDGE(ViaPageEnum.HOME_ORDER_DETAIL,"scienceKnowledge","科普知识"),
    //保险信息 insuranceInfo
    PROMISE_INSURANCE_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"insuranceInfo","保险信息"),
    //购买商品信息 orderSkuInfo
    PROMISE_ORDER_SKU_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"orderSkuInfo","订单商品信息"),
    //订单信息 orderInfo
    PROMISE_ORDER_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"orderInfo","订单信息"),
    //订单信息与购买商品信息聚合楼层 orderInfoMergeSkuInfo
    PROMISE_ORDER_MERGE_SKU_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"orderInfoMergeSkuInfo","订单信息"),
    //订单状态 orderStatus
    PROMISE_ORDER_STATUS(ViaPageEnum.HOME_ORDER_DETAIL,"orderStatus","订单状态"),
    //底部按钮 footerButtons
    PROMISE_FOOTER_BUTTONS(ViaPageEnum.HOME_ORDER_DETAIL,"footerButtons","底部按钮"),
    // 检测项目明细
    PROMISE_SPECIMEN_CODE(ViaPageEnum.HOME_ORDER_DETAIL,"promiseSpecimenCode","检测项目条码信息"),
    // 送检中
    PROMISE_UNDER_INSPECTION_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"promiseUnderInspectionInfo","送检中"),
    // 检测中
    PROMISE_LABORATORY_TESTING_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"promiseLaboratoryTestingInfo","检测中"),
    // 报告已生成
    PROMISE_VIEW_REPORT_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"promiseViewReportInfo","报告已生成"),
    // 用户反馈
    PROMISE_USER_FEEDBACK_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"userFeedback","用户反馈"),
    // nps问卷调查
    PROMISE_USER_SERVICE_SURVEY(ViaPageEnum.HOME_ORDER_DETAIL,"userServiceSurvey","nps问卷调查","userServiceSurveyFloor"),

     /** VTP楼层 */
    VTP_VOUCHER_LIST_INFO(ViaPageEnum.HOME_ORDER_DETAIL,"vtpVoucherListInfo","VTP卡券信息"),

    /**
     * VTP 预约聚合页
     */
    VTP_PROMISE_GATHER_ADDRESS_INFO(ViaPageEnum.APPOINT_GETHER,"appointmentAddressInfo","预约地址信息"),
    VTP_PROMISE_GATHER_COMMON_INFO(ViaPageEnum.APPOINT_GETHER,"appointmentCommonInfo","预约信息"),
    VTP_PROMISE_GATHER_REMARK_INFO(ViaPageEnum.APPOINT_GETHER,"appointmentRemarkInfo","地址信息"),
    VTP_PROMISE_GATHER_KINDLY_TIP(ViaPageEnum.APPOINT_GETHER,"kindlyTip","温馨提示"),
    VTP_PROMISE_GATHER_FOOTER_BUTTONS(ViaPageEnum.APPOINT_GETHER,"footerButtons","底部按钮")

    ;



    /**
     * ViaFloorEnum
     *
     * @param scene     场景
     * @param floorCode 楼层代码
     * @param floorName 楼层名称
     */
    ViaFloorEnum(ViaPageEnum scene, String floorCode, String floorName) {
        this.scene = scene;
        this.floorCode = floorCode;
        this.floorName = floorName;
    }

    ViaFloorEnum(ViaPageEnum scene, String floorCode, String floorName,String beanName) {
        this.scene = scene;
        this.floorCode = floorCode;
        this.floorName = floorName;
        this.beanName = beanName;
    }

    /**
     * 场景
     */
    private ViaPageEnum scene;

    /**
     * 楼层code
     */
    private String floorCode;

    /**
     * 楼层名称
     */
    private String floorName;
    /**
     * 执行类
     */
    private String beanName;

    public static ViaFloorEnum getByFloorCode(String floorCode){
        for (ViaFloorEnum viaFloorEnum :ViaFloorEnum.values()) {
            if(viaFloorEnum.getFloorCode().equals(floorCode)){
                return viaFloorEnum;
            }
        }
        return null;
    }

}
