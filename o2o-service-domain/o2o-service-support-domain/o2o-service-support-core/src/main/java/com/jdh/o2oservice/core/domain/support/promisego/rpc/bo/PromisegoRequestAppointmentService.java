package com.jdh.o2oservice.core.domain.support.promisego.rpc.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * promisego请求SKU
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromisegoRequestAppointmentService {

    /**
     * <pre>
     * 主站商品id
     * </pre>
     */
    private Long skuId;

    /**
     * <pre>
     * 商品类型；0为主品，1为加项品
     * </pre>
     */
    private Integer skuType;

    /**
     * <pre>
     * 服务类型
     * </pre>
     */
    private Integer serviceType;

    /**
     * <pre>
     * 已选择检测项目集合
     * </pre>
     */
    private List<PromisegoRequestAppointmentServiceItem> itemList;
}
