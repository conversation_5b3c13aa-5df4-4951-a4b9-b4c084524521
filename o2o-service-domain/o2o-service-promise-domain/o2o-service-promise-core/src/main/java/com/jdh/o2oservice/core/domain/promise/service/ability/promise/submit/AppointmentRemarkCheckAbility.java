package com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit;

import cn.hutool.core.util.StrUtil;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseSubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AppointmentRemarkCheckAbility implements SubmitAbility{

    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SubmitAbilityCode getAbilityCode() {
        return SubmitAbilityCode.APPOINTMENT_REMARK_CHECK;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(PromiseSubmitAbilityContext context) {
        JdhVerticalBusiness verticalBusiness = context.getVerticalBusiness();
        //到家 进行预约时间校验
        if(BusinessModeEnum.ANGEL_CARE.getCode().equals(verticalBusiness.getBusinessModeCode())
                || BusinessModeEnum.ANGEL_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                || BusinessModeEnum.SELF_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())){
            homeCheck(context);
        }
    }

    /**
     * 到家检测
     *
     * @param context 上下文
     */
    private void homeCheck(PromiseSubmitAbilityContext context) {
        String remark = context.getRemark();
        if(StrUtil.isNotBlank(remark)){
            if(remark.length() > NumConstant.NUM_200){
                throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_REMARK_LENGTH_ERROR);
            }
        }
    }
}
