package com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseSubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.rpc.PatientServiceRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.PatientInfoRpcBO;
import com.jdh.o2oservice.core.domain.support.basic.model.Birthday;
import com.jdh.o2oservice.core.domain.support.basic.model.CredentialNumber;
import com.jdh.o2oservice.core.domain.support.basic.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 解密用户信息能力，目前用户信息填写支持两种方式
 * 1、用户从常用预约人选择，此时提交的预约人信息是加星的，格式"159****1160"，我们需要根据patientId去健康档案获取完整的预约人信息；
 * 2、支持用户手填的信息，此时信息完全的加密的，我们需要解密数据并创建对应的常用预约人，并获取PID【目前健康档案一个pin下最多支持20个常用预约人】
 * @author: yangxiyu
 * @date: 2022/9/9 9:43 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class DecryptUserAbility implements SubmitAbility {

    /** 健康档案服务 */
    @Resource
    private PatientServiceRpc patientServiceRpc;
    /**
     *
     * @return
     */
    @Override
    public SubmitAbilityCode getAbilityCode() {
        return SubmitAbilityCode.DECRYPT_PHONE_ID_CARD;
    }
    @Override
    public void execute(PromiseSubmitAbilityContext context) {
        List<User> users = context.getUsers();

        String businessModeCode = context.getVerticalBusiness().getBusinessModeCode();
        if(BusinessModeEnum.ANGEL_CARE.getCode().equals(businessModeCode)
                || BusinessModeEnum.ANGEL_TEST.getCode().equals(businessModeCode)
                || BusinessModeEnum.SELF_TEST.getCode().equals(businessModeCode)){
            if(CollUtil.isEmpty(users)){
                throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_USER_MISS_ERROR);
            }
        }

        for (User user : users) {
            decrypt(user);
        }
    }

    public void decrypt(User user){
        AssertUtils.nonNull(user, "预约人信息为空");
        user.decrypt();
        if (Objects.nonNull(user.getPatientId())) {
            fillByPatient(user);
        }
    }

    /**
     * 1、如果身份证和证件号存在掩码，且PID不为空需要去健康档案取正确的值，否则不需要
     * @param user
     */
    private void fillByPatient(User user){
        // 存在掩码，查询健康档案
        //if(user.isMask()){
        if(needFillByPatient(user)){
            PatientInfoRpcBO patientInfo = patientServiceRpc.queryByPatientId(user.getUserPin(), user.getPatientId());
            if (Objects.nonNull(patientInfo)){

                if ( (Objects.nonNull(user.getPhoneNumber()) && user.getPhoneNumber().isMask())
                        || Objects.isNull(user.getPhoneNumber())
                        || StrUtil.isBlank(user.getPhoneNumber().getPhone())){
                    user.initPhoneNumber(patientInfo.getPhone());
                }
                if ((Objects.nonNull(user.getCredentialNum()) && user.getCredentialNum().isMask())
                        || Objects.isNull(user.getCredentialNum())
                        || StrUtil.isBlank(user.getCredentialNum().getCredentialNo())
                ){
                    user.setCredentialNum(new CredentialNumber(patientInfo.getIdCardType(), patientInfo.getIdCardNum()));
                }
                if ((Objects.nonNull(user.getUserName()) && user.getUserName().isMask())
                        || Objects.isNull(user.getUserName())
                        || StrUtil.isBlank(user.getUserName().getName())
                ){
                    user.initUserName(patientInfo.getPatientName());
                }
                if(Objects.isNull(user.getVerifyStatus())){
                    user.setVerifyStatus(patientInfo.getVerifyStatus());
                }
                if(Objects.isNull(user.getGender())){
                    user.setGender(patientInfo.getSex());
                }
                if(Objects.isNull(user.getBirthday()) || StrUtil.isBlank(user.getBirthday().getBirth())){
                    user.initBirthday(patientInfo.getBirthday());
                }
            }
        }
    }

    /**
     * 需要根据pid反查患者
     *
     * @param user 用户
     * @return {@link Boolean}
     */
    private Boolean needFillByPatient(User user){
        if(user.isMask()){
            return Boolean.TRUE;
        }
        //如果手机号、证件号、姓名都为空，也进行反查补充
        if((Objects.isNull(user.getPhoneNumber()) || StrUtil.isBlank(user.getPhoneNumber().getPhone()))
                && (Objects.isNull(user.getCredentialNum()) || StrUtil.isBlank(user.getCredentialNum().getCredentialNo()))
                && (Objects.isNull(user.getUserName()) || StrUtil.isBlank(user.getUserName().getName()))
                && (Objects.isNull(user.getBirthday()) || StrUtil.isBlank(user.getBirthday().getBirth()))
                && (Objects.isNull(user.getVerifyStatus()))
        ){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
