package com.jdh.o2oservice.core.domain.product.model;

import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.product.enums.ProductAggregateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 京东健康商品扩展主数据
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhSku implements Aggregate<JdhSkuIdentifier> {
    
    /**
     * <pre>
     * 主站商品id
     * </pre>
     */
    private Long skuId;
    
    /**
     * <pre>
     * 售卖状态 0-不可售卖 1-可售卖,仅控制是否可下单,非上下架
     * </pre>
     */
    private Integer saleStatus;
    
    /**
     * <pre>
     * 服务类型
     * </pre>
     */
    private Integer serviceType;
    
    /**
     * <pre>
     * 已选择检测项目id集合
     * </pre>
     */
    private List<Long> serviceItemIdList;
    
    /**
     * <pre>
     * 服务时长,单位分钟
     * </pre>
     */
    private Integer serviceDuration;
    
    /**
     * <pre>
     * 服务资源类型集合 1-骑手 2-护士 3-护工 4-康复师,多个值
     * </pre>
     */
    private String serviceResourceType;
    
    /**
     * <pre>
     * 需提前预约时间,单位小时
     * </pre>
     */
    private Integer advanceAppointTime;
    
    /**
     * <pre>
     * 未来可约天数,单位天
     * </pre>
     */
    private Integer maxScheduleDays;
    
    /**
     * <pre>
     * 每天可预约时间段,["08:00-12:00", "14:00-18:00"]
     * </pre>
     */
    private String dayTimeFrame;
    
    /**
     * <pre>
     * 实验室分发规则 1-整单 2-时效 3-成本,多个值
     * </pre>
     */
    private String stationAssignType;
    
    /**
     * <pre>
     * 是否需要投保 0-不需要 1-需要
     * </pre>
     */
    private Integer requiredInsure;
    
    /**
     * <pre>
     * 商品标签,多个
     * </pre>
     */
    private String tags;
    
    /**
     * <pre>
     * 服务须知,JSON对象集合
     * </pre>
     */
    private String serviceNotice;
    
    /**
     * <pre>
     * 服务流程图
     * </pre>
     */
    private String serviceProcessImg;
    
    /**
     * <pre>
     * 是否实名 0-否 1-是
     * </pre>
     */
    private Integer requiredRealName;
    
    /**
     * <pre>
     * 使用教程地址
     * </pre>
     */
    private String tutorialUrl;
    
    /**
     * <pre>
     * 知情同意书地址
     * </pre>
     */
    private String informedConsentUrl;
    
    /**
     * <pre>
     * 预约模板id
     * </pre>
     */
    private Integer appointTemplateId;
    
    /**
     * <pre>
     * 年龄范围,最小最大值逗号隔开 0,150
     * </pre>
     */
    private String ageRange;
    
    /**
     * <pre>
     * 适用性别，用户性别 1-男 2-女
     * </pre>
     */
    private String genderLimit;
    
    /**
     * <pre>
     * 客户确认信息类型,1-医嘱证明
     * </pre>
     */
    private String customerConfirmType;
    
    /**
     * <pre>
     * 服务记录类型,1-废料处理 2-服务记录 3-上传着装照片
     * </pre>
     */
    private String serviceRecordType;
    
    /**
     * <pre>
     * 服务资源结算价
     * </pre>
     */
    private BigDecimal resourceSettlementPrice;
    
    /**
     * <pre>
     * 渠道id
     * </pre>
     */
    private Long channelId;
    
    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;
    
    /**
     * <pre>
     * 版本号
     * </pre>
     */
    private Integer version;
    
    /**
     * <pre>
     * 是否有效 0：无效；1：有效
     * </pre>
     */
    private Integer yn;
    
    /**
     * <pre>
     * 创建人pin
     * </pre>
     */
    private String createUser;
    
    /**
     * <pre>
     * 创建时间
     * </pre>
     */
    private Date createTime;
    
    /**
     * <pre>
     * 修改人pin
     * </pre>
     */
    private String updateUser;
    
    /**
     * <pre>
     * 更新时间
     * </pre>
     */
    private Date updateTime;
    
    /**
     * <pre>
     * 商品关联项目清单类型 1-项目（单项目） 2-服务（多项目套餐） 3-服务组（多服务套餐）
     * </pre>
     */
    private Integer skuItemType;
    
    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageNum = 1;
    
    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageSize = 10;

    /**
     * <pre>
     * 草稿
     * </pre>
     */
    private Boolean draft;

    /**
     * <pre>
     * 商品类型；0为主品，1为加项品
     * </pre>
     */
    private Integer skuType;

    /**
     * <pre>
     * 技术难度 0-1000
     * </pre>
     */
    private Integer technicalLevel;

    /**
     * <pre>
     * 购买后服务有效时间,单位天
     * </pre>
     */
    private Integer buyValidPeriod;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * <pre>
     * 主站商品id
     * </pre>
     */
    private List<Long> skuIdList;

    /**
     * <pre>
     * 采样教程轮播图fileId集合，格式：[fileId,fieldId]
     * </pre>
     */
    private String tutorialCarousel;

    /**
     * 采样教程视频fileId
     */
    private String tutorialVideo;

    /**
     * 采样教程视频封面图fileId
     */
    private String tutorialVideoThumbnail;

    /**
     * 采样方法说明 fileId
     */
    private String tutorialMethod;

    /**
     * 采样方法说明跳转url
     */
    private String tutorialMethodJumpUrl;

    /**
     * 活动楼层
     */
    private List<ProductActivityFloor> activityFloors;

    /**
     * <pre>
     * 高优分配实验室
     * </pre>
     */
    private List<String> highQualityStoreId;

    /**
     * 采样教程名称
     */
    private String tutorialName;

    /**
     * 聚合所属领域编码
     *
     * @return {@link DomainCode}
     */
    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.PRODUCT;
    }
    
    /**
     * 获取聚合编码
     *
     * @return {@link AggregateCode}
     */
    @Override
    public AggregateCode getAggregateCode() {
        return ProductAggregateEnum.PRODUCT_SKU;
    }
    
    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return version;
    }
    
    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        version++;
    }
    
    /**
     * 获取标识符
     *
     * @return id
     */
    @Override
    public JdhSkuIdentifier getIdentifier() {
        return new JdhSkuIdentifier(this.skuId);
    }
}
