package com.jdh.o2oservice.core.domain.trade.enums;

import com.jd.medicine.base.common.util.StringUtil;

import java.util.Objects;

/**
 * OrderExtTypeEnum
 * @author: yaoqinghai
 * @date: 2024/01/22 11:13
 * @version: 1.0
 */
public enum ServiceHomeTypeEnum {
    /**
     * 到家商品分类
     */
    XFYL_HOME_TEST_PHASE1("10", "xfylHomeSelfTest1Phase", "test"),
    XFYL_HOME_TEST_PHASE2("11", "xfylHomeSelfTest1Phase", "test"),
    XFYL_HOME_TEST1("20", "xfylHomeTest", "test"),
    XFYL_HOME_TEST2("21", "xfylHomeTest", "test"),
    XFYL_HOME_CARE1("30", "xfylHomeCare", "care"),
    XFYL_HOME_CARE2("31", "xfylHomeCare", "care"),
    XFYL_HOME_CARE3("50", "xfylHomeCare", "care"),
    XFYL_HOME_CARE4("51", "xfylHomeCare", "care"),


    NH_HOME_TEST_PHASE1("110", "nhHomeSelfTest1Phase","test"),
    NH_HOME_TEST_PHASE2("111", "nhHomeSelfTest1Phase","test"),
    NH_HOME_TEST1("120", "nhHomeTest", "test"),
    NH_HOME_TEST2("121", "nhHomeTest", "test"),
    NH_HOME_CARE1("130", "nhHomeCare", "care"),
    NH_HOME_CARE2("131", "nhHomeCare", "care"),
    NH_HOME_CARE3("150", "nhHomeCare", "care"),
    NH_HOME_CARE4("151", "nhHomeCare", "care"),

    /**
     * VTP
     */
    VTP_HOME_TEST_PHASE("01", "xfylVtpHomeTestPhase", "test"),
    VTP_HOME_TEST("02", "xfylVtpHomeTest", "test"),
    VTP_HOME_CARE("03", "xfylVtpHomeCare", "care"),
    VTP_HOME1_CARE("05", "xfylVtpHomeCare", "care"),


    /**
     *  精准营养code ************ 无实际含义，
     */
    JZYY_HOME_TEST("************", "jzyyHomeTest", "test"),



    XFYL_HOME_TEST_PHASE1_BACKUP("010", "xfylHomeSelfTest1Phase", "test"),
    XFYL_HOME_TEST1_BACKUP("020", "xfylHomeTest", "test"),
    XFYL_HOME_CARE1_BACKUP("030", "xfylHomeCare", "care"),
    XFYL_HOME_CARE2_BACKUP("050", "xfylHomeCare", "care"),

    /**
     * 快递检测
     */
    XFYL_HOME_SELF_TEST_TRANSPORT("40", "xfylHomeSelfTestTransport", "test"),


    /**
     * 院内自费导诊解决方案A
     */
    HOSPITAL_PAID_GUIDANCE_A_HOME_TEST("3020", "hospitalPaidGuidanceAHomeTest", "test"),
    HOSPITAL_PAID_GUIDANCE_A_HOME_TEST1("3021", "hospitalPaidGuidanceAHomeTest", "test")
    ;
    /**
     *
     */
    private String code;
    /**
     *
     */
    private String verticalCode;
    /**
     *
     */
    private String serviceType;
    /**
     *
     */
    private Integer itemType;

    ServiceHomeTypeEnum(String code, String verticalCode,String serviceType) {
        this.code = code;
        this.verticalCode = verticalCode;
        this.serviceType = serviceType;
    }

    /**
     *
     * @param code
     * @return
     */
    public static ServiceHomeTypeEnum getServiceHomeTypeEnum(String code){
        if(StringUtil.isBlank(code)){
            return null;
        }
        for (ServiceHomeTypeEnum value : ServiceHomeTypeEnum.values()) {
            if(value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getVerticalCode() {
        return verticalCode;
    }

    public void setVerticalCode(String verticalCode) {
        this.verticalCode = verticalCode;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }
}
