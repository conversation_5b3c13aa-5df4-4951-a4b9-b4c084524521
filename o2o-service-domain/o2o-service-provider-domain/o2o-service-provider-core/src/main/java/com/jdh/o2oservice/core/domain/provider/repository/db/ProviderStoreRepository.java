package com.jdh.o2oservice.core.domain.provider.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.provider.model.ProviderStore;
import com.jdh.o2oservice.core.domain.provider.model.ProviderStoreIdentifier;

import java.util.List;

/**
 * 商家门店数据仓
 *
 * <AUTHOR>
 * @date 2024/04/25
 */
public interface ProviderStoreRepository extends Repository<ProviderStore, ProviderStoreIdentifier> {

    /**
     * 保存门店项目关系
     *
     * @param rel
     * @return
     */
    int saveStationServiceItem(JdhStationServiceItemRel rel);

    /**
     * 更新门店项目关系
     *
     * @param rel
     * @return
     */
    int updateStationServiceItem(JdhStationServiceItemRel rel);

    int updateEquipmentId(JdhStationServiceItemRel rel);

    int updateContentId(JdhStationServiceItemRel rel);

    /**
     * 删除门店项目关系
     *
     * @param rel
     * @return
     */
    int deleteStationServiceItem(JdhStationServiceItemRel rel);


    /**
     * 查询门店项目关系
     *
     * @param jdhStationServiceItemRel jdhStationServiceItemRel
     * @return
     */
    List<JdhStationServiceItemRel> queryStationServiceItemList(JdhStationServiceItemRel jdhStationServiceItemRel);


    /**
     * 查询门店项目关系
     *
     * @param jdhStationServiceItemRel jdhStationServiceItemRel
     * @return
     */
    List<JdhStationServiceItemRel> queryStationServiceItemList(List<JdhStationServiceItemRel> jdhStationServiceItemRel);

    /**
     * 分页查询门店项目关系
     *
     */
    Page<JdhStationServiceItemRel> queryStationServiceItemListPage(JdhStationServiceItemRel jdhStationServiceItemRel);

    /**
     * 查询门店项目关系
     *
     */
    JdhStationServiceItemRel queryStationServiceItem(JdhStationServiceItemRel jdhStationServiceItemRel);


    /**
     * 查询项目在同一个门店数据
     *
     * @param jdhStationServiceItemRel jdhStationServiceItemRel
     * @return
     */
    List<JdhStationServiceItemRel> queryStationListForServiceItemAllInOneStore(List<JdhStationServiceItemRel> jdhStationServiceItemRel);

    /**
     * 内容管理查询门店项目关系
     *
     * @param jdhStationServiceItemRel
     */
    Page<JdhStationServiceItemRel> queryStationServiceItemByContentPage(JdhStationServiceItemRel jdhStationServiceItemRel);
    /**
     * 通过设备ID查询门店项目关系
     *
     * @param jdhStationServiceItemRel
     */
    List<JdhStationServiceItemRel> queryStationServiceItemByEquipmentIdOrContentId(JdhStationServiceItemRel jdhStationServiceItemRel);
}