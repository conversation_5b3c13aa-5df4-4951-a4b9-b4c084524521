package com.jdh.o2oservice.core.domain.provider.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

/**
 * <pre>
 *  门店项目关系表
 * </pre>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhStationServiceItemRel {

    private int pageNum = 1;

    private int pageSize = 10;

    /**
     * <pre>
     * 主键
     * </pre>
     */
    private Long id;
    
    /**
     * <pre>
     * 京东门店id
     * </pre>
     */
    private String stationId;

    /**
     * <pre>
     * 京东门店名称
     * </pre>
     */
    private String stationName;

    /**
     * 门店所在省份
     */
    private Integer provinceId;

    /**
     * 门店所在城市
     */
    private Integer cityId;
    
    /**
     * <pre>
     * 项目id
     * </pre>
     */
    private Long serviceItemId;

    /**
     * <pre>
     * 项目名
     * </pre>
     */
    private String serviceItemName;

    /**
     * <pre>
     * 项目英文名
     * </pre>
     */
    private String serviceItemNameEn;
    
    /**
     * <pre>
     * 实验室结算价格,单位:元
     * </pre>
     */
    private BigDecimal settlementPrice;
    
    /**
     * <pre>
     * 耗材包id
     * </pre>
     */
    private Long materialPackageId;

    /**
     * <pre>
     * 耗材包id
     * </pre>
     */
    private String materialPackageName;
    
    /**
     * <pre>
     * 采样方式
     * </pre>
     */
    private String specimenWay;
    
    /**
     * <pre>
     * 样本类型 1鼻咽拭子采样、2唾液、3痰液、4粪便、5肛周拭子、6尿液、7指尖血、8干血斑、9阴道/宫颈采样拭子、10C13吹气袋（幽门螺旋杆菌检测）、11头发（带毛囊）、12静脉血
     * </pre>
     */
    private Integer specimenType;
    
    /**
     * <pre>
     * 检测方法学
     * </pre>
     */
    private Integer testWay;
    
    /**
     * <pre>
     * 样本保存时长,单位小时
     * </pre>
     */
    private Integer specimenPreserveDuration;
    
    /**
     * <pre>
     * 样本量,数值及单位
     * </pre>
     */
    private String specimenNum;
    
    /**
     * <pre>
     * 服务时长,单位分钟
     * </pre>
     */
    private Integer serviceDuration;
    
    /**
     * <pre>
     * 样本存放要求
     * </pre>
     */
    private String specimenPreserveCondition;
    
    /**
     * <pre>
     * 检测时长,单位分钟
     * </pre>
     */
    private Integer testDuration;
    
    /**
     * <pre>
     * 服务要求
     * </pre>
     */
    private String serviceCondition;

    /**
     * 上下架状态
     */
    private Integer onOffShelf;

    /**
     * 设备业务序列id
     */
    private Long equipmentBizId;

    /**
     * 厂商id
     */
    private Long manufacturerId;

    /**
     * 设备型号
     */
    private String equipmentModel;

    /**
     * 内容业务序列id
     */
    private Long contentBizId;

    /**
     * 内容名称
     */
    private String contentName;
    
    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;
    
    /**
     * <pre>
     * 版本号
     * </pre>
     */
    private Integer version;
    
    /**
     * <pre>
     * 是否有效 0-无效 1-有效
     * </pre>
     */
    private Integer yn;
    
    /**
     * <pre>
     * 创建人
     * </pre>
     */
    private String createUser;
    
    /**
     * <pre>
     * 更新人
     * </pre>
     */
    private String updateUser;
    
    /**
     * <pre>
     * 创建时间
     * </pre>
     */
    private Date createTime;
    
    /**
     * <pre>
     * 更新时间
     * </pre>
     */
    private Date updateTime;

    /**
     * <pre>
     * 项目id集合
     * </pre>
     */
    private Set<Long> serviceItemIds;

    /**
     * 渠道编号
     */
    private Long channelNo;

    /**
     * 服务门店状态（是否可用）
     */
    private Integer stationStatus;

    /**
     * 门店地址
     */
    private String stationAddr;

    /**
     * 经度
     */
    private String stationLng;

    /**
     * 纬度
     */
    private String stationLat;

    /**
     * 门店营业时间
     */
    private String stationHours;

    /**
     * 门店电话,可能多个
     */
    private String storePhone;

    /**
     * 爆单开关
     */
    private Integer limitBuyStatus;

    /**
     * 天算渠道ID
     */
    private String channelRuleCode;

    /**
     * <pre>
     * 京东门店idList
     * </pre>
     */
    private Set<String> stationIdSet;

    /**
     * 查询忽略上下架
     */
    private Boolean queryIgnoreOnOffShelf;
}