package com.jdh.o2oservice.core.domain.provider.rpc.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 快检推送信息入参
 * @Date 2025/2/7 下午08:29
 * <AUTHOR>
 **/
@Data
public class QuickCheckPushInfoBO implements Serializable {

    /**
     * 检测单id
     */
    private String sampleId;

    /**
     * 样本条码
     */
    private String sampleBarcode;

    /**
     * 预约人
     */
    private String userName;

    /**
     * 预约人电话（如需走申请）
     */
    private String userPhone;

    /**
     * 性别 1男 2女
     */
    private Integer userGender;

    /**
     * 预约人年龄
     */
    private Integer userAge;

    /**
     * 服务项目列表
     */
    private String serviceItemName;

    /**
     * 服务项目的唯一标识符。
     */
    private String serviceItemId;

    /**
     * 商家门店ID
     */
    private String storeId;

    /**
     * 商家门店名称 例：北京实验室
     */
    private String storeName;

    /**
     * 用户预约采样上门时间 日期格式yyyy-MM-dd HH:mm:ss
     */
    private String appointmentStartTime;

    /**
     * 婚否1未婚 2已婚
     */
    private Integer userMarriage;

    /**
     * 出生日期,格式yyyy-MM-dd
     */
    private String userBirth;

    /**
     * 检测单状态
     * 枚举：
     * 待送检员接单:0
     * 待送检员取货:2
     * 送检员配送中:3
     * 送检员已送达:4
     * 检测中:5
     * 已出报告:6
     * 已作废:8
     */
    private Integer status;

    /**
     * 预计送达时间,日期格式yyyy-MM-dd HH:mm:ss
     */
    private Date deliverEtaTime;

    /**
     * 配送员类型
     * 1-采样员自送 2-达达 3-闪送 4-顺丰
     */
    private Integer deliverType;

    /**
     * 取件码
     */
    private String pickUpCode;

    /**
     * 骑手姓名
     */
    private String senderName;

    /**
     * 骑手电话
     */
    private String senderPhone;

    /**
     * 要求最晚收样时间，日期格式yyyy-MM-dd HH:mm:ss
     */
    private Date requireCollectSampleTime;

    /**
     * 要求最晚推送报告时间，日期格式yyyy-MM-dd HH:mm:ss
     */
    private Date requirePushReportTime;

    /**
     * 版本号，数据更新以最新版本号为准
     */
    private Integer version;

    /**
     * 耗材名称
     */
    private String consumableName;

    /**
     * 采样人
     */
    private String samplingTechnician;

    /**
     * 采样时间
     */
    private Date samplingTime;

    /**
     * 开单人
     */
    private String orderingPhysician;

    /**
     * 开单时间
     */
    private Date orderingTime;

    /**
     * 1 - 京东平台
     * 2 - 院方开具
     */
    private Integer orderSource;

    /**
     * 证件类型
     */
    private Integer credentialType;

    /**
     * 证件号
     */
    private Integer credentialNo;
}
