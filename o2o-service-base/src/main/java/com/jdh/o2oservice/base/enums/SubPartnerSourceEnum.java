package com.jdh.o2oservice.base.enums;

/**
 * PartnerSourceEnum 合作者来源
 * <AUTHOR>
 * @version 2024/4/28 17:20
 **/
public enum SubPartnerSourceEnum {
    /**
     * 合作者来源
     */
    OUT_HOSPITAL_PAID_GUIDANCE_SHANGHAI_CHILD_CENTER(PartnerSourceEnum.OUT_HOSPITAL_PAID_GUIDANCE_A, 1, "上海儿中心"),
    ;

    private PartnerSourceEnum partnerSourceEnum;
    /**
     * 子渠道码
     */
    private Integer subCode;
    /**
     * 子渠道名称
     */
    private String subDesc;

    SubPartnerSourceEnum(PartnerSourceEnum partnerSourceEnum, Integer subCode, String subDesc) {
        this.partnerSourceEnum = partnerSourceEnum;
        this.subCode = subCode;
        this.subDesc = subDesc;
    }

    /**
     * @param subCode
     * @return
     */
    public static SubPartnerSourceEnum fromSubCode(Integer subCode) {
        if (null == subCode) {
            return null;
        }
        for (SubPartnerSourceEnum subPartnerSourceEnum : SubPartnerSourceEnum.values()) {
            if (subPartnerSourceEnum.getSubCode().equals(subCode)) {
                return subPartnerSourceEnum;
            }
        }
        return null;
    }
    public PartnerSourceEnum getPartnerSourceEnum() {
        return partnerSourceEnum;
    }

    public Integer getSubCode() {
        return subCode;
    }

    public String getSubDesc() {
        return subDesc;
    }
}
