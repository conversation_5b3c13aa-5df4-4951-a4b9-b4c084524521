package com.jdh.o2oservice.application.ztools.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Sets;
import com.jd.fastjson.JSON;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.ztools.MedicalPromiseToolsApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.ErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipCancelCodeStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.export.angelpromise.cmd.AngelWorkReCallShipCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.DirectCallMedicalPromiseSubmitCmd;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.provider.query.StoreInfoRequest;
import com.jdh.o2oservice.export.ztools.cmd.TargetStationCmd;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelShipPoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: 检测单tools
 * @Author: wangpengfei144
 * @Date: 2024/9/11
 */
@Service
@Slf4j
public class MedicalPromiseToolsApplicationImpl implements MedicalPromiseToolsApplication {

    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;
    /**
     * providerStoreApplication
     */
    @Autowired
    private ProviderStoreApplication providerStoreApplication;

    @Resource
    private RedisLockUtil redisLockUtil;

    @Resource
    MedicalPromiseApplication medicalPromiseApplication;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    AngelWorkApplication angelWorkApplication;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelShipRepository angelShipRepository;
    @Autowired
    private DuccConfig duccConfig;
    @Resource
    private JdhAngelShipPoMapper jdhAngelShipPoMapper;

    /**
     * 指定派发实验室
     *
     * @param targetStationCmd
     * @return
     */
    @Override
    public Boolean targetStation(TargetStationCmd targetStationCmd) {
        AssertUtils.nonNull(targetStationCmd, "请求参数错误");
        AssertUtils.nonNull(targetStationCmd.getAngelStationId(), "服务站id不能为空");
        AssertUtils.nonNull(targetStationCmd.getStationId(), "实验室id不能为空");
        AssertUtils.nonNull(targetStationCmd.getMedicalPromiseId(), "检测单id不能为空");

        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.JD_BATCH_SYNC_SPECIMEN_STATION_LOCK_KEY, targetStationCmd.getMedicalPromiseId());
        boolean lock = redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.JD_BATCH_SYNC_SPECIMEN_STATION_LOCK_KEY.getExpireTime(), RedisKeyEnum.JD_BATCH_SYNC_SPECIMEN_STATION_LOCK_KEY.getExpireTimeUnit());
        if(!lock) {
            log.error("[MedicalPromiseToolsApplicationImpl -> targetStation],任务正在执行!targetStation={}", JSON.toJSONString(targetStationCmd));
            return Boolean.FALSE;
        }
        try{
            MedicalPromise existMP = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(targetStationCmd.getMedicalPromiseId()).build());
            StoreInfoRequest storeInfoRequest = new StoreInfoRequest();
            storeInfoRequest.setStationId(targetStationCmd.getStationId());
            StoreInfoDto storeInfoDto = providerStoreApplication.queryStationInfo(storeInfoRequest);
            //如果没有服务站信息
            if (existMP.getStatus() >= 4 || existMP.getReportStatus() == 1){
                throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATUS_NOT_ALLOW_OPERATION);
            }
            MedicalPromise medicalPromise = new MedicalPromise();
            medicalPromise.setId(existMP.getId());
            medicalPromise.setMedicalPromiseId(existMP.getMedicalPromiseId());
            medicalPromise.setProviderId(storeInfoDto.getProviderId());
            medicalPromise.setStationId(storeInfoDto.getStationId());
            medicalPromise.setStationName(storeInfoDto.getStoreName());
            medicalPromise.setStationAddress(storeInfoDto.getStationAddress());
            medicalPromise.setStationPhone(storeInfoDto.getStationPhone());
            medicalPromise.setAngelStationId(String.valueOf(targetStationCmd.getAngelStationId()));
            medicalPromise.setVersion(existMP.getVersion());
            int count = medicalPromiseRepository.save(medicalPromise);
            if (count > 0){
                // 发送指派实验室事件
                eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_TARGET_STATION,
                        MedicalPromiseEventBody.builder()
                                .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                                .beforeStatus(existMP.getStatus())
                                .status(existMP.getStatus())
                                .specimenCode(existMP.getSpecimenCode())
                                .verticalCode(existMP.getVerticalCode())
                                .serviceType(existMP.getServiceType())
                                .version(existMP.getVersion())
                                .beforeStationId(existMP.getStationId())
                                .stationId(storeInfoDto.getStationId())
                                .build()));
                ErrorCode errorMsg = null;
                // 呼叫运力
                try {
                    AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
                    angelWorkDBQuery.setPromiseId(existMP.getPromiseId());
                    angelWorkDBQuery.setStatusList(AngelWorkStatusEnum.getValidStatus());
                    List<AngelWork> angelWorkSnapShotList = angelWorkRepository.findList(angelWorkDBQuery);
                    if (CollUtil.isNotEmpty(angelWorkSnapShotList)) {
                        for (AngelWork angelWorkSnapShot : angelWorkSnapShotList) {
                            // 已接单不再干预运力
                            AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
                            angelShipDBQuery.setWorkId(angelWorkSnapShot.getWorkId());
                            List<AngelShip> ships = angelShipRepository.findList(angelShipDBQuery);
                            refreshShipReceiveId(ships, storeInfoDto);
                            Set<Integer> accessStatusList = Sets.newHashSet(AngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus(), AngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus(), AngelShipStatusEnum.ORDER_SHIP_CANCEL.getShipStatus());
                            if (CollUtil.isNotEmpty(ships) && ships.stream().map(AngelShip::getShipStatus).filter(Objects::nonNull).collect(Collectors.toSet()).stream().anyMatch(ele -> !accessStatusList.contains(ele))) {
                                log.info("已接单不再干预运力, promiseId={}", existMP.getPromiseId());
                                continue;
                            }

                            AngelWorkReCallShipCmd angelWorkCancelShipCmd = new AngelWorkReCallShipCmd();
                            angelWorkCancelShipCmd.setWorkId(String.valueOf(angelWorkSnapShot.getWorkId()));
                            angelWorkCancelShipCmd.setWorkType(angelWorkSnapShot.getWorkType());
                            angelWorkCancelShipCmd.setOperateSource(CommonConstant.ONE);
                            angelWorkApplication.reCallShipFormWork(angelWorkCancelShipCmd, AngelShipCancelCodeStatusEnum.TARGET_STATION);
                        }
                    }
                } catch (Exception e) {
                    if (e instanceof BusinessException) {
                        errorMsg = ((BusinessException) e).getErrorCode();
                    } else {
                        errorMsg = new DynamicErrorCode(MedPromiseErrorCode.MEDICAL_PROMISE_SUBMIT_ERROR.getCode(),"重新呼叫运力失败");
                    }
                    log.error("重新呼叫运力失败", e);
                }
                // API调用三方
                try {
                    if (!duccConfig.getQuickCheckStationIdWhiteList().contains(medicalPromise.getStationId())){
                        Boolean callRet = medicalPromiseApplication.directCallMedicalPromiseToStation(DirectCallMedicalPromiseSubmitCmd.builder()
                                .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                                .directCall(YnStatusEnum.YES.getCode()).build());
                        log.info("调用三方接口结果,callRet={}", callRet);
                    }
                } catch (Exception e) {
                    if (e instanceof BusinessException) {
                        errorMsg = ((BusinessException) e).getErrorCode();
                    } else {
                        errorMsg = new DynamicErrorCode(MedPromiseErrorCode.MEDICAL_PROMISE_SUBMIT_ERROR.getCode(),"调用三方接口失败");
                    }
                    log.error("调用三方接口失败", e);
                }
                if (errorMsg != null) {
                    throw new BusinessException(errorMsg);
                }
            } else {
                throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_NULL);
            }
            return Boolean.TRUE;
        }finally {
            if(lock) {
                redisLockUtil.unLock(lockKey);
            }
        }
    }

    /**
     * 如果是自配送的运单，重派实验室，需要修改ReceiveId
     * @param ships
     */
    public void refreshShipReceiveId(List<AngelShip> ships, StoreInfoDto storeInfoDto){
        if (CollectionUtils.isEmpty(ships) || Objects.isNull(storeInfoDto)){
            return;
        }
        ships.forEach(e -> {
            if (Objects.equals(e.getType(), DeliveryTypeEnum.SELF_DELIVERY.getType()) && !StringUtils.equals(storeInfoDto.getStationId(), e.getReceiverId())){
                e.setReceiverId(storeInfoDto.getStationId());
                angelShipRepository.updateByShipId(e);
            }
        });
    }

}
