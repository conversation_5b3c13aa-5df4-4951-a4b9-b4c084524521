package com.jdh.o2oservice.application.provider.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.health.xfyl.merchant.export.dto.store.StoreInfoDTO;
import com.jd.health.xfyl.merchant.export.param.supplier.store.StoreInfoQueryParam;
import com.jdh.o2oservice.application.provider.convert.ProviderContentApplicationConverter;
import com.jdh.o2oservice.application.provider.convert.ProviderStoreApplicationConverter;
import com.jdh.o2oservice.application.provider.service.ProviderContentApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.product.context.ServiceItemQueryContext;
import com.jdh.o2oservice.core.domain.product.model.JdhMaterialPackage;
import com.jdh.o2oservice.core.domain.product.model.ServiceItem;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhMaterialPackageRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemRepository;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.enums.ProviderErrorCode;
import com.jdh.o2oservice.core.domain.provider.model.*;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderContentRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderEquipmentRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderStoreRepository;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.export.provider.cmd.JdhStationContentCreateCmd;
import com.jdh.o2oservice.export.provider.cmd.JdhStationContentDeleteCmd;
import com.jdh.o2oservice.export.provider.cmd.JdhStationContentItemRelCreateCmd;
import com.jdh.o2oservice.export.provider.cmd.JdhStationContentUpdateCmd;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.ProviderContentDto;
import com.jdh.o2oservice.export.provider.dto.ProviderContentStageDto;
import com.jdh.o2oservice.export.provider.query.JdhStationContentRequest;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 实验室内容管理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ProviderContentApplicationImpl implements ProviderContentApplication {

    /**
     * 门店仓储
     */
    @Resource
    ProviderContentRepository providerContentRepository;

    @Resource
    ProviderEquipmentRepository providerEquipmentRepository;

    @Resource
    ProviderStoreRepository providerStoreRepository;

    @Resource
    private GenerateIdFactory generateIdFactory;

    @Autowired
    private JdhServiceItemRepository jdhServiceItemRepository;

    @Autowired
    private ProviderStoreExportServiceRpc providerStoreExportServiceRpc;

    /**
     * 耗材包仓储
     */
    @Resource
    JdhMaterialPackageRepository jdhMaterialPackageRepository;

    /**
     * 查询内容信息
     *
     * @param request req
     * @return dto
     */
    @Override
    public ProviderContentDto queryContentInfo(JdhStationContentRequest request) {
        AssertUtils.hasText(request.getContentName(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("contentId"));
        log.info("ProviderContentApplicationImpl#queryContentInfo req={}", JSON.toJSONString(request));
        ProviderContent providerContent = providerContentRepository.find(ProviderContentIdentifier.builder().contentBizId(request.getContentBizId()).build());
        log.info("ProviderContentApplicationImpl#queryContentInfo result ={}",JSON.toJSONString(providerContent));
        return this.buildProviderContentDto(providerContent);
    }

    @Override
    public PageDto<ProviderContentDto> queryStationContentPage(JdhStationContentRequest request) {

        log.info("ProviderContentApplicationImpl#queryStationContentPage, request={}", JSON.toJSONString(request));
        PageDto<ProviderContentDto> pageDto = new PageDto<>();
        pageDto.setTotalPage(CommonConstant.ZERO);
        pageDto.setPageNum(request.getPageNum());
        pageDto.setPageSize(request.getPageSize());
        pageDto.setTotalCount(CommonConstant.ZERO);
        pageDto.setList(Collections.emptyList());
        ProviderContent query = ProviderContentApplicationConverter.INSTANCE.requestToModel(request);
        Page<ProviderContent> page = providerContentRepository.findListPage(query);
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalCount(page.getTotal());
        if (CollUtil.isEmpty(page.getRecords())) {
            pageDto.setList(Collections.emptyList());
            log.info("ProviderContentApplicationImpl#queryStationContentPage, pageDto={}", JSON.toJSONString(pageDto));
            return pageDto;
        }
        pageDto.setList(page.getRecords().stream().map(this::buildProviderContentDto).filter(Objects::nonNull).collect(Collectors.toList()));
        log.info("ProviderContentApplicationImpl#queryStationContentPage, pageDto={}", JSON.toJSONString(pageDto));
        return pageDto;
    }

    @Override
    public Boolean addStationContent(JdhStationContentCreateCmd cmd) {
        AssertUtils.nonNull(cmd,ProviderErrorCode.PARAM_NULL_ERROR);
        ProviderContent providerContent = ProviderContentApplicationConverter.INSTANCE.createCmdToModel(cmd);
        providerContent.setAuditStage(cmd.getAuditStageDto()==null?null:JSON.toJSONString(cmd.getAuditStageDto()));
        providerContent.setMachineStage(cmd.getMachineStageDto()==null?null:JSON.toJSONString(cmd.getMachineStageDto()));
        providerContent.setCheckStage(cmd.getCheckStageDto()==null?null:JSON.toJSONString(cmd.getCheckStageDto()));
        providerContent.setProcessStage(cmd.getProcessStageDto()==null?null:JSON.toJSONString(cmd.getProcessStageDto()));
        providerContent.setReportStage(cmd.getReportStageDto()==null?null:JSON.toJSONString(cmd.getReportStageDto()));
        providerContent.setContentBizId(generateIdFactory.getId());
        return providerContentRepository.save(providerContent) > CommonConstant.ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addStationContentItemRel(JdhStationContentItemRelCreateCmd cmd) {
        AssertUtils.nonNull(cmd,ProviderErrorCode.PARAM_NULL_ERROR);
        int i = CommonConstant.ZERO;
        for(JdhStationServiceItemRelRequest serviceItemRelRequest:cmd.getServiceItemRels()){
            JdhStationServiceItemRel jdhStationServiceItemRel = providerStoreRepository.queryStationServiceItem(JdhStationServiceItemRel.builder().stationId(serviceItemRelRequest.getStationId()).serviceItemId(serviceItemRelRequest.getServiceItemId()).build());
            if(jdhStationServiceItemRel.getContentBizId() != null){
                if(cmd.getAddType().equals(CommonConstant.TWO)){
                    i = i + providerStoreRepository.updateContentId(JdhStationServiceItemRel.builder().stationId(serviceItemRelRequest.getStationId()).serviceItemId(serviceItemRelRequest.getServiceItemId()).contentBizId(cmd.getContentBizId()).version(jdhStationServiceItemRel.getVersion()).build());
                }else{
                    i ++;
                }
            }else{
                i = i + providerStoreRepository.updateContentId(JdhStationServiceItemRel.builder().stationId(serviceItemRelRequest.getStationId()).serviceItemId(serviceItemRelRequest.getServiceItemId()).contentBizId(cmd.getContentBizId()).version(jdhStationServiceItemRel.getVersion()).build());
            }
        }
        return i > CommonConstant.ZERO;
    }

    @Override
    public Boolean updateStationContent(JdhStationContentUpdateCmd cmd) {
        AssertUtils.nonNull(cmd,ProviderErrorCode.PARAM_NULL_ERROR);
        ProviderContent providerContent = ProviderContentApplicationConverter.INSTANCE.updateCmdToModel(cmd);
        providerContent.setAuditStage(cmd.getAuditStageDto()==null?null:JSON.toJSONString(cmd.getAuditStageDto()));
        providerContent.setMachineStage(cmd.getMachineStageDto()==null?null:JSON.toJSONString(cmd.getMachineStageDto()));
        providerContent.setCheckStage(cmd.getCheckStageDto()==null?null:JSON.toJSONString(cmd.getCheckStageDto()));
        providerContent.setProcessStage(cmd.getProcessStageDto()==null?null:JSON.toJSONString(cmd.getProcessStageDto()));
        providerContent.setReportStage(cmd.getReportStageDto()==null?null:JSON.toJSONString(cmd.getReportStageDto()));
        return providerContentRepository.save(providerContent) > CommonConstant.ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteStationContent(JdhStationContentDeleteCmd cmd) {
        AssertUtils.nonNull(cmd,ProviderErrorCode.PARAM_NULL_ERROR);
        ProviderContent providerContent = ProviderContentApplicationConverter.INSTANCE.deleteCmdToModel(cmd);
        JdhStationServiceItemRel jdhStationServiceItemRel = new JdhStationServiceItemRel();
        jdhStationServiceItemRel.setContentBizId(providerContent.getContentBizId());
        List<JdhStationServiceItemRel> stationServiceItemRels = providerStoreRepository.queryStationServiceItemByEquipmentIdOrContentId(jdhStationServiceItemRel);
        stationServiceItemRels.forEach(stationServiceItemRel ->{
            providerStoreRepository.updateContentId(JdhStationServiceItemRel.builder().stationId(stationServiceItemRel.getStationId()).serviceItemId(stationServiceItemRel.getServiceItemId()).version(stationServiceItemRel.getVersion()).build());
        });
        return providerContentRepository.remove(providerContent) > CommonConstant.ZERO;
    }

    @Override
    public ProviderContentDto queryContentDtoByStationIdAndItemId(String stationId, Long serviceItemId) {
        AssertUtils.hasText(stationId, ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("stationId"));
        AssertUtils.hasText(String.valueOf(serviceItemId), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("serviceItemId"));
        JdhStationServiceItemRel jdhStationServiceItemRel = new JdhStationServiceItemRel();
        jdhStationServiceItemRel.setStationId(stationId);
        jdhStationServiceItemRel.setServiceItemId(serviceItemId);
        JdhStationServiceItemRel stationServiceItemRel = providerStoreRepository.queryStationServiceItem(jdhStationServiceItemRel);
        if(stationServiceItemRel != null){
            ProviderContent providerContent = providerContentRepository.find(ProviderContentIdentifier.builder().contentBizId(stationServiceItemRel.getContentBizId()).build());
            log.info("ProviderContentApplicationImpl#queryContentDtoByStationIdAndItemId result ={}",JSON.toJSONString(providerContent));
            return this.buildProviderContentDto(providerContent);
        }
        return null;
    }

    @Override
    public PageDto<JdhStationServiceItemRelDto> queryItemListByContent(JdhStationContentRequest request) {
        PageDto<JdhStationServiceItemRelDto> pageDto = new PageDto<>();
        pageDto.setTotalPage(CommonConstant.ZERO);
        pageDto.setPageNum(request.getPageNum());
        pageDto.setPageSize(request.getPageSize());
        pageDto.setTotalCount(CommonConstant.ZERO);
        pageDto.setList(Collections.emptyList());
        JdhStationServiceItemRel jdhStationServiceItemRel = new JdhStationServiceItemRel();
        jdhStationServiceItemRel.setContentBizId(request.getContentBizId());
        jdhStationServiceItemRel.setStationId(request.getStationId());
        jdhStationServiceItemRel.setServiceItemId(request.getServiceItemId());
        jdhStationServiceItemRel.setPageSize(request.getPageSize());
        jdhStationServiceItemRel.setPageNum(request.getPageNum());
        Map<String, StoreInfoDTO> storeInfoDTOMap = new HashMap<>();
        if(StringUtils.isNotEmpty(request.getStationId()) || StringUtils.isNotEmpty(request.getStationName())){
            StoreInfoQueryParam storeInfoQueryParam = new StoreInfoQueryParam();
            storeInfoQueryParam.setJdStoreId(request.getStationId());
            storeInfoQueryParam.setStoreName(request.getStationName());
            storeInfoQueryParam.setPageNum(CommonConstant.ONE);
            storeInfoQueryParam.setPageSize(CommonConstant.NUMBER_FIVE_THOUSAND);
            List<StoreInfoDTO> storeInfoDTOS = providerStoreExportServiceRpc.queryMerchantStoreListByParam(storeInfoQueryParam).getList();
            if(CollectionUtils.isNotEmpty(storeInfoDTOS)) {
                storeInfoDTOMap.putAll(storeInfoDTOS.stream().filter(Objects::nonNull).collect(Collectors.toMap(StoreInfoDTO::getJdStoreId, item -> item, (v1, v2) -> v2)));
                jdhStationServiceItemRel.setStationIdSet(storeInfoDTOMap.keySet());
            }else{
                return pageDto;
            }
        }
        Map<Long,ServiceItem> serviceItemMap = new HashMap<>();
        if(request.getServiceItemId() != null || StringUtils.isNotEmpty(request.getServiceItemName())){
            List<ServiceItem> serviceItems = jdhServiceItemRepository.queryJdhItemList(ServiceItemQueryContext.builder().itemIds(Collections.singleton(request.getServiceItemId())).itemName(request.getServiceItemName()).build());
            if(CollectionUtils.isNotEmpty(serviceItems)) {
                serviceItemMap.putAll(serviceItems.stream().collect(Collectors.toMap(ServiceItem::getItemId, Function.identity())));
                jdhStationServiceItemRel.setServiceItemIds(serviceItemMap.keySet());
            }else{
                return pageDto;
            }
        }
        Page<JdhStationServiceItemRel> page = providerStoreRepository.queryStationServiceItemByContentPage(jdhStationServiceItemRel);
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalCount(page.getTotal());
        if (CollUtil.isEmpty(page.getRecords())) {
            pageDto.setList(Collections.emptyList());
            log.info("ProviderContentApplicationImpl#queryItemListByContent, pageDto={}", JSON.toJSONString(pageDto));
            return pageDto;
        }
        List<JdhStationServiceItemRel> stationServiceItemRels = page.getRecords();
        if(CollectionUtils.isNotEmpty(stationServiceItemRels)){
            Set<Long> itemIds = new HashSet<>();
            Set<Long> equipmentBizIds = new HashSet<>();
            List<JdhMaterialPackage> jdhMaterialPackageList = new ArrayList<>();
            stationServiceItemRels.forEach(stationServiceItemRel -> {
                itemIds.add(stationServiceItemRel.getServiceItemId());
                equipmentBizIds.add(stationServiceItemRel.getEquipmentBizId());
                JdhMaterialPackage jdhMaterialPackage = new JdhMaterialPackage();
                jdhMaterialPackage.setMaterialPackageId(stationServiceItemRel.getMaterialPackageId());
                jdhMaterialPackageList.add(jdhMaterialPackage);
            });
            if(CollectionUtils.isEmpty(serviceItemMap.keySet())) {
                List<ServiceItem> serviceItems = jdhServiceItemRepository.queryJdhItemList(ServiceItemQueryContext.builder().itemIds(itemIds).build());
                serviceItemMap.putAll(serviceItems.stream().collect(Collectors.toMap(ServiceItem::getItemId, Function.identity())));
            }
            Map<String, StoreInfoBo> StoreInfoBoMap = new HashMap<>();
            if(CollectionUtils.isEmpty(storeInfoDTOMap.keySet())){
                List<StoreInfoBo> storeInfoBoList = providerStoreExportServiceRpc.listByStoreIds(stationServiceItemRels.stream().map(JdhStationServiceItemRel::getStationId).collect(Collectors.toSet()));
                StoreInfoBoMap.putAll(storeInfoBoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(StoreInfoBo::getJdStoreId, item -> item, (v1, v2) -> v2)));
            }
            List<JdhMaterialPackage> jdhMaterialPackages = jdhMaterialPackageRepository.queryList(jdhMaterialPackageList);
            Map<Long, JdhMaterialPackage> materialPackageMap = jdhMaterialPackages.stream().collect(Collectors.toMap(JdhMaterialPackage::getMaterialPackageId, Function.identity()));
            List<ProviderEquipment> providerEquipments = providerEquipmentRepository.findList(ProviderEquipment.builder().equipmentBizIds(equipmentBizIds).build());
            Map<Long, ProviderEquipment> providerEquipmentMap = providerEquipments.stream().collect(Collectors.toMap(ProviderEquipment::getEquipmentBizId, Function.identity()));
            stationServiceItemRels.forEach(stationServiceItemRel ->{
                ServiceItem serviceItem = serviceItemMap.get(stationServiceItemRel.getServiceItemId());
                if(serviceItem != null){
                    stationServiceItemRel.setServiceItemName(serviceItem.getItemName());
                    stationServiceItemRel.setServiceItemNameEn(serviceItem.getItemNameEn());
                    stationServiceItemRel.setSpecimenType(serviceItem.getSampleType());
                    stationServiceItemRel.setTestWay(serviceItem.getTestWay());
                }
                StoreInfoDTO storeInfoDTO = storeInfoDTOMap.get(stationServiceItemRel.getStationId());
                if (storeInfoDTO != null) {
                    stationServiceItemRel.setChannelNo(storeInfoDTO.getChannelNo());
                    stationServiceItemRel.setStationName(storeInfoDTO.getStoreName());
                    stationServiceItemRel.setStationStatus(storeInfoDTO.getStatus());
                }
                StoreInfoBo storeInfoBo = StoreInfoBoMap.get(stationServiceItemRel.getStationId());
                if (storeInfoBo != null) {
                    stationServiceItemRel.setChannelNo(storeInfoBo.getChannelNo());
                    stationServiceItemRel.setStationName(storeInfoBo.getStoreName());
                    stationServiceItemRel.setStationStatus(storeInfoBo.getStatus());
                }
                JdhMaterialPackage jdhMaterialPackage = materialPackageMap.get(stationServiceItemRel.getMaterialPackageId());
                if (jdhMaterialPackage != null) {
                    stationServiceItemRel.setMaterialPackageName(jdhMaterialPackage.getMaterialPackageName());
                }
                ProviderEquipment providerEquipment = providerEquipmentMap.get(stationServiceItemRel.getEquipmentBizId());
                if(providerEquipment != null){
                    stationServiceItemRel.setEquipmentModel(providerEquipment.getEquipmentModel());
                    stationServiceItemRel.setManufacturerId(providerEquipment.getManufacturerId());
                }
            });
            log.info("ProviderContentApplicationImpl#queryItemListByContent result ={}",JSON.toJSONString(stationServiceItemRels));
            pageDto.setList(ProviderStoreApplicationConverter.INSTANCE.modelToDtoList(stationServiceItemRels));
        }
        log.info("ProviderContentApplicationImpl#queryItemListByContent, pageDto={}", JSON.toJSONString(pageDto));
        return pageDto;
    }

    @Override
    public List<JdhStationServiceItemRelDto> queryItemAndContentRel(JdhStationContentRequest request) {
        List<JdhStationServiceItemRelRequest> serviceItemRelRequests = request.getServiceItemRels();
        if(serviceItemRelRequests == null){
            return new ArrayList<>();
        }
        List<JdhStationServiceItemRel> stationServiceItemRels = new ArrayList<>();
        serviceItemRelRequests.forEach(serviceItemRelRequest ->{
            JdhStationServiceItemRel jdhStationServiceItemRel = providerStoreRepository.queryStationServiceItem(JdhStationServiceItemRel.builder().stationId(serviceItemRelRequest.getStationId()).serviceItemId(serviceItemRelRequest.getServiceItemId()).build());
            if(jdhStationServiceItemRel.getContentBizId() != null){
                stationServiceItemRels.add(jdhStationServiceItemRel);
            }
        });
        if(CollectionUtils.isNotEmpty(stationServiceItemRels)){
            Set<Long> itemIds = new HashSet<>();
            Set<Long> contentBizIds = new HashSet<>();
            stationServiceItemRels.forEach(stationServiceItemRel -> {
                itemIds.add(stationServiceItemRel.getServiceItemId());
                contentBizIds.add(stationServiceItemRel.getContentBizId());
            });
            List<ServiceItem> serviceItems = jdhServiceItemRepository.queryJdhItemList(ServiceItemQueryContext.builder().itemIds(itemIds).build());
            Map<Long,ServiceItem> serviceItemMap = serviceItems.stream().collect(Collectors.toMap(ServiceItem::getItemId, Function.identity()));
            List<StoreInfoBo> storeInfoBoList = providerStoreExportServiceRpc.listByStoreIds(stationServiceItemRels.stream().map(JdhStationServiceItemRel::getStationId).collect(Collectors.toSet()));
            Map<String, StoreInfoBo> StoreInfoBoMap = storeInfoBoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(StoreInfoBo::getJdStoreId, item -> item, (v1, v2) -> v2));
            List<ProviderContent> providerContents = providerContentRepository.findList(ProviderContent.builder().contentBizIds(contentBizIds).build());
            Map<Long, ProviderContent> providerContentMap = providerContents.stream().collect(Collectors.toMap(ProviderContent::getContentBizId, Function.identity()));
            stationServiceItemRels.forEach(stationServiceItemRel ->{
                ServiceItem serviceItem = serviceItemMap.get(stationServiceItemRel.getServiceItemId());
                if(serviceItem != null){
                    stationServiceItemRel.setServiceItemName(serviceItem.getItemName());
                    stationServiceItemRel.setServiceItemNameEn(serviceItem.getItemNameEn());
                    stationServiceItemRel.setSpecimenType(serviceItem.getSampleType());
                    stationServiceItemRel.setTestWay(serviceItem.getTestWay());
                }
                StoreInfoBo storeInfoBo = StoreInfoBoMap.get(stationServiceItemRel.getStationId());
                if (storeInfoBo != null) {
                    stationServiceItemRel.setChannelNo(storeInfoBo.getChannelNo());
                    stationServiceItemRel.setStationName(storeInfoBo.getStoreName());
                    stationServiceItemRel.setStationStatus(storeInfoBo.getStatus());
                }
                ProviderContent providerContent = providerContentMap.get(stationServiceItemRel.getContentBizId());
                if(providerContent != null){
                    stationServiceItemRel.setContentBizId(providerContent.getContentBizId());
                    stationServiceItemRel.setContentName(providerContent.getContentName());
                }
            });
            log.info("ProviderContentApplicationImpl#queryItemAndContentRel result ={}",JSON.toJSONString(stationServiceItemRels));
            return  ProviderStoreApplicationConverter.INSTANCE.modelToDtoList(stationServiceItemRels);
        }
        return new ArrayList<>();
    }

    private ProviderContentDto buildProviderContentDto(ProviderContent providerContent){
        ProviderContentDto providerContentDto = ProviderContentApplicationConverter.INSTANCE.modelToDto(providerContent);
        if(StringUtils.isNotEmpty(providerContent.getAuditStage())){
            providerContentDto.setAuditStageDto(JSONObject.parseObject(providerContent.getAuditStage(), ProviderContentStageDto.class));
        }
        if(StringUtils.isNotEmpty(providerContent.getCheckStage())){
            providerContentDto.setCheckStageDto(JSONObject.parseObject(providerContent.getCheckStage(), ProviderContentStageDto.class));
        }
        if(StringUtils.isNotEmpty(providerContent.getMachineStage())){
            providerContentDto.setMachineStageDto(JSONObject.parseObject(providerContent.getMachineStage(), ProviderContentStageDto.class));
        }
        if(StringUtils.isNotEmpty(providerContent.getProcessStage())){
            providerContentDto.setProcessStageDto(JSONObject.parseObject(providerContent.getProcessStage(), ProviderContentStageDto.class));
        }
        if(StringUtils.isNotEmpty(providerContent.getReportStage())){
            providerContentDto.setReportStageDto(JSONObject.parseObject(providerContent.getReportStage(), ProviderContentStageDto.class));
        }
        log.info("ProviderContentApplicationImpl#buildProviderContentDto, providerContentDto={}", JSON.toJSONString(providerContentDto));
        return providerContentDto;
    }

}
