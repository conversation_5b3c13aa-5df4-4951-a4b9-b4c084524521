package com.jdh.o2oservice.application.provider.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.application.provider.convert.ProviderContentApplicationConverter;
import com.jdh.o2oservice.application.provider.convert.ProviderEquipmentApplicationConverter;
import com.jdh.o2oservice.application.provider.convert.ProviderStoreApplicationConverter;
import com.jdh.o2oservice.application.provider.service.ProviderEquipmentApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.product.context.ServiceItemExactQueryContext;
import com.jdh.o2oservice.core.domain.product.model.JdhMaterialPackage;
import com.jdh.o2oservice.core.domain.product.model.ServiceItem;
import com.jdh.o2oservice.core.domain.product.model.ServiceItemIdentifier;
import com.jdh.o2oservice.core.domain.product.model.ServiceItemIndicatorRel;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemRepository;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.enums.ProviderErrorCode;
import com.jdh.o2oservice.core.domain.provider.model.*;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderEquipmentRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderStoreRepository;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.export.provider.cmd.JdhStationEquipmentCreateCmd;
import com.jdh.o2oservice.export.provider.cmd.JdhStationEquipmentDeleteCmd;
import com.jdh.o2oservice.export.provider.cmd.JdhStationEquipmentUpdateCmd;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.ProviderContentDto;
import com.jdh.o2oservice.export.provider.dto.ProviderEquipmentDto;
import com.jdh.o2oservice.export.provider.query.JdhStationEquipmentRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 实验室设备管理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ProviderEquipmentApplicationImpl implements ProviderEquipmentApplication {


    @Resource
    ProviderEquipmentRepository providerEquipmentRepository;

    @Resource
    ProviderStoreRepository providerStoreRepository;

    @Resource
    private GenerateIdFactory generateIdFactory;

    @Resource
    JdhServiceItemRepository jdhServiceItemRepository;

    /**
     * 门店信息
     */
    @Autowired
    private ProviderStoreExportServiceRpc providerStoreExportServiceRpc;

    /**
     * 查询设备信息
     *
     * @param request req
     * @return dto
     */
    @Override
    public ProviderEquipmentDto queryEquipmentInfo(JdhStationEquipmentRequest request) {
        AssertUtils.hasText(String.valueOf(request.getEquipmentBizId()), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("equipmentId"));
        log.info("ProviderEquipmentApplicationImpl#queryEquipmentInfo req={}", JSON.toJSONString(request));
        ProviderEquipment providerEquipment = providerEquipmentRepository.find(ProviderEquipmentIdentifier.builder().equipmentBizId(request.getEquipmentBizId()).build());
        log.info("ProviderEquipmentApplicationImpl#queryEquipmentInfo result ={}",JSON.toJSONString(providerEquipment));
        return ProviderEquipmentApplicationConverter.INSTANCE.modelToDto(providerEquipment);
    }

    @Override
    public PageDto<ProviderEquipmentDto> queryStationEquipmentPage(JdhStationEquipmentRequest request) {
        log.info("ProviderEquipmentApplicationImpl#queryStationEquipmentPage, request={}", JSON.toJSONString(request));
        PageDto<ProviderEquipmentDto> pageDto = new PageDto<>();
        pageDto.setTotalPage(CommonConstant.ZERO);
        pageDto.setPageNum(request.getPageNum());
        pageDto.setPageSize(request.getPageSize());
        pageDto.setTotalCount(CommonConstant.ZERO);
        pageDto.setList(Collections.emptyList());
        ProviderEquipment providerEquipment = ProviderEquipmentApplicationConverter.INSTANCE.requestToModel(request);
        Page<ProviderEquipment> page = providerEquipmentRepository.findListPage(providerEquipment);
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalCount(page.getTotal());
        if (CollUtil.isEmpty(page.getRecords())) {
            pageDto.setList(Collections.emptyList());
            log.info("ProviderEquipmentApplicationImpl#queryStationEquipmentPage, pageDto={}", JSON.toJSONString(pageDto));
            return pageDto;
        }
        pageDto.setList(page.getRecords().stream().map(ProviderEquipmentApplicationConverter.INSTANCE::modelToDto).filter(Objects::nonNull).collect(Collectors.toList()));
        log.info("ProviderEquipmentApplicationImpl#queryStationEquipmentPage, pageDto={}", JSON.toJSONString(pageDto));
        return pageDto;
    }

    @Override
    public Boolean addStationEquipment(JdhStationEquipmentCreateCmd cmd) {
        AssertUtils.nonNull(cmd,ProviderErrorCode.PARAM_NULL_ERROR);
        ProviderEquipment providerContent = ProviderEquipmentApplicationConverter.INSTANCE.createCmdToModel(cmd);
        providerContent.setEquipmentBizId(generateIdFactory.getId());
        return providerEquipmentRepository.save(providerContent) > CommonConstant.ZERO;
    }

    @Override
    public Boolean updateStationEquipment(JdhStationEquipmentUpdateCmd cmd) {
        AssertUtils.nonNull(cmd,ProviderErrorCode.PARAM_NULL_ERROR);
        ProviderEquipment providerContent = ProviderEquipmentApplicationConverter.INSTANCE.updateCmdToModel(cmd);
        return providerEquipmentRepository.save(providerContent) > CommonConstant.ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteStationEquipment(JdhStationEquipmentDeleteCmd cmd) {
        AssertUtils.nonNull(cmd,ProviderErrorCode.PARAM_NULL_ERROR);
        ProviderEquipment providerEquipment = ProviderEquipmentApplicationConverter.INSTANCE.deleteCmdToModel(cmd);
        JdhStationServiceItemRel jdhStationServiceItemRel = new JdhStationServiceItemRel();
        jdhStationServiceItemRel.setEquipmentBizId(providerEquipment.getEquipmentBizId());
        List<JdhStationServiceItemRel> stationServiceItemRels = providerStoreRepository.queryStationServiceItemByEquipmentIdOrContentId(jdhStationServiceItemRel);
        stationServiceItemRels.forEach(stationServiceItemRel ->{
            providerStoreRepository.updateEquipmentId(JdhStationServiceItemRel.builder().stationId(stationServiceItemRel.getStationId()).serviceItemId(stationServiceItemRel.getServiceItemId()).version(stationServiceItemRel.getVersion()).build());
        });
        return providerEquipmentRepository.remove(providerEquipment) > CommonConstant.ZERO;
    }

    @Override
    public ProviderEquipmentDto queryEquipmentDtoByStationIdAndItemId(String stationId, Long serviceItemId) {
        AssertUtils.hasText(stationId, ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("stationId"));
        AssertUtils.hasText(String.valueOf(serviceItemId), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("serviceItemId"));
        JdhStationServiceItemRel jdhStationServiceItemRel = new JdhStationServiceItemRel();
        jdhStationServiceItemRel.setStationId(stationId);
        jdhStationServiceItemRel.setServiceItemId(serviceItemId);
        JdhStationServiceItemRel stationServiceItemRel = providerStoreRepository.queryStationServiceItem(jdhStationServiceItemRel);
        if(stationServiceItemRel != null){
            ProviderEquipment providerEquipment = providerEquipmentRepository.find(ProviderEquipmentIdentifier.builder().equipmentBizId(stationServiceItemRel.getEquipmentBizId()).build());
            log.info("ProviderEquipmentApplicationImpl#queryEquipmentDtoByStationIdAndItemId result ={}",JSON.toJSONString(providerEquipment));
            return ProviderEquipmentApplicationConverter.INSTANCE.modelToDto(providerEquipment);
        }
        return null;
    }


    @Override
    public List<JdhStationServiceItemRelDto> queryItemListByEquipment(JdhStationEquipmentRequest request) {
        AssertUtils.hasText(String.valueOf(request.getEquipmentBizId()), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("equipmentBizId"));
        JdhStationServiceItemRel jdhStationServiceItemRel = new JdhStationServiceItemRel();
        jdhStationServiceItemRel.setEquipmentBizId(request.getEquipmentBizId());
        List<JdhStationServiceItemRel> stationServiceItemRels = providerStoreRepository.queryStationServiceItemByEquipmentIdOrContentId(jdhStationServiceItemRel);
        if(CollectionUtils.isNotEmpty(stationServiceItemRels)){
            Set<Long> itemIds = stationServiceItemRels.stream().map(JdhStationServiceItemRel::getServiceItemId).collect(Collectors.toSet());
            List<ServiceItem> serviceItems = jdhServiceItemRepository.queryServiceItemExactList(ServiceItemExactQueryContext.builder().itemIds(itemIds).build());
            Map<Long,ServiceItem> serviceItemMap = serviceItems.stream().collect(Collectors.toMap(ServiceItem::getItemId, Function.identity()));
            List<StoreInfoBo> storeInfoBoList = providerStoreExportServiceRpc.listByStoreIds(stationServiceItemRels.stream().map(JdhStationServiceItemRel::getStationId).collect(Collectors.toSet()));
            Map<String, StoreInfoBo> storeInfoBoMap = storeInfoBoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(StoreInfoBo::getJdStoreId, item -> item, (v1, v2) -> v2));
            stationServiceItemRels.forEach(stationServiceItemRel ->{
                ServiceItem serviceItem = serviceItemMap.get(stationServiceItemRel.getServiceItemId());
                if(serviceItem != null){
                    stationServiceItemRel.setServiceItemName(serviceItem.getItemName());
                    stationServiceItemRel.setServiceItemNameEn(serviceItem.getItemNameEn());
                    stationServiceItemRel.setSpecimenType(serviceItem.getSampleType());
                    stationServiceItemRel.setTestWay(serviceItem.getTestWay());
                }
                StoreInfoBo storeInfoBo = storeInfoBoMap.get(stationServiceItemRel.getStationId());
                if (storeInfoBo != null) {
                    stationServiceItemRel.setChannelNo(storeInfoBo.getChannelNo());
                    stationServiceItemRel.setStationName(storeInfoBo.getStoreName());
                    stationServiceItemRel.setStationStatus(storeInfoBo.getStatus());
                }
            });
            log.info("ProviderEquipmentApplicationImpl#queryItemListByEquipment result ={}",JSON.toJSONString(stationServiceItemRels));
            return ProviderStoreApplicationConverter.INSTANCE.modelToDtoList(stationServiceItemRels);
        }
        return null;
    }

}
