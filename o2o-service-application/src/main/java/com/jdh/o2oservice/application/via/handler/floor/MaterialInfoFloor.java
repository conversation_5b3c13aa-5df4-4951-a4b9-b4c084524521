package com.jdh.o2oservice.application.via.handler.floor;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.via.handler.materialfloor.MaterialInfoFloorHandler;
import com.jdh.o2oservice.application.via.handler.materialfloor.MaterialInfoFloorServiceContext;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.CustomMaterialProgressBarMapping;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.enums.RelationTypeEnum;
import com.jdh.o2oservice.base.util.BeanUtil;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseSubdivisionStatusEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.model.*;
import com.jdh.o2oservice.export.laboratory.dto.QueryMerchantStoreDetailResponse;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantStoreDetailByParamRequest;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.support.dto.FileUrlDto;
import com.jdh.o2oservice.export.trade.dto.CustomOrderDetailProgressBarDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Project : 新样本信息楼层
 * <AUTHOR> maoxianglin1
 * @create 2025/7/10 下午4:25
 */
@Slf4j
@Component
public class MaterialInfoFloor implements Floor {

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private ProviderStoreApplication providerStoreApplication;

    @Resource
    private MaterialInfoFloorServiceContext materialInfoFloorServiceContext;

    private final String MATERIAL_INFO_FLOOR_HANDLER_KEY = CommonConstant.MEDICAL_PROMISE_STATUS_KEY;

    @Override
    @LogAndAlarm
    public void handleData(FillViaConfigDataContext ctx, Map<String, Object> sourceData, ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping, JdhPromise jdhPromise, JdOrderDTO jdOrder, List<JdhPromiseHistory> promiseHistories, List<MedicalPromiseDTO> medicalPromiseList) {
        log.info("UserServiceSurveyFloor.handleData viaFloorInfo={}", JSON.toJSONString(viaFloorInfo));
        // 不存在检验单信息，无需增强该楼层
        if (CollectionUtils.isEmpty(medicalPromiseList)) {
            return;
        }
        String scene = String.valueOf(sourceData.get("scene"));
        // 检测单按人分堆
        Map<Long, List<MedicalPromiseDTO>> medicalPromiseMap = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromisePatientId));
        // 履约单相关患者信息Map
        Map<Long, JdhPromisePatient> promisePatientMap = jdhPromise.getPatients().stream().collect(Collectors.toMap(JdhPromisePatient::getPromisePatientId, Function.identity()));

        // 样本专业信息
        ViaMaterialInfoDTO viaMaterialInfoDTO = new ViaMaterialInfoDTO();
        // 样本检测用户信息
        List<ViaMaterialPatientInfoDTO> patientInfoList = new ArrayList<>();
        // 样本检测具体信息
        List<ViaMaterialDetailInfoDTO> viaMaterialDetailInfoList = new ArrayList<>();

        medicalPromiseMap.forEach((promisePatientId, list) -> {
            // 组装头部被服务者信息
            JdhPromisePatient jdhPromisePatient = promisePatientMap.get(promisePatientId);
            ViaMaterialPatientInfoDTO viaMaterialPatientInfoDTO = new ViaMaterialPatientInfoDTO();
            viaMaterialPatientInfoDTO.setPatientAge(jdhPromisePatient.getBirthday().getAge() + "岁");
            viaMaterialPatientInfoDTO.setPatientGender(GenderEnum.getDescOfType(jdhPromisePatient.getGender()));
            viaMaterialPatientInfoDTO.setPatientName(jdhPromisePatient.getUserName().mask());
            if (jdhPromisePatient.getRelativesType() != null) {
                viaMaterialPatientInfoDTO.setPatientRelationType(RelationTypeEnum.getEnumByType(jdhPromisePatient.getRelativesType()).getDesc());
            }
            // 判断当前用户的履约检测单是否全部作废，如果全部作废，则返回退款标识
            List<MedicalPromiseDTO> cancelMedicalPromiseDTOList = list.stream().filter(e -> MedicalPromiseStatusEnum.INVALID.getStatus().equals(e.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cancelMedicalPromiseDTOList) && cancelMedicalPromiseDTOList.size() == list.size()) {
                viaMaterialPatientInfoDTO.setOrderStatusDesc("已退款");
            }
            patientInfoList.add(viaMaterialPatientInfoDTO);

            // 过滤冻结或退款的样本数据
            List<MedicalPromiseDTO> surviveList = list.stream()
                    .filter(e -> !Objects.equals(e.getStatus(), MedicalPromiseStatusEnum.INVALID.getStatus()))
                    .filter(e -> !Objects.equals(e.getFreeze(), JdhFreezeEnum.FREEZE.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(surviveList)) {
                return;
            }

            ViaMaterialDetailInfoDTO viaMaterialDetailInfoDTO = new ViaMaterialDetailInfoDTO();
            viaMaterialDetailInfoDTO.setPatientAge(jdhPromisePatient.getBirthday().getAge() + "岁");
            viaMaterialDetailInfoDTO.setPatientGender(GenderEnum.getDescOfType(jdhPromisePatient.getGender()));
            viaMaterialDetailInfoDTO.setPatientName(jdhPromisePatient.getUserName().getName());

            // 具体的楼层数据
            List<ViaMaterialFloorDetailInfoDTO> viaMaterialFloorDetailInfoDTOList = new ArrayList<>();

            // 将履约检测单分类，分成检测完成和检测未完成的样本数据
            List<MedicalPromiseDTO> completedSurviveList = new ArrayList<>();
            List<MedicalPromiseDTO> unCompletedSurviveList = new ArrayList<>();
            for (MedicalPromiseDTO promiseDTO : surviveList) {
                if (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(promiseDTO.getStatus())) {
                    completedSurviveList.add(promiseDTO);
                } else {
                    unCompletedSurviveList.add(promiseDTO);
                }
            }

            // 如果存在已经完成的节点，则需要组装完成的信息数据
            if (CollectionUtils.isNotEmpty(completedSurviveList)) {
                String status = MATERIAL_INFO_FLOOR_HANDLER_KEY + MedicalPromiseSubdivisionStatusEnum.MATERIAL_INFO_INSPECTION_COMPLETED.getStatusCode();
                ViaMaterialFloorDetailInfoDTO viaMaterialFloorDetailInfoDTO = materialInfoFloorServiceContext.getHandler(status).getViaMaterialInfo(statusMapping, surviveList, jdhPromise, jdOrder, scene);
                if (viaMaterialFloorDetailInfoDTO != null) {
                    viaMaterialFloorDetailInfoDTOList.add(viaMaterialFloorDetailInfoDTO);
                }
            }

            // 均已经完成，无需走下面流程
            if (CollectionUtils.isEmpty(unCompletedSurviveList)) {
                return;
            }

            // 根据履约单节点状态进行排序
            unCompletedSurviveList.sort(Comparator.comparing(MedicalPromiseDTO::getStatus));
            for (MedicalPromiseDTO medicalPromiseDTO : unCompletedSurviveList) {

                Integer medicalPromiseStatus = medicalPromiseDTO.getStatus();
                // 依赖鹏飞新增节点字段信息
                Integer subdivisionStatus = medicalPromiseDTO.getSubStatus();
                MedicalPromiseSubdivisionStatusEnum medicalPromiseSubdivisionStatusEnum = MedicalPromiseSubdivisionStatusEnum.getMedicalPromiseSubdivisionStatusEnumByCondition(medicalPromiseStatus, subdivisionStatus);
                if (medicalPromiseSubdivisionStatusEnum == null) {
                    continue;
                }
                String status = MATERIAL_INFO_FLOOR_HANDLER_KEY + medicalPromiseSubdivisionStatusEnum.getStatusCode();
                MaterialInfoFloorHandler materialInfoFloorHandler = materialInfoFloorServiceContext.getHandler(status);
                ViaMaterialFloorDetailInfoDTO viaMaterialFloorDetailInfoDTO = materialInfoFloorHandler.getViaMaterialInfo(statusMapping, Lists.newArrayList(medicalPromiseDTO), jdhPromise, jdOrder, scene);
                if (viaMaterialFloorDetailInfoDTO == null) {
                    continue;
                }
                // 组装实验室信息
                if (materialInfoFloorHandler.getShowViaMaterialStoreInfoStatus()) {
                    viaMaterialFloorDetailInfoDTO.setViaMaterialStoreInfo(this.getViaMaterialStoreInfoDTO(medicalPromiseDTO));
                }
                // 组装进度条信息
                if (materialInfoFloorHandler.getShowCustomOrderDetailProgressBarStatus()) {
                    viaMaterialFloorDetailInfoDTO.setServiceProgressBar(this.getCustomOrderDetailProgressBarList(subdivisionStatus));
                }
                viaMaterialFloorDetailInfoDTOList.add(viaMaterialFloorDetailInfoDTO);
                viaMaterialDetailInfoDTO.setViaMaterialFloorDetailInfoList(viaMaterialFloorDetailInfoDTOList);
                viaMaterialDetailInfoList.add(viaMaterialDetailInfoDTO);
            }
        });
        viaMaterialInfoDTO.setPatientInfoList(patientInfoList);
        viaMaterialInfoDTO.setViaMaterialDetailInfoList(viaMaterialDetailInfoList);

        List<ViaFloorConfig> floorConfigList = Lists.newArrayList();
        ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
        viaFloorConfig.setViaMaterialInfo(viaMaterialInfoDTO);
        floorConfigList.add(viaFloorConfig);
        viaFloorInfo.setFloorConfigList(floorConfigList);
    }


    /**
     * 通用：获得样本进度条的方法
     *
     * @param subdivisionStatus 样本子状态
     * @return 样本进度条列表
     */
    private List<CustomOrderDetailProgressBarDTO> getCustomOrderDetailProgressBarList(Integer subdivisionStatus) {
        List<CustomMaterialProgressBarMapping> customMaterialProgressBarList = duccConfig.getCustomMaterialProgressBarList();
        return customMaterialProgressBarList.stream().map(e -> {
            CustomOrderDetailProgressBarDTO customOrderDetailProgressBarDTO = new CustomOrderDetailProgressBarDTO();
            BeanUtil.copyProperties(e, customOrderDetailProgressBarDTO);
            if (e.getServiceNodeType().equals(subdivisionStatus)) {
                customOrderDetailProgressBarDTO.setSelected(Boolean.TRUE);
            }
            if (e.getServiceNodeType() <= subdivisionStatus) {
                customOrderDetailProgressBarDTO.setIsDone(Boolean.TRUE);
            }
            return customOrderDetailProgressBarDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 通用：查询实验室配置信息
     *
     * @param medicalPromiseDTO 履约检测单信息
     * @return 实验室配置信息
     */
    private ViaMaterialStoreInfoDTO getViaMaterialStoreInfoDTO(MedicalPromiseDTO medicalPromiseDTO) {
        QueryMerchantStoreDetailByParamRequest queryMerchantStoreDetailByParamRequest = new QueryMerchantStoreDetailByParamRequest();
        queryMerchantStoreDetailByParamRequest.setJdStoreId(medicalPromiseDTO.getStationId());
        QueryMerchantStoreDetailResponse queryMerchantStoreDetailResponse = providerStoreApplication.queryMerchantStoreDetailByParam(queryMerchantStoreDetailByParamRequest);
        if (queryMerchantStoreDetailResponse == null) {
            return null;
        }
        ViaMaterialStoreInfoDTO viaMaterialStoreInfo = new ViaMaterialStoreInfoDTO();
        viaMaterialStoreInfo.setStoreName(queryMerchantStoreDetailResponse.getStoreName());
        FileUrlDto storeFile = queryMerchantStoreDetailResponse.getStoreFile();
        if (storeFile != null) {
            viaMaterialStoreInfo.setStoreIcon(storeFile.getUrl());
        }
        viaMaterialStoreInfo.setStoreLabelList(queryMerchantStoreDetailResponse.getStoreLabelList());
        if (CollectionUtils.isEmpty(queryMerchantStoreDetailResponse.getLicenseInfoList())) {
            return viaMaterialStoreInfo;
        }
        List<ViaMaterialStoreLicenseInfoDTO> licenseInfoList = queryMerchantStoreDetailResponse.getLicenseInfoList().stream().map(e -> {
            ViaMaterialStoreLicenseInfoDTO viaMaterialStoreLicenseInfoDTO = new ViaMaterialStoreLicenseInfoDTO();
            viaMaterialStoreLicenseInfoDTO.setLicenseName(e.getLicenseName());
            if (CollectionUtils.isNotEmpty(e.getLicenseFileList())) {
                List<String> licenseFileUrlList = e.getLicenseFileList().stream().map(FileUrlDto::getUrl).collect(Collectors.toList());
                viaMaterialStoreLicenseInfoDTO.setLicenseFileUrlList(licenseFileUrlList);
            }
            return viaMaterialStoreLicenseInfoDTO;
        }).collect(Collectors.toList());
        viaMaterialStoreInfo.setLicenseInfoList(licenseInfoList);
        return viaMaterialStoreInfo;
    }
}
