package com.jdh.o2oservice.application.trade.handler;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.ServiceTypeNewEnum;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderContext;
import com.jdh.o2oservice.core.domain.trade.enums.JdOrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderExt;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderExtRepository;
import com.jdh.o2oservice.core.domain.trade.service.VerticalCodeAbstractHandler;
import com.jdh.o2oservice.core.domain.trade.vo.JdOrderExtendVo;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentInfoValueObject;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * @ClassName:SelfHomeHandler
 * @Description: 自营到家
 * @Author: liwenming
 * @Date: 2024/5/19 17:07
 * @Vserion: 1.0
 **/
@Slf4j
@Service
public class SelfHomeHandler extends VerticalCodeAbstractHandler<JdOrderContext> implements MapAutowiredKey {

    /**
     * jdOrderExtRepository
     */
    @Resource
    private JdOrderExtRepository jdOrderExtRepository;
    /**
     * 商品
     */
    @Autowired
    private ProductApplication productApplication;


    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return CommonConstant.NUMBER_SIX;
    }


    /**
     * 业务处理消息
     *
     * @param jdOrderContext
     * @return
     */
    @Override
    public void bulidVerticalCode(JdOrderContext jdOrderContext) {
        dealVerticalCodeAndServiceType(jdOrderContext);
    }

    /**
     * 处理VerticalCodeAndServiceType
     * @param jdOrderContext
     */
    private void dealVerticalCodeAndServiceType(JdOrderContext jdOrderContext){
        log.info("[SelfHomeHandler->dealVerticalCodeAndServiceType] jdOrderContext={}", JSON.toJSONString(jdOrderContext));
        JdOrder childOrder = jdOrderContext.getJdOrder();
        JdOrder parentIdOrder = jdOrderContext.getDbOrder();
        childOrder.setPartnerSourceOrderId(parentIdOrder.getPartnerSourceOrderId());
        childOrder.setPartnerSource(parentIdOrder.getPartnerSource());
        childOrder.setRemark(parentIdOrder.getRemark());
        childOrder.setHasAdded(parentIdOrder.getHasAdded());

        StringBuilder stringBuilder = new StringBuilder();
        Integer partnerSource = childOrder.getPartnerSource();
        if(Objects.nonNull(partnerSource) && partnerSource != 0){
            stringBuilder.append(partnerSource);
        }
        List<JdOrderItem> jdOrderItemList = childOrder.getJdOrderItemList();
        if(CollectionUtils.isEmpty(jdOrderItemList)){
            throw new BusinessException(BusinessErrorCode.SKU_INFO_QUERY_FAIL);
        }
        Long skuId = jdOrderItemList.get(0).getSkuId();
        JdhSkuRequest request = new JdhSkuRequest();
        request.setSkuId(skuId);
        JdhSkuDto jdhSkuDto = productApplication.queryJdhSkuInfo(request);
        if(Objects.isNull(jdhSkuDto)){
            log.error("[SelfHomeHandler->dealVerticalCodeAndServiceType],未查询到商品信息!order={}，skuId={}",childOrder.getOrderId(), skuId);
            throw new BusinessException(BusinessErrorCode.SKU_INFO_QUERY_FAIL);
        }
        stringBuilder.append(getServiceType(parentIdOrder, jdhSkuDto));
        String extend = childOrder.getExtend();
        JdOrderExtendVo jdOrderExtendVo = new JdOrderExtendVo();
        if(StringUtils.isNotBlank(extend)) {
            jdOrderExtendVo = JSON.parseObject(extend, JdOrderExtendVo.class);
        }
        jdOrderExtendVo.setSkuServiceType(getServiceType(parentIdOrder, jdhSkuDto));
        childOrder.setExtend(JsonUtil.toJSONString(jdOrderExtendVo));

        copyJdOrderExtend(childOrder,parentIdOrder);
        String isImmediatelyFlag = CommonConstant.ZERO_STR;
        JdOrderExt jdOrderExt = jdOrderExtRepository.findJdOrderExtDetail(parentIdOrder.getOrderId(), JdOrderExtTypeEnum.APPOINTMENT_INFO.getExtType());
        if(Objects.nonNull(jdOrderExt)){
            String extContext = jdOrderExt.getExtContext();
            log.info("[SelfHomeHandler->dealVerticalCodeAndServiceType] extContext={}",extContext);
            if(StringUtils.isNotBlank(extContext)){
                OrderAppointmentInfoValueObject orderAppointmentInfoValueObject = JsonUtil.parseObject(extContext,OrderAppointmentInfoValueObject.class);
                if(Objects.nonNull(orderAppointmentInfoValueObject) && orderAppointmentInfoValueObject.getAppointmentTime().getIsImmediately()){
                    isImmediatelyFlag = CommonConstant.ONE_STR;
                }
            }
        }

        copyParentExt(childOrder,parentIdOrder,jdOrderExt);
        stringBuilder.append(isImmediatelyFlag);
        ServiceHomeTypeEnum serviceHomeTypeEnum = ServiceHomeTypeEnum.getServiceHomeTypeEnum(stringBuilder.toString());
        log.info("[SelfHomeHandler->dealVerticalCodeAndServiceType] stringBuilder={}, serviceHomeTypeEnum={}", stringBuilder.toString(), JSON.toJSONString(serviceHomeTypeEnum));
        if(Objects.nonNull(serviceHomeTypeEnum)){
            childOrder.setVerticalCode(serviceHomeTypeEnum.getVerticalCode());
            childOrder.setServiceType(serviceHomeTypeEnum.getServiceType());
            parentIdOrder.setVerticalCode(serviceHomeTypeEnum.getVerticalCode());
            parentIdOrder.setServiceType(serviceHomeTypeEnum.getServiceType());
        }
    }

    private Integer getServiceType(JdOrder parentIdOrder, JdhSkuDto jdhSkuDto){
        String extend = parentIdOrder.getExtend();
        JdOrderExtendVo jdOrderExtendVo = new JdOrderExtendVo();
        if (StringUtils.isNotBlank(extend)) {
            jdOrderExtendVo = JSON.parseObject(extend, JdOrderExtendVo.class);
        }
        if(Objects.nonNull(jdOrderExtendVo.getServiceUpgradeSelected()) && jdOrderExtendVo.getServiceUpgradeSelected()){
            return ServiceTypeNewEnum.ANGEL_TEST.getType();
        }
        return jdhSkuDto.getServiceType();
    }

    private void copyJdOrderExtend(JdOrder childOrder,JdOrder parentIdOrder){
        JdOrderExtendVo childExtendVo = new JdOrderExtendVo();
        if(StringUtils.isNotBlank(childOrder.getExtend())) {
            childExtendVo = JSON.parseObject(childOrder.getExtend(), JdOrderExtendVo.class);
        }
        if(StringUtils.isNotBlank(parentIdOrder.getExtend())) {
            JdOrderExtendVo parentExtendVo = JSON.parseObject(parentIdOrder.getExtend(), JdOrderExtendVo.class);
            if(StringUtils.isNotEmpty(parentExtendVo.getChannelName())){
                childExtendVo.setChannelName(parentExtendVo.getChannelName());
            }
            if(Objects.nonNull(parentExtendVo.getServiceUpgradeSelected())){
                childExtendVo.setServiceUpgradeSelected(parentExtendVo.getServiceUpgradeSelected());
            }
            if(Objects.nonNull(parentExtendVo.getSaleChannelId())){
                childExtendVo.setSaleChannelId(parentExtendVo.getSaleChannelId());
            }
        }
        childOrder.setExtend(JsonUtil.toJSONString(childExtendVo));
    }

    /**
     *
     * @param childOrder
     * @param parentIdOrder
     */
    private void copyParentExt(JdOrder childOrder,JdOrder parentIdOrder,JdOrderExt jdOrderExt){
        if(!childOrder.getOrderId().equals(parentIdOrder.getOrderId())){
            List<JdOrderExt> jdOrderExtList = Lists.newArrayList();
            if(CollUtil.isNotEmpty(childOrder.getJdOrderExtList())){
                jdOrderExtList.addAll(childOrder.getJdOrderExtList());
            }
            if(Objects.nonNull(jdOrderExt)){
                jdOrderExt.setOrderId(childOrder.getOrderId());
                jdOrderExt.setCreateTime(new Date());
                jdOrderExt.setUpdateTime(new Date());
                jdOrderExtList.add(jdOrderExt);
            }
            JdOrderExt jdOrderExt2 = jdOrderExtRepository.findJdOrderExtDetail(parentIdOrder.getOrderId(), JdOrderExtTypeEnum.SERVICE_FEE_INFO.getExtType());
            if(Objects.nonNull(jdOrderExt2)){
                jdOrderExt2.setOrderId(childOrder.getOrderId());
                jdOrderExt2.setCreateTime(new Date());
                jdOrderExt2.setUpdateTime(new Date());
                jdOrderExtList.add(jdOrderExt2);
            }
            childOrder.setJdOrderExtList(jdOrderExtList);
        }
    }
}
