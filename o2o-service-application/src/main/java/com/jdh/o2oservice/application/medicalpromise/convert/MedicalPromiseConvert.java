package com.jdh.o2oservice.application.medicalpromise.convert;

import com.alibaba.fastjson.JSON;
import com.jd.health.xfyl.merchant.export.param.AppointmentServiceItemParam;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.common.enums.MedicalPromiseFlagEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.medpromise.context.*;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseReportEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseFull;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseSku;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseSpecimenCode;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseEsQuery;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseFreezeInfoQuery;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicatorQueryPageBo;
import com.jdh.o2oservice.core.domain.report.model.ReportCenterReport;
import com.jdh.o2oservice.export.medicalpromise.cmd.*;
import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseDeliveryStepDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseConditionDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseFullDTO;
import com.jdh.o2oservice.export.medicalpromise.query.LabQueryMedPromisePageRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseBindRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.report.cmd.MedicalReportIndicatorFlushToEsCmd;
import com.jdh.o2oservice.export.report.cmd.MedicalReportSaveCmd;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 履约检测单Convert
 * @Interface: MedicalPromiseConvert
 * @Author: wangpengfei144
 * @Date: 2024/4/15
 */
@Mapper
public interface MedicalPromiseConvert {

    /**
     * 初始化
     */
    MedicalPromiseConvert INSTANCE = Mappers.getMapper(MedicalPromiseConvert.class);


    /**
     * cmd2CreateContext
     * @param cmd
     * @return
     */
    MedicalPromiseCreateContext cmd2CreateContext(MedicalPromiseCreateCmd cmd);

    MedicalPromiseEventBody medicalPromise2MedicalPromiseEventBody(MedicalPromise medicalPromise);
    /**
     * cmd2MedicalPromiseBindContext
     * @param cmd
     * @return
     */
    MedicalPromiseBatchBindSpecimenCodeContext cmd2MedicalPromiseBindContext(MedicalPromiseBindSpecimenCodeCmd cmd);

    /**
     * jdhSku2MedicalPromiseSku
     * @param jdhSkuDtoMap
     * @return
     */
    Map<Long, MedicalPromiseSku> jdhSku2MedicalPromiseSku(Map<Long, JdhSkuDto> jdhSkuDtoMap);

    /**
     * 转化
     * @param medicalPromiseDispatchCmd medicalPromiseDispatchCmd
     * @return return
     */
    MedicalPromiseDispatchContext convert(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd);


    /**
     * 转化
     * @param medicalPromiseListRequest
     * @return
     */
    MedicalPromiseListQuery convert(MedicalPromiseListRequest medicalPromiseListRequest);

    /**
     * batchInvalidCmd2ListQuery
     * @param cmd
     * @return
     */
    @Mapping(source = "medicalPromiseStatusInfoCmds" ,target = "invalidList")
    MedicalPromiseBatchInvalidContext batchInvalidCmd2MedicalPromiseBatchInvalidContext(MedicalPromiseBatchInvalidCmd cmd);

    /**
     * 转化
     * @param medicalPromise
     * @return
     */
    @Mapping(source = "flag",target = "flagDesc",qualifiedByName = "getFlagDesc")
    @Mapping(source = "deliveryStepFlow",target = "deliveryStepFlow",qualifiedByName = "deliveryStepFlow")
    MedicalPromiseDTO convert(MedicalPromise medicalPromise);

    /**
     * 转化
     * @param medicalPromises
     * @return
     */
    List<MedicalPromiseDTO> convert(List<MedicalPromise> medicalPromises);

    /**
     * convert
     * @param medicalPromisePageDto
     * @return
     */
    PageDto<MedicalPromiseDTO> convert(PageDto<MedicalPromise> medicalPromisePageDto);

    /**
     * 转化
     * @param medicalPromiseCallbackCmd
     * @return
     */
    MedicalPromise convert(MedicalPromiseCallbackCmd medicalPromiseCallbackCmd);

    /**
     * 转化
     * @param medicalPromiseBindRequest
     * @return
     */
    MedicalPromiseListQuery convert(MedicalPromiseBindRequest medicalPromiseBindRequest);

    /**
     * convertCondition
     * @param medicalPromises
     * @return
     */
    List<MedicalPromiseConditionDTO> convertCondition(List<MedicalPromise> medicalPromises);

    /**
     * convertCondition
     * @param medicalPromise
     * @return
     */
    MedicalPromiseConditionDTO convertCondition(MedicalPromise medicalPromise);

    /**
     * 转化
     * @param m
     * @return
     */
    @Mapping(source = "medicalPromiseStatusInfoCmds" ,target = "medicalPromiseNestedQueries")
    MedicalPromiseListQuery convert(MedicalPromiseStatusCmd m);

    /**
     * 转化
     * @param m
     * @return
     */
    MedicalPromiseFreezeInfoQuery convert(MedicalPromiseStatusInfoCmd m);

//    /**
//     * 转化
//     * @param m
//     * @return
//     */
//    List<MedicalPromiseFreezeInfoQuery> convert(List<MedicalPromiseStatusInfoCmd> m);

    /**
     * 转化
     * @param batchMedicalPromiseSubmitCmd m
     * @return r
     */
    MedicalPromiseListQuery convertSubmit(BatchMedicalPromiseSubmitCmd batchMedicalPromiseSubmitCmd);

    /**
     * convertList
     * @param medicalPromises
     * @return
     */
    List<MedicalPromiseSubmitContext> convertList(List<MedicalPromise> medicalPromises);

    @Mapping(source = "specimenCode" ,target = "specimenCode",qualifiedByName = "toUppercase")
    MedicalPromiseSpecimenCode convert(MedicalPromiseCmdSpecimenCode medicalPromiseCmdSpecimenCode);

    MedicalReportIndicatorQueryPageBo convert(MedicalReportIndicatorFlushToEsCmd medicalReportIndicatorFlushToEsCmd);


    /**
     * convertMedicalPromiseSubmitContext
     * @param medicalPromise
     * @param promisePatientDto
     * @return
     */
    default MedicalPromiseSubmitContext convertMedicalPromiseSubmitContext(MedicalPromise medicalPromise, PromisePatientDto promisePatientDto){
        if ( medicalPromise == null) {
            return null;
        }
        MedicalPromiseSubmitContext medicalPromiseSubmitContext = convertTo(medicalPromise);
        medicalPromiseSubmitContext.setUserName(promisePatientDto.getUserName().getName());
        medicalPromiseSubmitContext.setUserBirth(promisePatientDto.getBirthday().getBirth());
        medicalPromiseSubmitContext.setUserAge(promisePatientDto.getBirthday().getAge());
        medicalPromiseSubmitContext.setUserGender(promisePatientDto.getGender());
        medicalPromiseSubmitContext.setUserMarriage(promisePatientDto.getMarriage());
        medicalPromiseSubmitContext.setUserPhone(promisePatientDto.getPhoneNumber().getPhone());
        return medicalPromiseSubmitContext;
    }

    /**
     *
     * @param medicalPromises
     * @param patientToDto
     * @return
     */
    default List<MedicalPromiseSubmitContext> convertList(List<MedicalPromise> medicalPromises, Map<Long, PromisePatientDto> patientToDto ){
        if ( medicalPromises == null ) {
            return null;
        }

        List<MedicalPromiseSubmitContext> list = new ArrayList<MedicalPromiseSubmitContext>( medicalPromises.size() );
        for ( MedicalPromise medicalPromise : medicalPromises ) {
            PromisePatientDto promisePatientDto = patientToDto.get(medicalPromise.getPromisePatientId());
            if (Objects.isNull(promisePatientDto)){
                //检测人信息为空
                throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_PERSON_INFO_NULL);
            }
            list.add( convertMedicalPromiseSubmitContext(medicalPromise, promisePatientDto) );
        }

        return list;
    }






    /**
     * convert
     * @param medicalPromiseHandCmd
     * @return
     */
    MedicalPromise convert(MedicalPromiseHandCmd medicalPromiseHandCmd);


    default MedicalPromiseSubmitContext convertTo(MedicalPromise medicalPromise){
        MedicalPromiseSubmitContext medicalPromiseSubmitContext = new MedicalPromiseSubmitContext();

        medicalPromiseSubmitContext.setPromisePatientId(medicalPromise.getPromisePatientId());
        medicalPromiseSubmitContext.setUserPin(medicalPromise.getUserPin());
        medicalPromiseSubmitContext.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        if (medicalPromise.getStationId() != null) {
            medicalPromiseSubmitContext.setStationId(String.valueOf(medicalPromise.getStationId()));
        }
        medicalPromiseSubmitContext.setProviderId(medicalPromise.getProviderId());
        medicalPromiseSubmitContext.setServiceId(medicalPromise.getServiceId());
        medicalPromiseSubmitContext.setOuterId(medicalPromise.getOuterId());
        if (medicalPromise.getVersion() != null) {
            medicalPromiseSubmitContext.setVersion(String.valueOf(medicalPromise.getVersion()));
        }
        medicalPromiseSubmitContext.setSampleBarcodeList(Lists.newArrayList(medicalPromise.getSpecimenCode()));
        List<AppointmentServiceItemParam> appointmentServiceItemParams = Lists.newArrayList();
        AppointmentServiceItemParam appointmentServiceItemParam = new AppointmentServiceItemParam();
        appointmentServiceItemParam.setItemId(medicalPromise.getServiceItemId());
        appointmentServiceItemParam.setItemName(medicalPromise.getServiceItemName());
        appointmentServiceItemParam.setSampleBarcode(medicalPromise.getSpecimenCode());
        appointmentServiceItemParams.add(appointmentServiceItemParam);
        medicalPromiseSubmitContext.setServiceItemList(appointmentServiceItemParams);
        return medicalPromiseSubmitContext;
    }

    /**
     * convert
     * @param jdhStationServiceItemRelDto
     * @return
     */
    JdhServiceItemContext convert(JdhStationServiceItemRelDto jdhStationServiceItemRelDto);

    /**
     * convertRel
     * @param jdhStationServiceItemRelDto
     * @return
     */
    @Mapping(source = "storePhone",target = "stationPhone")
    JdhStationServiceItemRelContext convertRel(JdhStationServiceItemRelDto jdhStationServiceItemRelDto);

    /**
     * convert
     * @param medicalPromiseRequest
     * @return
     */
    MedicalPromiseRepQuery convert(MedicalPromiseRequest medicalPromiseRequest);

    /**
     * convert
     * @param medicalPromiseReportCmd
     * @return
     */
    MedicalPromiseReportEventBody convert(MedicalPromiseReportCmd medicalPromiseReportCmd);

    /**
     * convert
     * @param medicalPromiseReportEventBody
     * @return
     */
    MedicalPromiseReportCmd convert(MedicalPromiseReportEventBody medicalPromiseReportEventBody);

    /**
     * convert
     * @param labQueryMedPromisePageRequest
     * @return
     */
    MedicalPromiseEsQuery convert(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest);

    /**
     *
     * @param medicalPromiseFullPageDto
     * @return
     */
    PageDto<MedicalPromiseFullDTO> convertFull(PageDto<MedicalPromiseFull> medicalPromiseFullPageDto);
    /**
     *
     * @param medicalPromiseFullPageDto
     * @return
     */
    MedicalPromiseFullDTO convertFull(MedicalPromiseFull medicalPromiseFullPageDto);

    @Named("getFlagDesc")
    default String getFlagDesc(String flag){
        return MedicalPromiseFlagEnum.getDesc(flag);
    }

    @Named("deliveryStepFlow")
    default List<MedPromiseDeliveryStepDTO> deliveryStepFlow(String flag){
        if (StringUtils.isBlank(flag)) {
            return null;
        }
        return JSON.parseArray(flag, MedPromiseDeliveryStepDTO.class);
    }

    @Named("toUppercase")
    default String toUppercase(String str){
        if (StringUtil.isBlank(str)){
            return null;
        }
        return str.trim().toUpperCase();
    }

    @Mapping(source = "reportId", target = "medicalReportId")
    ReportCenterReport medicalReportSaveCmd2ReportCenterReport(MedicalReportSaveCmd medicalReportSaveCmd);

}
