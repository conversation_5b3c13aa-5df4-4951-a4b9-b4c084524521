
package com.jdh.o2oservice.application.report.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.EnvTypeEnum;
import com.jdh.o2oservice.base.enums.IndicatorTemplateEnum;
import com.jdh.o2oservice.base.enums.PartnerSourceEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.NumberUtils;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.report.ReportErrorCode;
import com.jdh.o2oservice.core.domain.report.bo.MedicalReportQueryBO;
import com.jdh.o2oservice.core.domain.report.bo.MedicineBO;
import com.jdh.o2oservice.core.domain.report.bo.MedicineProgramDetailBO;
import com.jdh.o2oservice.core.domain.report.bo.MedicineProgramGroupBO;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.repository.db.QuickDrugConfigRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.QuickDrugConfigRepository2;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SickLeaveExportRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.QuerySickCertBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.QuerySickCertParam;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.netrx.bo.DpRelationCheckBo;
import com.jdh.o2oservice.core.domain.support.netrx.bo.RxDetailBo;
import com.jdh.o2oservice.core.domain.support.netrx.bo.RxInfoBo;
import com.jdh.o2oservice.core.domain.support.userinfo.bo.UserBaseInfoBo;
import com.jdh.o2oservice.core.domain.trade.bo.InspectSheetInfoBO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseHistoryDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedPromiseHistoryRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.ServiceIndicatorDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.JdhIndicatorQuery;
import com.jdh.o2oservice.export.product.query.ServiceItemConditionQuery;
import com.jdh.o2oservice.export.report.dto.*;
import com.jdh.o2oservice.export.report.query.MedicalReportOrderRequest;
import com.jdh.o2oservice.export.report.query.OuterOrderRequest;
import com.jdh.o2oservice.export.support.dto.UserMarketDTO;
import com.jdh.o2oservice.export.support.query.UserMarketRequest;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.query.OrderDetailParam;
import com.jdh.trade.basis.base.exception.ParamException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.BooleanSupplier;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode.JDH_PROMISE_NOT_EXIST;
import static com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode.ORDER_NOT_EXIST;

/**
 * @Description: 订单报告详情编排执行容器
 * <AUTHOR>
 * @Date 2024/9/19
 * @Version V1.0
 **/
@Slf4j
public class MedicalReportContainer implements AutoCloseable {

    /**
     * 上下文对象
     * <p>
     * 使用上下文对象注意！！！ 用 try()-->>  统一释放内存对象
     */
    private static final ThreadLocal<ReportData> data = ThreadLocal.withInitial(ReportData::new);

    /**
     * 提供外部获取上下文对象
     *
     * @return
     */
    public ThreadLocal<ReportData> getContextData() {
        return this.data;
    }

    /**
     * 初始化入参参数
     *
     * @param supplier
     * @return
     */
    public MedicalReportContainer initBaseParam(BooleanSupplier supplier) {
        supplier.getAsBoolean();
        return this;
    }

    /**
     * 订单查询
     *
     * @param func
     * @return
     */
    public MedicalReportContainer initOrder(Function<OrderDetailParam, JdOrderDTO> func) {
        MedicalReportOrderRequest request = data.get().getRequest();
        if (request == null) {
            log.error("exception.initOrder.data:{}", JSON.toJSONString(data.get()));
            throw new ParamException(-1, "参数异常");
        }
        if (StringUtil.isBlank(request.getOrderId()) && StringUtils.equals(request.getDomainCode(), "promise") && StringUtils.equals(request.getAggregateCode(), "promise")) {
            return this;
        }
        if (Objects.nonNull(data.get().jdOrder)) {
            return this;
        }
        OrderDetailParam param = OrderDetailParam.builder()
                .orderId(request.getOrderId()).pin(request.getUserPin()).build();
        JdOrderDTO order = func.apply(param);
        log.info("MedicalReportContainer.initOrder.param:{},result:{}", JSON.toJSONString(param), JSON.toJSONString(order));
        if (order == null) {
            throw new BusinessException(ORDER_NOT_EXIST);
        }
        //互医的检验单不需要 patientId 过滤   //互医和家医检验单，只会对应一个患者
        if (request.getPatientId() != null && (PartnerSourceEnum.JDH_NETDIAG.getCode().equals(order.getPartnerSource()) || PartnerSourceEnum.JDH_HOMEDIAG.getCode().equals(order.getPartnerSource()))) {
            request.setPatientId(null);
        }
        data.get().setJdOrder(order);
        return this;
    }

    /**
     * 履约单查询
     *
     * @param func
     * @return
     */
    public MedicalReportContainer initPromise(Function<PromiseRepQuery, JdhPromise> func) {
        long startTime = System.currentTimeMillis();
        log.info("MedicalReportContainer.initPromise.start,startTime={}", String.valueOf(startTime));
        if (Objects.nonNull(data.get().getJdhPromise())) {
            return this;
        }
        MedicalReportOrderRequest request = data.get().getRequest();
        if (StringUtils.equals(request.getDomainCode(), "promise") && StringUtils.equals(request.getAggregateCode(), "promise")) {
            if (request.getPromiseId() == null) {
                throw new ParamException(-1, "参数异常 promiseId不存在");
            }
            PromiseRepQuery queryParam = PromiseRepQuery.builder().promiseId(request.getPromiseId()).userPin(request.getUserPin()).build();
            JdhPromise promise = func.apply(queryParam);
            log.info("MedicalReportContainer.initPromise.param:{},result:{}", JSON.toJSONString(queryParam), JSON.toJSONString(promise));
            if (promise == null) {
                throw new BusinessException(JDH_PROMISE_NOT_EXIST);
            }
            data.get().setJdhPromise(promise);
            return this;
        }
        JdOrderDTO jdOrder = data.get().getJdOrder();
        if (jdOrder == null) {
            log.error("exception.initPromise.data:{}", JSON.toJSONString(data.get()));
            throw new BusinessException(ORDER_NOT_EXIST);
        }
        String sourceVoucherId = Objects.nonNull(jdOrder.getParentId()) && jdOrder.getParentId() > 0 ? jdOrder.getParentId().toString() : jdOrder.getOrderId().toString();
        PromiseRepQuery queryParam = PromiseRepQuery.builder().sourceVoucherId(sourceVoucherId).userPin(jdOrder.getUserPin()).build();
        JdhPromise promise = func.apply(queryParam);
        log.info("MedicalReportContainer.initPromise.param:{},result:{}", JSON.toJSONString(queryParam), JSON.toJSONString(promise));
        if (promise == null) {
            throw new BusinessException(JDH_PROMISE_NOT_EXIST);
        }
        data.get().setJdhPromise(promise);
        long endTime = System.currentTimeMillis();
        return this;
    }

    /**
     * 提供并发执行函数
     *
     * @param future
     * @return
     */
    public void initFutureFunc(CompletableFuture... future) {
        long startTime = System.currentTimeMillis();
        log.info("MedicalReportContainer.initFutureFunc.start,startTime={}", String.valueOf(startTime));
        try {
            List<CompletableFuture> nonNullFutures = Arrays.stream(future).filter(Objects::nonNull).collect(Collectors.toList());
            CompletableFuture[] array = nonNullFutures.toArray(new CompletableFuture[0]);
            CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(array);
//            combinedFuture.get()
//            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(future);
//            voidCompletableFuture.
            combinedFuture.get(1000L, TimeUnit.MILLISECONDS);
        } catch (BusinessException e) {
            log.error("exception.initFutureFunc.data:{}", JSON.toJSONString(data.get().getMedicalPromiseList()));
            throw new BusinessException(JDH_PROMISE_NOT_EXIST);
        } catch (Exception e) {
            log.error("exception.initFutureFunc.data:{}", JSON.toJSONString(data.get().getMedicalPromiseList()));
            log.error("MedicalReportContainer.initFutureFunc.exception:{}", e);
        }
        long endTime = System.currentTimeMillis();
        log.info("MedicalReportContainer.initFutureFunc,endTime={},用时={}", String.valueOf(endTime), String.valueOf(endTime - startTime));
    }

    /**
     * 查询检测单
     *
     * @param func
     * @return
     */
    public MedicalReportContainer medicalPromiseList(Function<MedicalPromiseListRequest, List<MedicalPromiseDTO>> func, ReportData contextData) {
        log.info("MedicalReportContainer->medicalPromiseList,start");
        JdhPromise promise = contextData.getJdhPromise();
        if (promise == null) {
            log.error("exception.medicalPromiseList.empty");
            throw new BusinessException(JDH_PROMISE_NOT_EXIST);
        }
        Long patientId = contextData.getRequest().getPatientId();
        MedicalPromiseListRequest medicalPromiseReq = MedicalPromiseListRequest.builder()
                .promiseIdList(Arrays.asList(promise.getPromiseId()))
                .invalid(false).build();

        if (Objects.nonNull(patientId)) {
            List<Long> promisepatientIdList = contextData.getJdhPromise().getPatients().stream().filter(p -> Objects.equals(patientId, p.getPatientId())).map(JdhPromisePatient::getPromisePatientId).collect(Collectors.toList());
            medicalPromiseReq.setPromisePatientIdList(promisepatientIdList);
            medicalPromiseReq.setPatientDetail(true);
        }

        List<MedicalPromiseDTO> list = func.apply(medicalPromiseReq);
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException(JDH_PROMISE_NOT_EXIST);
        }
        //只看结构化的检测单
        list.removeIf(p->!Objects.equals(ReportShowTypeEnum.STRUCT.getType(),p.getReportShowType()));
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException(JDH_PROMISE_NOT_EXIST);
        }
        contextData.setMedicalPromiseList(list);
        log.info("MedicalReportContainer.medicalPromiseList.filter.param:{},result:{}", JSON.toJSONString(medicalPromiseReq), JSON.toJSONString(list));
        return this;
    }

    /**
     * 查询检测单历史
     *
     * @param func
     * @return
     */
    public MedicalReportContainer medicalPromiseHistoryList(Function<MedPromiseHistoryRequest, List<MedPromiseHistoryDTO>> func, ReportData contextData) {
        List<MedicalPromiseDTO> medicalPromiseList = contextData.getMedicalPromiseList();
        if (CollectionUtils.isEmpty(medicalPromiseList)) {
            log.info("exception.medicalPromiseHistoryList.empty");
            return this;
        }
        Set<Long> medicalPromiseIdSet = medicalPromiseList.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toSet());
        MedPromiseHistoryRequest promiseHistoryReq = MedPromiseHistoryRequest.builder().medicalPromiseIds(medicalPromiseIdSet).build();
        List<MedPromiseHistoryDTO> historyList = func.apply(promiseHistoryReq);
        log.info("MedicalReportContainer.medicalPromiseHistoryList.param:{},result:{}", JSON.toJSONString(promiseHistoryReq), JSON.toJSONString(historyList));
        contextData.setPromiseHistoryList(historyList);
        return this;
    }

    /**
     * 查询服务单明细
     *
     * @param func
     * @return
     */
    public MedicalReportContainer serviceItemList(Function<ServiceItemConditionQuery, List<ServiceItemDto>> func, ReportData contextData) {
        List<MedicalPromiseDTO> medicalPromiseList = contextData.getMedicalPromiseList();
        if (CollectionUtils.isEmpty(medicalPromiseList)) {
            log.error("exception.serviceItemList.empty");
            throw new BusinessException(JDH_PROMISE_NOT_EXIST);
        }
        Set<Long> itemSetIds = medicalPromiseList.stream().filter(p -> StringUtils.isNotEmpty(p.getServiceItemId())).map(p -> Long.parseLong(p.getServiceItemId())).collect(Collectors.toSet());
        ServiceItemConditionQuery serviceItemReq = ServiceItemConditionQuery.builder()
                .itemIds(itemSetIds).indicatorQuery(Boolean.TRUE).build();
        List<ServiceItemDto> apply = func.apply(serviceItemReq);
        contextData.setServiceItemList(apply);
        log.info("MedicalReportContainer.serviceItemList.param:{},result:{}", JSON.toJSONString(serviceItemReq), JSON.toJSONString(apply));
        return this;
    }

    /**
     * 查询报告单
     *
     * @param func
     * @return
     */
    public MedicalReportContainer reportList(Function<List<Long>, List<MedicalReport>> func, ReportData contextData) {
        List<MedicalPromiseDTO> medicalPromiseList = contextData.getMedicalPromiseList();
        if (CollectionUtils.isEmpty(medicalPromiseList)) {
            log.info("exception.reportList.empty");
            return this;
        }
        List<Long> medicalPromiseIdList = medicalPromiseList.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList());
        List<MedicalReport> reportList = func.apply(medicalPromiseIdList);
        log.info("MedicalReportContainer.reportList.param:{},result:{}", JSON.toJSONString(medicalPromiseIdList), JSON.toJSONString(reportList));
        MedicalReportOrderRequest request = data.get().getRequest();
        if (request != null && request.getPatientId() != null) {
            reportList = reportList.stream().filter(p -> request.getPatientId().equals(p.getPatientId())).collect(Collectors.toList());
        }
        contextData.setReportList(reportList);
        return this;
    }

    /**
     * 查询检验单详情
     *
     * @param func
     * @return
     */
    public MedicalReportContainer inspectSheet(BiFunction<String, Long, InspectSheetInfoBO> func, ReportData contextData) {
        log.info("MedicalReportContainer->inspectSheet,start");
        if (Boolean.TRUE.equals(contextData.getDoctorView())) {
            return this;
        }
        JdOrderDTO jdOrder = contextData.getJdOrder();
        if (jdOrder == null) {
            log.info("exception.inspectSheet.empty");
            return this;
        }

        if (PartnerSourceEnum.fromCode(jdOrder.getPartnerSource()) == PartnerSourceEnum.JDH_NETDIAG
                || PartnerSourceEnum.fromCode(jdOrder.getPartnerSource()) == PartnerSourceEnum.JDH_HOMEDIAG) {
            InspectSheetInfoBO sheetInfoBO = func.apply(jdOrder.getUserPin(), Long.parseLong(jdOrder.getPartnerSourceOrderId()));
            log.info("MedicalReportContainer.inspectSheet.pin:{},PartnerSourceOrderId:{},result:{}", jdOrder.getUserPin(), jdOrder.getPartnerSourceOrderId(), JSON.toJSON(sheetInfoBO));
            contextData.setInspectSheetInfoBO(sheetInfoBO);
        }
        log.info("MedicalReportContainer->inspectSheet,end");
        return this;
    }

    /**
     * 根据用户Pin和固定值2，通过BiFunction获取用户基本信息并设置到ReportData中。
     *
     * @param func 用于获取用户基本信息的函数接口
     * @return 当前对象，方便链式调用
     */
    public MedicalReportContainer userInfo(BiFunction<String, Integer, UserBaseInfoBo> func, ReportData contextData) {
        log.info("MedicalReportContainer->userInfo,start");
        String userPin = contextData.getRequest().getUserPin();
        UserBaseInfoBo apply = func.apply(userPin, CommonConstant.TWO);
        contextData.setUserBaseInfoBo(apply);
        log.info("MedicalReportContainer->userInfo,apply={}", JSON.toJSONString(apply));
        return this;
    }

    /**
     * 获取默认链接
     *
     * @return
     */
    public ReportDoctorDTO getDefaultReportDoctorDTO() {
        ReportDoctorDTO reportDoctorDTO = new ReportDoctorDTO();
        String jsonConfig = data.get().getDuccConfig().getReportDoctorConfig();
        if (StringUtils.isNotBlank(jsonConfig)) {
            reportDoctorDTO = JSON.parseObject(jsonConfig, ReportDoctorDTO.class);
        } else {
            //固定头像、标题、内容说明、问诊链接（若本期无法实现，则配置固定极速问诊链接）
            reportDoctorDTO.setPicUrl("https://jkimg10.360buyimg.com/pop/jfs/t1/230298/36/17694/33798/664d6503F44f51cb5/56128f58c96f2d4a.png");
            reportDoctorDTO.setTitle("京东自营  在线问诊");
            reportDoctorDTO.setIntroduction("京东医生24小时在线，30秒响应");
            reportDoctorDTO.setButtonName("问医生");
            reportDoctorDTO.setLink("https://m.healthjd.com/s/preInquiryNew?quickAnswer=true&sourceId=1&hy_entry=HomeInspection_ResultQuick17");

        }
        return reportDoctorDTO;
    }

    /**
     * 获取报告单oss Url解析
     *
     * @param fileManageService
     * @param executorService
     * @return
     */
    public MedicalReportContainer reportFileDownload(FileManageService fileManageService, ExecutorService executorService, Cluster jimClient) {

        List<MedicalReport> reportList = data.get().getReportList();
        if (CollectionUtils.isEmpty(reportList)) {
            log.info("exception.reportFileDownload.empty");
            return this;
        }
        Map<String, StructQuickReportContentDTO> fileList = new HashMap<>();
        List<String> ossUrl = reportList.stream().filter(p -> StringUtils.isNotEmpty(p.getStructReportOss())).map(MedicalReport::getStructReportOss).collect(Collectors.toList());

        //如果报告少，则遍历查询，因为在报告少的时候线程开销相对较大
        if (ossUrl.size() <= CommonConstant.THREE) {
            for (String url : ossUrl) {
                InputStream inputStream = fileManageService.get(url);
                String structReportJson = new BufferedReader(new InputStreamReader(inputStream)).lines().collect(Collectors.joining("\n"));
                if (StringUtils.isBlank(structReportJson)) {
                    return null;
                }
                StructQuickReportContentDTO reportContentDTO = JSON.parseObject(structReportJson, StructQuickReportContentDTO.class);
                log.info("reportFileDownload->for 循环下载 getInputStream,jsonEnd={}", System.currentTimeMillis());
                fileList.put(url, reportContentDTO);
                String structReportKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STRUCT_REPORT_CACHE_KEY, url);
                jimClient.setEx(structReportKey, structReportJson, 5L, TimeUnit.MINUTES);
                try {
                    inputStream.close();
                }catch (Exception e){
                    log.error("reportFileDownload.close,exception:{}", e);
                }


            }
        } else {
            CompletableFuture[] futures = new CompletableFuture[ossUrl.size()];
            for (int i = 0; i < ossUrl.size(); i++) {
                String url = ossUrl.get(i);
                futures[i] = CompletableFuture.supplyAsync(() -> {
                    InputStream inputStream = fileManageService.get(url);
                    String structReportJson = new BufferedReader(new InputStreamReader(inputStream)).lines().collect(Collectors.joining("\n"));
                    log.info("reportFileDownload->getInputStream,jsonStrEnd={}", System.currentTimeMillis());
                    if (StringUtils.isBlank(structReportJson)) {
                        return null;
                    }
                    StructQuickReportContentDTO reportContentDTO = JSON.parseObject(structReportJson, StructQuickReportContentDTO.class);
                    log.info("reportFileDownload->getInputStream,jsonEnd={}", System.currentTimeMillis());
                    fileList.put(url, reportContentDTO);
                    String structReportKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STRUCT_REPORT_CACHE_KEY, url);
                    jimClient.setEx(structReportKey, structReportJson, 5L, TimeUnit.MINUTES);
                    try {
                        inputStream.close();
                    }catch (Exception e){
                        log.error("reportFileDownload.close,exception:{}", e);
                    }
                    return null;
                }, executorService);
            }
            try {
                CompletableFuture.allOf(futures).get(3000L, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.error("exception.reportFileDownload.data:{}", JSON.toJSONString(data.get().getReportList()));
                log.error("fileReportDownload.exception:{}", e);
            }
        }

        data.get().setFileList(fileList);
        long endTime = System.currentTimeMillis();
        return this;
    }

    /**
     * 查询异常项
     *
     * @param func
     * @return
     */
    public MedicalReportContainer indicator(Function<JdhIndicatorQuery, List<ServiceIndicatorDto>> func) {
        Map<String, StructQuickReportContentDTO> fileList = data.get().getFileList();
        if (fileList == null || fileList.isEmpty()) {
            log.info("exception.indicator.empty");
            return this;
        }
        Set<Long> indicatorIdSet = new HashSet<>();
        fileList.entrySet().forEach(p -> {
            List<StructQuickReportResultDTO> reportResult = p.getValue().getReportResult();
            if (CollectionUtils.isEmpty(reportResult)) {
                return;
            }
            reportResult.forEach(q -> {
                List<StructQuickReportResultIndicatorDTO> indicators = q.getIndicators();
                if (CollectionUtils.isEmpty(indicators)) {
                    return;
                }
                indicators.stream().filter(k -> k.getIndicatorNo() != null && StringUtils.isNumeric(k.getIndicatorNo())).forEach(k -> {
                    indicatorIdSet.add(Long.parseLong(k.getIndicatorNo()));
                });
            });
        });
        if (indicatorIdSet.isEmpty()) {
            return this;
        }
        JdhIndicatorQuery jdhIndicatorQuery = new JdhIndicatorQuery();
        jdhIndicatorQuery.setIndicatorIds(indicatorIdSet);
        jdhIndicatorQuery.setIndicatorType(CommonConstant.TWO);
        List<ServiceIndicatorDto> indicatorList = func.apply(jdhIndicatorQuery);
        log.info("MedicalReportContainer.indicator.param:{},result:{}", JSON.toJSONString(jdhIndicatorQuery), JSON.toJSONString(indicatorList));
        data.get().setIndicatorList(indicatorList);
        return this;
    }


    /**
     * 构建检测项目
     *
     * @return
     */
    public MedicalReportContainer buildMedPromiseDetailList() {

        JdhPromise jdhPromise = data.get().getJdhPromise();
        List<MedPromiseHistoryDTO> promiseHistoryList = data.get().getPromiseHistoryList();
        List<MedicalPromiseDTO> medicalPromiseList = data.get().getMedicalPromiseList();
        List<ServiceIndicatorDto> indicatorList = data.get().getIndicatorList();
        List<ServiceItemDto> serviceItemList = data.get().getServiceItemList();
        JdOrderDTO jdOrder = data.get().getJdOrder();
        DuccConfig duccConfig = data.get().getDuccConfig();
        MedicalReportOrderRequest request = data.get().getRequest();


        Map<Long, ServiceItemDto> itemIdToObj = serviceItemList.stream().collect(Collectors.toMap(ServiceItemDto::getItemId, p -> p));
        Map<String, ServiceIndicatorDto> indicatorMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(indicatorList)) {
            indicatorMap = indicatorList.stream().collect(Collectors.toMap(ServiceIndicatorDto::getIndicatorId, p -> p));
        }
        Set<Long> medicalPromiseId = medicalPromiseList.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toSet());
        List<MedPromiseHistoryDTO> checkHistory = promiseHistoryList.stream().filter(p -> medicalPromiseId.contains(p.getMedicalPromiseId()) && Objects.equals(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), p.getAfterStatus())).collect(Collectors.toList());

        MedicalReportOrderDTO medicalReportOrderDTO = new MedicalReportOrderDTO();
        medicalReportOrderDTO.setPromiseId(jdhPromise.getPromiseId());
        if (CollectionUtils.isNotEmpty(checkHistory)) {
            checkHistory.sort(Comparator.comparing(MedPromiseHistoryDTO::getCreateTime));
            medicalReportOrderDTO.setCheckTime(checkHistory.get(0).getCreateTime());
        }
        if (Objects.nonNull(jdOrder)) {
            medicalReportOrderDTO.setOrderId(String.valueOf(jdOrder.getOrderId()));
            medicalReportOrderDTO.setOuterOrderId(jdOrder.getPartnerSourceOrderId());
        }

        //用户信息
        packUserInfo(jdhPromise, medicalReportOrderDTO);

        //总异常指标
        List<ReportIndicatorDTO> abnormalSummaryList = new ArrayList<>();
        //指标详情list
        List<PromiseMedicalReportDTO> promiseMedicalReportDtoList = Lists.newArrayList();
        //结构化报告
        Map<String, StructQuickReportContentDTO> fileList = data.get().getFileList();
        //报告列表
        List<MedicalReport> reportList = data.get().getReportList();
        Map<Long, MedicalReport> mpToObj = reportList.stream().collect(Collectors.toMap(MedicalReport::getMedicalPromiseId, p -> p));
        boolean allFinish = Boolean.TRUE;
        Integer indicatorNum = CommonConstant.ZERO;
        Boolean select = Boolean.FALSE;
        for (MedicalPromiseDTO medicalPromise : medicalPromiseList) {
            PromiseMedicalReportDTO promiseMedicalReportDTO = new PromiseMedicalReportDTO();
            promiseMedicalReportDTO.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
            promiseMedicalReportDTO.setStatus(medicalPromise.getStatus());
            promiseMedicalReportDTO.setCheckTime(medicalPromise.getCheckTime());
            promiseMedicalReportDTO.setServiceItemId(medicalPromise.getServiceItemId());
            promiseMedicalReportDTO.setServiceItemName(medicalPromise.getServiceItemName());
            promiseMedicalReportDtoList.add(promiseMedicalReportDTO);

            if (!MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(medicalPromise.getStatus())) {
                allFinish = Boolean.FALSE;
                ServiceItemDto serviceItemDto = itemIdToObj.get(Long.valueOf(medicalPromise.getServiceItemId()));
                promiseMedicalReportDTO.setAnomalyNum(CommonConstant.ZERO);
                promiseMedicalReportDTO.setIndicatorNum(CollectionUtils.isNotEmpty(serviceItemDto.getServiceIndicatorDtoList()) ? serviceItemDto.getServiceIndicatorDtoList().size() : CommonConstant.ZERO);
                continue;
            }

            MedicalReport medicalReport = mpToObj.get(medicalPromise.getMedicalPromiseId());
            promiseMedicalReportDTO.setReportCenterId(medicalReport.getReportCenterId());
            StructQuickReportContentDTO structQuickReportContentDTO = fileList.get(medicalReport.getStructReportOss());

            Boolean stationNew = Boolean.FALSE;
            if (CollectionUtils.isEmpty(duccConfig.getReportNewTemplateStation()) || duccConfig.getReportNewTemplateStation().contains(medicalPromise.getStationId())) {
                stationNew = Boolean.TRUE;
            }
            if (duccConfig.getReportNewTemplateSwitch() && Boolean.TRUE.equals(structQuickReportContentDTO.getNewTemplate()) && stationNew) {
                promiseMedicalReportDTO.setNewTemplate(Boolean.TRUE);
            } else {
                promiseMedicalReportDTO.setNewTemplate(Boolean.FALSE);
            }

            StructQuickReportBaseInfoDTO reportBasicInfo = structQuickReportContentDTO.getReportBasicInfo();
            List<StructQuickReportResultDTO> reportResult = structQuickReportContentDTO.getReportResult();
            //检测方法
            promiseMedicalReportDTO.setTestingMethod(reportBasicInfo.getTestingMethod());
            //样本性状
            promiseMedicalReportDTO.setSampleCharacteristics(reportBasicInfo.getSampleCharacteristics());
            List<StructQuickReportResultIndicatorDTO> indicators = reportResult.get(0).getIndicators();

            //特殊标签，目前只有ct值
            if (Boolean.TRUE.equals(promiseMedicalReportDTO.getNewTemplate())) {
                StructQuickReportResultIndicatorDTO resultIndicatorDTO = indicators.stream().filter(p -> StringUtils.equals(IndicatorTemplateEnum.CT.getTemplateType(), p.getTemplateType())).findFirst().orElse(null);
                if (Objects.nonNull(resultIndicatorDTO)) {
                    promiseMedicalReportDTO.setSpecialAttributeTag(JsonUtil.parseObject(duccConfig.getCtTipConfig(), SpecialAttributeTag.class));
                }
            }

            indicatorNum += indicators.size();

            promiseMedicalReportDTO.setIndicatorNum(indicators.size());
            //正常指标
            List<StructQuickReportResultIndicatorDTO> normal = indicators.stream().filter(p -> StringUtils.equals(CommonConstant.ZERO_STR, p.getAbnormalType())).collect(Collectors.toList());
            //异常指标
            List<StructQuickReportResultIndicatorDTO> abnormal = indicators.stream().filter(p -> !StringUtils.equals(CommonConstant.ZERO_STR, p.getAbnormalType())).collect(Collectors.toList());
            promiseMedicalReportDTO.setNormalIndicatorDTOList(convert(normal, indicatorMap, duccConfig));
            List<ReportIndicatorDTO> abnormalDTO = convert(abnormal, indicatorMap, duccConfig);
            promiseMedicalReportDTO.setAbnormalIndicatorDTOList(abnormalDTO);
            if (CollectionUtils.isNotEmpty(abnormalDTO)) {
                abnormalSummaryList.addAll(abnormalDTO);
                List<ReportIndicatorDTO> pathogensList = abnormalDTO.stream().filter(p -> CollectionUtils.isNotEmpty(p.getTagDetailDTOS())).collect(Collectors.toList());
                promiseMedicalReportDTO.setPathogensNum(CollectionUtils.isNotEmpty(pathogensList) ? pathogensList.size() : CommonConstant.ZERO);
                if (promiseMedicalReportDTO.getPathogensNum() > 0) {
                    promiseMedicalReportDTO.setPathogensTip(duccConfig.getPathogensTip());
                }
            }
            promiseMedicalReportDTO.setAnomalyNum(CollectionUtils.isNotEmpty(abnormal) ? abnormal.size() : CommonConstant.ZERO);
            promiseMedicalReportDTO.setIndicatorNum(indicators.size());
            promiseMedicalReportDTO.setReportId(String.valueOf(medicalReport.getId()));
            if (!select && Objects.nonNull(request.getReportCenterId()) && StringUtils.equals(String.valueOf(request.getReportCenterId()), medicalReport.getReportCenterId())) {
                select = Boolean.TRUE;
                promiseMedicalReportDTO.setSelect(Boolean.TRUE);
            }
        }
        //是否全部完成
        medicalReportOrderDTO.setAllFinish(allFinish);

        //检测结论
        TestConclusionDTO testConclusionDTO = packTestConclusion(abnormalSummaryList);
        testConclusionDTO.setAllFinish(allFinish);
        testConclusionDTO.setIndicatorNum(indicatorNum);
        medicalReportOrderDTO.setTestConclusionDTO(testConclusionDTO);

        //排序
        //已出报告&有异常
        List<PromiseMedicalReportDTO> finishAndAbnormal = Lists.newArrayList();
        //已出报告&无异常
        List<PromiseMedicalReportDTO> finishAndNormal = Lists.newArrayList();
        //未出报告
        List<PromiseMedicalReportDTO> unFinish = Lists.newArrayList();

        for (PromiseMedicalReportDTO promiseMedicalReportDTO : promiseMedicalReportDtoList) {
            if (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(promiseMedicalReportDTO.getStatus())) {
                if (promiseMedicalReportDTO.getAnomalyNum() > 0) {
                    finishAndAbnormal.add(promiseMedicalReportDTO);
                } else {
                    finishAndNormal.add(promiseMedicalReportDTO);
                }
            } else {
                unFinish.add(promiseMedicalReportDTO);
            }
        }

        finishAndAbnormal.addAll(finishAndNormal);
        finishAndAbnormal.addAll(unFinish);

        medicalReportOrderDTO.setPromiseMedicalReportDtoList(finishAndAbnormal);

        medicalReportOrderDTO.setMedicalReportRxDTOS(data.get().getMedicalReportRxDTOS());
        data.get().setMedicalReportOrderDTO(medicalReportOrderDTO);
        return this;
    }

    /**
     * 封装用户信息到MedicalReportOrderDTO对象中。
     *
     * @param jdhPromise            用于获取患者信息的JdhPromise对象。
     * @param medicalReportOrderDTO 封装用户信息的MedicalReportOrderDTO对象。
     */
    private void packUserInfo(JdhPromise jdhPromise, MedicalReportOrderDTO medicalReportOrderDTO) {
        MedicalReportUserDTO medicalReportUserDTO = new MedicalReportUserDTO();
        JdhPromisePatient jdhPromisePatient = null;
        if (Objects.nonNull(data.get().getRequest().getPatientId())) {
            jdhPromisePatient = jdhPromise.getPatients().stream().filter(p -> Objects.equals(data.get().getRequest().getPatientId(), p.getPatientId())).findFirst().orElse(null);
        } else {
            jdhPromisePatient = jdhPromise.getPatients().get(0);
        }

        if (Objects.nonNull(jdhPromisePatient)) {
            medicalReportUserDTO.setPatientId(jdhPromisePatient.getPatientId());
            medicalReportUserDTO.setPatientName(jdhPromisePatient.getUserName().mask());
            medicalReportUserDTO.setUserAge(jdhPromisePatient.getBirthday().getAge());
            if (Objects.nonNull(data.get().getUserBaseInfoBo())) {
                medicalReportUserDTO.setYunSmaImageUrl(data.get().getUserBaseInfoBo().getYunSmaImageUrl());
            }
            medicalReportUserDTO.setUserGender(jdhPromisePatient.getGender());
            medicalReportOrderDTO.setMedicalReportUserDTO(medicalReportUserDTO);
        }
    }

    /**
     * 将异常汇总列表打包成测试结论DTO对象。
     *
     * @param abnormalSummaryList 异常汇总列表
     * @return 测试结论DTO对象
     */
    private TestConclusionDTO packTestConclusion(List<ReportIndicatorDTO> abnormalSummaryList) {
        TestConclusionDTO testConclusionDTO = new TestConclusionDTO();
        if (CollectionUtils.isNotEmpty(abnormalSummaryList)) {
            Map<String, ReportIndicatorDTO> maps = abnormalSummaryList.stream().collect(Collectors.toMap(ReportIndicatorDTO::getIndicatorName, each -> each, (value1, value2) -> value1));
            List<ReportIndicatorDTO> distinctAbnormalSummaryList = new ArrayList<>(maps.values());
            testConclusionDTO.setAbnormalSummaryIndicators(distinctAbnormalSummaryList);
            testConclusionDTO.setAnomalyNum(distinctAbnormalSummaryList.size());
            int pathogensNum = CommonConstant.ZERO;
            for (ReportIndicatorDTO abnormalSummary : distinctAbnormalSummaryList) {
                if (StringUtils.isNotBlank(abnormalSummary.getTags())) {
                    String[] tagArray = abnormalSummary.getTags().split(",");
                    for (String tag : tagArray) {
                        if (tag.equals("1")) {
                            pathogensNum++;
                            break;
                        }
                    }
                }
            }
            testConclusionDTO.setPathogensNum(pathogensNum);
        } else {
            testConclusionDTO.setAnomalyNum(CommonConstant.ZERO);
            testConclusionDTO.setPathogensNum(CommonConstant.ZERO);
        }

        return testConclusionDTO;
    }

    /**
     * 将 StructQuickReportResultIndicatorDTO 列表转换为 ReportIndicatorDTO 列表。
     *
     * @param indicatorDTOS StructQuickReportResultIndicatorDTO 列表
     * @param indicatorMap  指标ID对应的百科ID映射表
     * @return 转换后的 ReportIndicatorDTO 列表
     */
    private List<ReportIndicatorDTO> convert(List<StructQuickReportResultIndicatorDTO> indicatorDTOS, Map<String, ServiceIndicatorDto> indicatorMap, DuccConfig duccConfig) {
        if (CollectionUtils.isEmpty(indicatorDTOS)) {
            return null;
        }
        List<ReportIndicatorDTO> resultList = Lists.newArrayList();
        for (StructQuickReportResultIndicatorDTO dto : indicatorDTOS) {
            ReportIndicatorDTO reportIndicatorDTO = new ReportIndicatorDTO();
            reportIndicatorDTO.setIndicatorName(dto.getIndicatorName());
            reportIndicatorDTO.setIndicatorNo(dto.getIndicatorNo());
            if (StringUtils.isNumeric(dto.getIndicatorNo())) {
                reportIndicatorDTO.setIndicatorId(new Long(dto.getIndicatorNo()));
            }
            reportIndicatorDTO.setCtValue(dto.getCtValue());
            reportIndicatorDTO.setTemplateType(dto.getTemplateType());
            reportIndicatorDTO.setCtValue(dto.getCtValue());
            reportIndicatorDTO.setRangeValueDTOS(dto.getRangeValueDTOS());
            reportIndicatorDTO.setReferenceRangeValue(dto.getReferenceRangeValue());
            //CT模版要用科学计数法
            if (StringUtils.equals(IndicatorTemplateEnum.CT.getTemplateType(), dto.getTemplateType())) {
                if (StringUtils.isNotBlank(dto.getIndicatorConcentration())) {
                    reportIndicatorDTO.setIndicatorConcentration(NumberUtils.toScientificNotation(dto.getIndicatorConcentration()));
                }
                if (CollectionUtils.isNotEmpty(dto.getRangeValueDTOS())) {
                    for (RangeValueDTO rangeValueDTO : dto.getRangeValueDTOS()) {
                        rangeValueDTO.setRangeMax(NumberUtils.toScientificNotation(rangeValueDTO.getRangeMax()));
                        rangeValueDTO.setRangeMin(NumberUtils.toScientificNotation(rangeValueDTO.getRangeMin()));
                        rangeValueDTO.setValue(NumberUtils.toScientificNotation(rangeValueDTO.getValue()));
                    }
                }
            }

            reportIndicatorDTO.setIndicatorConcentrationBigDecimal(dto.getIndicatorConcentrationBigDecimal());

            Set<String> oldKnightSkuIndicatorValue = duccConfig.getOldKnightSkuIndicatorValue();
            Map<String, String> indicatorAbnoramlDesc = duccConfig.getIndicatorAbnoramlDesc();
            if (oldKnightSkuIndicatorValue.contains(dto.getIndicatorName())) {
                String s = indicatorAbnoramlDesc.get(dto.getAbnormalType());
                reportIndicatorDTO.setValue(StringUtils.isNotBlank(s) ? s : dto.getValue());
                reportIndicatorDTO.setUnit(null);
                reportIndicatorDTO.setNormalRangeValue(StringUtils.isNotBlank(s) ? "阴性" : dto.getNormalRangeValue());
            } else {
                reportIndicatorDTO.setValue(dto.getValue());
                reportIndicatorDTO.setNormalRangeValue(dto.getNormalRangeValue());
                reportIndicatorDTO.setUnit(dto.getUnit());
            }

            reportIndicatorDTO.setTags(dto.getTags());
            reportIndicatorDTO.setAbnormalType(dto.getAbnormalType());
            //百科ID
            if (StringUtils.isNotBlank(dto.getIndicatorNo()) && indicatorMap.containsKey(dto.getIndicatorNo())) {
                reportIndicatorDTO.setVirusEncyclopediaId(indicatorMap.get(dto.getIndicatorNo()).getJdhWikiId());
            }
            reportIndicatorDTO.setValueDescription(StringUtils.equals(CommonConstant.ZERO_STR, dto.getAbnormalType()) ? "正常" : "异常");
            reportIndicatorDTO.setPercentage(dto.getPercentage());
            if (StringUtils.isNotBlank(dto.getTags())) {
                List<IndicatorTagDetailDTO> indicatorTagDetailDTOS = Lists.newArrayList();
                String[] tagSplit = dto.getTags().split(",");
                for (int i = 0; i < tagSplit.length; i++) {
                    //条件致病只有在致病异常时候才可展示
                    if (StringUtils.equals(CommonConstant.ZERO_STR, dto.getAbnormalType()) && Objects.equals(CommonConstant.ONE, Integer.valueOf(tagSplit[i]))) {
                        continue;
                    }
                    IndicatorTagDetailDTO indicatorTagDetailDTO = new IndicatorTagDetailDTO();
                    indicatorTagDetailDTO.setTag(Integer.valueOf(tagSplit[i]));
                    indicatorTagDetailDTO.setTagName(IndicatorTagEnum.getNameByType(Integer.valueOf(tagSplit[i])));
                    indicatorTagDetailDTO.setTagTip(duccConfig.getTagTip().get(String.valueOf(indicatorTagDetailDTO.getTag())));
                    indicatorTagDetailDTOS.add(indicatorTagDetailDTO);
                }
                reportIndicatorDTO.setTagDetailDTOS(indicatorTagDetailDTOS);
            }
            resultList.add(reportIndicatorDTO);
        }
        return resultList;
    }


    /**
     * 构建温馨提示信息
     *
     * @return
     */
    public MedicalReportContainer buildTips() {
        DuccConfig duccConfig = data.get().getDuccConfig();
        Map<String, String> reportTipsMap = duccConfig.getReportTipsMap();
        JdOrderDTO jdOrder = data.get().getJdOrder();
        String tips = "";
        Integer partnerSource = null;
        if (Objects.nonNull(jdOrder)) {
            partnerSource = jdOrder.getPartnerSource();
        }
        PartnerSourceEnum partnerSourceEnum = PartnerSourceEnum.fromCode(partnerSource);
        if (Objects.isNull(partnerSourceEnum)) {
            tips = reportTipsMap.get("default");
        } else if (StringUtils.isNotBlank(reportTipsMap.get(String.valueOf(partnerSourceEnum.getCode())))) {
            tips = reportTipsMap.get(String.valueOf(partnerSourceEnum.getCode()));
        } else {
            tips = reportTipsMap.get("default");
        }
        data.get().getMedicalReportOrderDTO().setTips(tips);
        return this;
    }

    /**
     * 构建问卷
     *
     * @return
     */
    public MedicalReportContainer buildNps() {
        data.get().getMedicalReportOrderDTO().setNps("https://answer.jd.com/jump/?shortCode=cwtCwEVFGuC&surveyId=2079071");
        return this;
    }

    /**
     * 组装药品推荐
     *
     * @return 医疗报告容器对象
     */
    public MedicalReportContainer buildMedicineRecommend(QuickDrugConfigRepository quickDrugConfigRepository, QuickDrugConfigRepository2 quickDrugConfigRepository2) {
        log.info("buildMedicineRecommend->Start");
        DuccConfig duccConfig = data.get().getDuccConfig();
        if (!duccConfig.getReportShowRecommendSwitch()) {
            return this;
        }
        ReportData reportData = data.get();
        //用药推荐环境配置
        if (!duccConfig.getReportMedicineRecommendEnv().contains(reportData.getRequest().getEnvType())) {
            return this;
        }

        if (Boolean.TRUE.equals(reportData.getDoctorView())) {
            return this;
        }
        log.info("buildMedicineRecommend->env,pass");


        MedicalReportOrderDTO medicalReportOrderDTO = reportData.getMedicalReportOrderDTO();
        //报告全部已出再判断用药建议
        if (!medicalReportOrderDTO.getTestConclusionDTO().getAllFinish()) {
            return this;
        }
        log.info("buildMedicineRecommend->finish,pass");


        TestConclusionDTO testConclusionDTO = medicalReportOrderDTO.getTestConclusionDTO();
        //如果没有异常指标 返回
        if (CollectionUtils.isEmpty(testConclusionDTO.getAbnormalSummaryIndicators())) {
            return this;
        }
        log.info("buildMedicineRecommend->testConclusionDTO,pass");

        List<MedicalReport> reportList = data.get().getReportList();
        MedicalReport medicalReport = reportList.stream().filter(p -> StringUtils.isEmpty(p.getReportJpgOss())).findFirst().orElse(null);
        if (Objects.nonNull(medicalReport)) {
            return this;
        }

        String reportMedicineRecommend = reportData.getDuccConfig().getReportMedicineRecommend();
        log.info("buildMedicineRecommend->,reportMedicineRecommend={}", reportMedicineRecommend);


        //获取sku、项目
        List<String> skuNo = medicalReportOrderDTO.getPromiseMedicalReportDtoList().stream().map(PromiseMedicalReportDTO::getServiceId).distinct().sorted().collect(Collectors.toList());
        List<String> serviceItem = medicalReportOrderDTO.getPromiseMedicalReportDtoList().stream().map(PromiseMedicalReportDTO::getServiceItemId).distinct().collect(Collectors.toList());
        Map<String, Object> aviatorParam = Maps.newHashMap();
        aviatorParam.put("skuNo", skuNo);
        aviatorParam.put("serviceItem", serviceItem);

        try {
            if (StringUtils.isNotBlank(reportMedicineRecommend)) {
                Boolean execute = (boolean) AviatorEvaluator.compile(reportMedicineRecommend).execute(aviatorParam);
                log.info("MedicalReportContainer.buildMedicineRecommend.execute={}", execute);
                if (!execute) {
                    return this;
                }
            }
            log.info("buildMedicineRecommend->reportMedicineRecommend,pass");

            List<ReportIndicatorDTO> abnormalSummaryIndicators = medicalReportOrderDTO.getTestConclusionDTO().getAbnormalSummaryIndicators();
            List<String> indicatorIds = abnormalSummaryIndicators.stream().map(p -> String.valueOf(p.getIndicatorId())).distinct().collect(Collectors.toList());
            Collections.sort(indicatorIds);
            log.info("buildMedicineRecommend->indicatorIds={}", JsonUtil.toJSONString(indicatorIds));
            String indicatorId = String.join("_", indicatorIds);
            log.info("MedicalReportContainer.buildMedicineRecommend.indicatorId={}", indicatorId);

            List<MedicineProgramGroupBO> programGroupBOList = Lists.newArrayList();

            programGroupBOList = quickDrugConfigRepository.findConfig(indicatorId);
            log.info("MedicalReportContainer.buildMedicineRecommend.programGroupBOList={}", JSON.toJSONString(programGroupBOList));
            if (CollectionUtils.isEmpty(programGroupBOList)) {
                programGroupBOList = quickDrugConfigRepository2.findConfig(indicatorId);
                log.info("MedicalReportContainer.buildMedicineRecommend.programGroupBOList2={}", JSON.toJSONString(programGroupBOList));
            }
            if (CollectionUtils.isEmpty(programGroupBOList)) {
                return this;
            }

            //过滤掉不展示的
            programGroupBOList.removeIf(p -> !p.getShow());
            if (CollectionUtils.isEmpty(programGroupBOList)) {
                return this;
            }

            List<MedicineRecommendGroupDTO> recommendGroupDTOS = Lists.newArrayList();

            //排序
            programGroupBOList.sort(Comparator.comparing(MedicineProgramGroupBO::getShowOrder).reversed());

            //medicalPromiseDTO已经赋值了被检测者信息
            MedicalPromiseDTO medicalPromiseDTO = reportData.getMedicalPromiseList().get(0);
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("medicalPromiseDTO",medicalPromiseDTO);
            //result对应用药showOrder字段
            Long result = (Long)AviatorEvaluator.compile(duccConfig.getHeartSmartOrderDetailConfig().getRecommendDrugHitRule(),true).execute(paramMap);

            for (MedicineProgramGroupBO medicineProgramGroupBO : programGroupBOList) {

                MedicineRecommendGroupDTO recommendGroupDTO = new MedicineRecommendGroupDTO();

                recommendGroupDTO.setGroupBenefit(medicineProgramGroupBO.getGroupBenefit());
                recommendGroupDTO.setGroupName(medicineProgramGroupBO.getGroupName());
                recommendGroupDTO.setShowOrder(medicineProgramGroupBO.getShowOrder());

                if(result.equals(medicineProgramGroupBO.getShowOrder().longValue())){
                    recommendGroupDTO.setSelected(1);
                }

                List<MedicineProgramDetailBO> medicineProgramDetailBOS = medicineProgramGroupBO.getMedicineProgramDetailBOS();
                medicineProgramDetailBOS.removeIf(p -> !p.getShow());
                if (CollectionUtils.isEmpty(medicineProgramDetailBOS)) {
                    continue;
                }
                medicineProgramDetailBOS.sort(Comparator.comparing(MedicineProgramDetailBO::getShowOrder));
                MedicineProgramDetailBO medicineProgramDetailBO = medicineProgramDetailBOS.get(0);
                log.info("MedicalReportContainer.buildMedicineRecommend.medicineProgramDetailBO={}", JSON.toJSONString(medicineProgramDetailBO));
                List<MedicineBO> medicineBOList = medicineProgramDetailBO.getMedicineBOList();
                medicineBOList.removeIf(p -> !p.getShow());
                if (CollectionUtils.isEmpty(medicineBOList)) {
                    continue;
                }
                medicineBOList.sort(Comparator.comparing(MedicineBO::getSort).reversed());

                List<MedicineDetailDTO> medicineDetailDTOS = Lists.newArrayList();
                for (MedicineBO medicineBO : medicineBOList) {
                    MedicineDetailDTO medicineDetailDTO = new MedicineDetailDTO();
                    medicineDetailDTO.setMedicineSpecifications(medicineBO.getMedicineSpecifications());
                    medicineDetailDTO.setApprovedMedicineName(medicineBO.getApprovedMedicineName());
                    medicineDetailDTO.setLabel(medicineBO.getLabel());
                    medicineDetailDTO.setSort(medicineBO.getSort());
                    medicineDetailDTO.setMedicineType(medicineBO.getMedicineType());
                    medicineDetailDTOS.add(medicineDetailDTO);
                }

                recommendGroupDTO.setMedicineDTOList(medicineDetailDTOS);

                recommendGroupDTOS.add(recommendGroupDTO);
            }
            if (CollectionUtils.isNotEmpty(recommendGroupDTOS)) {
                medicalReportOrderDTO.setRecommendGroupDTOS(recommendGroupDTOS);
            }
        } catch (Exception e) {
            log.error("MedicalReportContainer.buildMedicineRecommend.error", e);
        }

        return this;
    }


    public MedicalReportContainer buildDoctorInfo() {
        MedicalReportOrderRequest request = data.get().getRequest();
        //从互医问诊界面进入的，不再展示医生信息
        if (StringUtils.equals("huyi", request.getPath()) || Boolean.TRUE.equals(request.getDoctorView())) {
            return this;
        }
        ReportDoctorDTO reportDoctorDTO = null;
        InspectSheetInfoBO inspectSheetInfoBO = data.get().getInspectSheetInfoBO();
        Map<String, Object> reportNetDoctorConfig = data.get().getDuccConfig().getReportNetDoctorConfig();
        if (Objects.nonNull(inspectSheetInfoBO)) {
            reportDoctorDTO = new ReportDoctorDTO();
            reportDoctorDTO.setPicUrl(inspectSheetInfoBO.getDoctorHeadPic());
            reportDoctorDTO.setLink(inspectSheetInfoBO.getDiagJumpUrl());
            reportDoctorDTO.setDoctorDepartmentName(inspectSheetInfoBO.getSecondDepartmentName());
            reportDoctorDTO.setDoctorTitle(inspectSheetInfoBO.getDoctorTitle());
            reportDoctorDTO.setHospitalName(inspectSheetInfoBO.getHospitalName());
            reportDoctorDTO.setNumInquiry(inspectSheetInfoBO.getNumInquiry());
            reportDoctorDTO.setButtonName(reportNetDoctorConfig.get("buttonName").toString());
            reportDoctorDTO.setTitle(reportNetDoctorConfig.get("title").toString());
            String format = DateUtil.format(DateUtil.parseDate(inspectSheetInfoBO.getExpireDate()), CommonConstant.YMDHM2);
            reportDoctorDTO.setHints(format + "前" + inspectSheetInfoBO.getNumInquiry() + "次免费提问机会");
            reportDoctorDTO.setIntroduction(reportNetDoctorConfig.get("introduction").toString());
            reportDoctorDTO.setDoctorName(inspectSheetInfoBO.getDoctorName());

        } else {
            reportDoctorDTO = getDefaultReportDoctorDTO();
        }

        DuccConfig duccConfig = SpringUtil.getBean(DuccConfig.class);
        JSONObject jsonObject = JSONObject.parseObject(duccConfig.getSickConfig());
        if(jsonObject!=null
                &&(jsonObject.getBoolean("open")||jsonObject.getJSONArray("whitePin").contains(request.getUserPin()))){
            //返回是否开病假文案
            MedicalReportApplication medicalReportApplication = SpringUtil.getBean(MedicalReportApplication.class);
            OuterOrderRequest outerOrderRequest = new OuterOrderRequest();
            outerOrderRequest.setPatientId(request.getPatientId());
            outerOrderRequest.setUserPin(request.getUserPin());
            outerOrderRequest.setMedicalPromiseId(data.get().getMedicalPromiseList().get(0).getMedicalPromiseId());
            outerOrderRequest.setReportDetail(true);
            MedicalReportSummaryDTO medicalReportSummaryDTO = medicalReportApplication.queryListByMedPromiseId(outerOrderRequest);


            //需要开具病假单->查互医接口是否已开过病假单
            SickLeaveExportRpc sickLeaveExportRpc = SpringUtil.getBean(SickLeaveExportRpc.class);
            QuerySickCertParam querySickCertParam = new QuerySickCertParam();
            if(data.get().jdOrder!=null&&PartnerSourceEnum.JDH_NETDIAG.getCode().equals(data.get().jdOrder.getPartnerSource())){
                querySickCertParam.setMedicalPromiseIds(Collections.singletonList(data.get().jdOrder.getPartnerSourceOrderId()));
            }else{
                querySickCertParam.setMedicalPromiseIds(data.get().getMedicalPromiseList().stream().map(t->t.getMedicalPromiseId()+"").collect(Collectors.toList()));
            }
            QuerySickCertBO querySickCertBO = sickLeaveExportRpc.querySickCert(querySickCertParam);
            if(querySickCertBO!=null){
                if(StringUtils.isNotEmpty(querySickCertBO.getPicUrl())){
                    //已开具病假单
                    reportDoctorDTO.setSickCertImage(querySickCertBO.getPicUrl());
                    reportDoctorDTO.setSickCertNote(reportNetDoctorConfig.get("sickCertNoteTwo").toString());
                    reportDoctorDTO.setSickOpen(1);
                }
            }

            if(medicalReportSummaryDTO!=null&&medicalReportSummaryDTO.getOpenSickCert()){
                if(StringUtils.isEmpty(reportDoctorDTO.getSickCertImage())){
                    //未开具病假单
                    reportDoctorDTO.setSickCertNote(String.format(reportNetDoctorConfig.get("sickCertNoteOne").toString(), TimeUtils.dateTimeToStr(medicalReportSummaryDTO.getSickCertEndDay(), TimeFormat.SHORT_PATTERN_LINE)));
                }
                reportDoctorDTO.setIntroductionList(((JSONArray)reportNetDoctorConfig.get("introductionListOne")).toJavaList(String.class));
            }else{
                reportDoctorDTO.setIntroductionList(((JSONArray)reportNetDoctorConfig.get("introductionListTwo")).toJavaList(String.class));
            }
        }else{
            reportDoctorDTO.setIntroductionList(((JSONArray)reportNetDoctorConfig.get("introductionListTwo")).toJavaList(String.class));
        }
        data.get().getMedicalReportOrderDTO().setReportDoctorDTO(reportDoctorDTO);
        return this;
    }

    /**
     * 买药秒送楼层
     *
     * @return
     */
    public MedicalReportContainer buildQuickDrug() {
        List<MedicineRecommendGroupDTO> recommendGroupDTOS = data.get().getMedicalReportOrderDTO().getRecommendGroupDTOS();
        if (CollectionUtils.isNotEmpty(recommendGroupDTOS)) {
            return this;
        }
        MedicalReportOrderRequest request = data.get().getRequest();
        Map<String, String> quickDrugConfig = data.get().getDuccConfig().getQuickDrugConfig();
        String envType = request.getEnvType();
        if (StringUtils.isBlank(envType)) {
            String defaultConfig = quickDrugConfig.get("default");
            List<QuickDrugDTO> quickDrugDTOS = JsonUtil.parseArray(defaultConfig, QuickDrugDTO.class);
            data.get().getMedicalReportOrderDTO().setQuickDrugDTOS(quickDrugDTOS);
            return this;
        }

        if (EnvTypeEnum.JD_APP.getCode().equals(envType)) {
            QuickDrugDTO drugDTO = new QuickDrugDTO();
            drugDTO.setImgLink("https://img12.360buyimg.com/imagetools/jfs/t1/255209/21/13085/76671/6785dde9F4d1a26bb/477fe9abd792c0f4.png");
            Map<String, Object> param = Maps.newHashMap();
            param.put("ishidden", true);
            param.put("transparentenable", true);
            param.put("des", "jdreactcommon");
            param.put("appname", "JDReactJDHYJS");
            param.put("needLogin", true);
            param.put("modulename", "JDReactJDHYJS");
            param.put("category", "jump");
            Map<String, Object> map = Maps.newHashMap();
            map.put("transparentenable", true);
            map.put("channelId", "rn38");
            map.put("app_v", "2");
            param.put("param", map);
            drugDTO.setUrl("openApp.jdMobile://virtual?params=" + JsonUtil.toJSONString(param));

            data.get().getMedicalReportOrderDTO().setQuickDrugDTOS(Lists.newArrayList(drugDTO));
            return this;
        }

        String config = quickDrugConfig.get(envType);
        if (StringUtils.isBlank(config)) {
            config = quickDrugConfig.get("default");
        }
        List<QuickDrugDTO> quickDrugDTOS = JsonUtil.parseArray(config, QuickDrugDTO.class);
        data.get().getMedicalReportOrderDTO().setQuickDrugDTOS(quickDrugDTOS);
        return this;

    }


    /**
     * 返回结果
     *
     * @return
     */
    public MedicalReportOrderDTO resp() {
        return data.get().getMedicalReportOrderDTO();
    }

    /**
     * 订单、履约单查询
     *
     * @param func
     * @return
     */
    public MedicalReportContainer initOrderPromise(Function<OrderDetailParam, JdOrderDTO> func,
                                                   Function<PromiseRepQuery, JdhPromise> func2,
                                                   Function<MedicalPromiseRepQuery, MedicalPromise> func3,
                                                   Function<MedicalReportQueryBO, List<MedicalReport>> func4,
                                                   Function<DpRelationCheckBo, Boolean> func5
    ) {
        MedicalReportOrderRequest request = data.get().getRequest();
        if (request == null) {
            log.error("exception.initOrder.data:{}", JSON.toJSONString(data.get()));
            throw new ParamException(-1, "参数异常");
        }
        if (StringUtil.isBlank(request.getOrderId()) && StringUtils.equals(request.getDomainCode(), "promise") && StringUtils.equals(request.getAggregateCode(), "promise")) {
            fromPromise(func, func2, request, func5);
        } else if (StringUtils.equals("reportCenter", request.getPath())) {
            fromReportCenterId(func, func2, func3, func4, request, func5);
        } else {
            fromOrder(func, func2, request, func5);
        }
        return this;
    }

    /**
     * fromOrder
     *
     * @param func
     * @param func2
     * @param request
     */
    private void fromOrder(Function<OrderDetailParam, JdOrderDTO> func, Function<PromiseRepQuery, JdhPromise> func2, MedicalReportOrderRequest request, Function<DpRelationCheckBo, Boolean> func5) {
        if (Objects.isNull(request.getOrderId())) {
            throw new ParamException(-1, "参数异常");
        }
        //查订单
        OrderDetailParam param = OrderDetailParam.builder()
                .orderId(request.getOrderId()).build();
        JdOrderDTO order = func.apply(param);

        if (Objects.isNull(order)) {
            throw new BusinessException(ReportErrorCode.HAVE_NO_AUTH);
        }

        //如果登陆pin和记录pin不一致，则判断是不是有医患关系
        if (!StringUtil.equals(order.getUserPin(), request.getUserPin())) {
            DpRelationCheckBo bo = DpRelationCheckBo.builder()
                    .loginPin(request.getUserPin())
                    .patientId(request.getPatientId())
                    .tenantType("JD8888")
                    .checkType(CommonConstant.ONE)
                    .encStr(request.getEncStr())
                    .build();
            Boolean apply = func5.apply(bo);

            //如果没有医患关系
            if (!apply) {
                throw new BusinessException(ReportErrorCode.HAVE_NO_AUTH);
            }
            request.setDoctorView(Boolean.TRUE);
        }


        data.get().setJdOrder(order);
        //互医的检验单不需要 patientId 过滤   //互医和家医检验单，只会对应一个患者
        if (request.getPatientId() != null && (PartnerSourceEnum.JDH_NETDIAG.getCode().equals(order.getPartnerSource()) || PartnerSourceEnum.JDH_HOMEDIAG.getCode().equals(order.getPartnerSource()))) {
            request.setPatientId(null);
        }
        //查履约单
        String sourceVoucherId = Objects.nonNull(order.getParentId()) && order.getParentId() > 0 ? order.getParentId().toString() : order.getOrderId().toString();
        PromiseRepQuery queryParam = PromiseRepQuery.builder().sourceVoucherId(sourceVoucherId).userPin(order.getUserPin()).build();
        JdhPromise promise = func2.apply(queryParam);
        log.info("MedicalReportContainer.initOrderPromise.param:{},result:{}", JSON.toJSONString(queryParam), JSON.toJSONString(promise));
        if (promise == null) {
            throw new BusinessException(JDH_PROMISE_NOT_EXIST);
        }
        data.get().setJdhPromise(promise);
    }

    /**
     * fromReportCenterId
     *
     * @param func
     * @param func2
     * @param func3
     * @param func4
     * @param request
     */
    private void fromReportCenterId(Function<OrderDetailParam, JdOrderDTO> func, Function<PromiseRepQuery, JdhPromise> func2, Function<MedicalPromiseRepQuery, MedicalPromise> func3, Function<MedicalReportQueryBO, List<MedicalReport>> func4, MedicalReportOrderRequest request, Function<DpRelationCheckBo, Boolean> func5) {
        if (Objects.isNull(request.getReportCenterId())) {
            throw new ParamException(-1, "参数异常 reportCenterId不存在");
        }
        //查报告
        List<MedicalReport> medicalReports = func4.apply(MedicalReportQueryBO.builder().patientId(request.getPatientId()).reportCenterId(String.valueOf(request.getReportCenterId())).build());
        if (CollectionUtils.isEmpty(medicalReports)) {
            throw new BusinessException(ReportErrorCode.HAVE_NO_AUTH);
        }
        MedicalReport medicalReport = medicalReports.get(0);

        //如果登陆pin和记录pin不一致，则判断是不是有医患关系 todo 增加判断条件：档案问诊场景
        if (!StringUtil.equals(medicalReport.getUserPin(), request.getUserPin())) {

            if (Objects.isNull(request.getPatientId())) {
                throw new BusinessException(ReportErrorCode.HAVE_NO_AUTH);
            }

            DpRelationCheckBo bo = DpRelationCheckBo.builder()
                    .loginPin(request.getUserPin())
                    .patientId(request.getPatientId())
                    .tenantType("JD8888")
                    .checkType(CommonConstant.ONE)
                    .encStr(request.getEncStr())
                    .build();
            Boolean apply = func5.apply(bo);

            //如果没有医患关系
            if (!apply) {
                throw new BusinessException(ReportErrorCode.HAVE_NO_AUTH);
            }

            request.setDoctorView(Boolean.TRUE);
        }


        //查履约单
        MedicalPromise medicalPromise = func3.apply(MedicalPromiseRepQuery.builder().medicalPromiseId(medicalReport.getMedicalPromiseId()).build());
        //查promise
        JdhPromise jdhPromise = func2.apply(PromiseRepQuery.builder().promiseId(medicalPromise.getPromiseId()).build());
        data.get().setJdhPromise(jdhPromise);
        //查询订单
        String sourceVoucherId = jdhPromise.getSourceVoucherId();
        promiseToOrder(func, jdhPromise, sourceVoucherId);
    }


    /**
     * fromPromise
     *
     * @param func
     * @param func2
     * @param request
     */
    private void fromPromise(Function<OrderDetailParam, JdOrderDTO> func, Function<PromiseRepQuery, JdhPromise> func2, MedicalReportOrderRequest request, Function<DpRelationCheckBo, Boolean> func5) {
        //查询履约单
        if (request.getPromiseId() == null) {
            throw new ParamException(-1, "参数异常 promiseId不存在");
        }
        PromiseRepQuery queryParam = PromiseRepQuery.builder().promiseId(request.getPromiseId()).build();
        JdhPromise promise = func2.apply(queryParam);
        log.info("MedicalReportContainer.initPromise.param:{},result:{}", JSON.toJSONString(queryParam), JSON.toJSONString(promise));
        if (promise == null) {
            throw new BusinessException(ReportErrorCode.HAVE_NO_AUTH);
        }

        //如果登陆pin和记录pin不一致，则判断是不是有医患关系
        if (!StringUtil.equals(promise.getUserPin(), request.getUserPin())) {

            if (Objects.isNull(request.getPatientId())) {
                throw new BusinessException(ReportErrorCode.HAVE_NO_AUTH);
            }
            //需要判断传入pid和promise中的pid是否一致
            JdhPromisePatient jdhPromisePatient = promise.getPatients().stream().filter(p -> Objects.equals(request.getPatientId(), p.getPatientId())).findFirst().orElse(null);
            if (Objects.isNull(jdhPromisePatient)) {
                throw new BusinessException(ReportErrorCode.HAVE_NO_AUTH);
            }

            DpRelationCheckBo bo = DpRelationCheckBo.builder()
                    .loginPin(request.getUserPin())
                    .patientId(request.getPatientId())
                    .tenantType("JD8888")
                    .checkType(CommonConstant.ONE)
                    .encStr(request.getEncStr())
                    .build();
            Boolean apply = func5.apply(bo);

            //如果没有医患关系
            if (!apply) {
                throw new BusinessException(ReportErrorCode.HAVE_NO_AUTH);
            }

            request.setDoctorView(Boolean.TRUE);
        }


        data.get().setJdhPromise(promise);

        //查询订单
        String sourceVoucherId = promise.getSourceVoucherId();
        promiseToOrder(func, promise, sourceVoucherId);
    }

    /**
     * promiseToOrder
     *
     * @param func
     * @param jdhPromise
     * @param sourceVoucherId
     */
    private void promiseToOrder(Function<OrderDetailParam, JdOrderDTO> func, JdhPromise jdhPromise, String sourceVoucherId) {
        OrderDetailParam param = OrderDetailParam.builder().pin(jdhPromise.getUserPin()).orderId(sourceVoucherId).build();
        JdOrderDTO apply = func.apply(param);
        JdOrderDTO jdOrderDTO = null;
        if (Objects.nonNull(apply)) {
            if (apply.getParentId() > 0) {
                param.setOrderId(String.valueOf(apply.getParentId()));
                jdOrderDTO = func.apply(param);
            } else {
                jdOrderDTO = apply;
            }
        }

        if (Objects.nonNull(jdOrderDTO)) {
            data.get().setJdOrder(jdOrderDTO);
        }
    }


    /**
     * userMarketInfo
     *
     * @param func
     * @param contextData
     * @return
     */
    public MedicalReportContainer userMarketInfo(Function<UserMarketRequest, List<UserMarketDTO>> func, ReportData contextData) {
        if (!contextData.getDuccConfig().getRxShowSwitch()) {
            return this;
        }
        if (Boolean.TRUE.equals(contextData.getDoctorView())) {
            return this;
        }

        Long patientId = null;
        JdhPromise jdhPromise = contextData.getJdhPromise();
        MedicalReportOrderRequest request = contextData.getRequest();
        if (Objects.nonNull(request.getPatientId())) {
            patientId = request.getPatientId();
        } else {
            patientId = jdhPromise.getPatients().get(0).getPatientId();
        }

        UserMarketRequest userMarketRequest = new UserMarketRequest();
        userMarketRequest.setUserPin(request.getUserPin());
        userMarketRequest.setPromiseId(jdhPromise.getPromiseId());
        userMarketRequest.setPatientId(patientId);
        //快检报告详情页一键购药
        userMarketRequest.setScene(UserMarketSceneEnum.QUICK_CHECK_REPORT_DRUG.getScene());
        //互医问诊
        userMarketRequest.setMarketType(UserMarketTypeEnum.NET_FORMULARY.getType());
        List<UserMarketDTO> apply = func.apply(userMarketRequest);
        contextData.setUserMarketDTOS(apply);
        return this;
    }

    /**
     * 查询检验单详情
     *
     * @param func
     * @return
     */
    public MedicalReportContainer netRx(BiFunction<List<Long>, Boolean, Map<Long, RxDetailBo>> func, ReportData contextData) {
        if (Boolean.TRUE.equals(contextData.getDoctorView())) {
            return this;
        }

        DuccConfig duccConfig = contextData.getDuccConfig();
        String rxShowExpression = duccConfig.getRxShowExpression();
        log.info("MedicalReportContainer->netRx,start");
        List<UserMarketDTO> userMarketDTOS = contextData.getUserMarketDTOS();
        log.info("MedicalReportContainer->netRx,userMarketDTOS={}", JsonUtil.toJSONString(userMarketDTOS));
        if (CollectionUtils.isEmpty(userMarketDTOS)) {
            return this;
        }
        List<Long> rxList = userMarketDTOS.stream().map(p -> Long.valueOf(p.getMarketDetail())).collect(Collectors.toList());
        log.info("MedicalReportContainer->netRx,rxList={}", JsonUtil.toJSONString(rxList));
        Map<Long, RxDetailBo> apply = func.apply(rxList, Boolean.FALSE);
        if (MapUtils.isEmpty(apply)) {
            return this;
        }
        Expression compile = AviatorEvaluator.compile(rxShowExpression, Boolean.TRUE);

        List<MedicalReportRxDTO> medicalReportRxDTOS = Lists.newArrayList();
        apply.forEach((k, v) -> {
            MedicalReportRxDTO medicalReportRxDTO = new MedicalReportRxDTO();
            RxInfoBo rxInfoDTO = v.getRxInfoDTO();
            medicalReportRxDTO.setRxId(String.valueOf(rxInfoDTO.getRxId()));
            medicalReportRxDTO.setRxStatus(rxInfoDTO.getRxStatus());
            medicalReportRxDTO.setRxExpiry(TimeUtils.timeStringFormatString(rxInfoDTO.getRxExpiry(), CommonConstant.YMDHMS, CommonConstant.YMDHM3));
            medicalReportRxDTO.setRxCreateTime(rxInfoDTO.getRxCreateTime());
            medicalReportRxDTO.setRxStatusShowStatus(rxInfoDTO.getRxStatusShowStatus());
            Map<String, Object> map = new HashMap<>();
            map.put("rxStatus", medicalReportRxDTO.getRxStatus());
            map.put("usable", rxInfoDTO.getUsable());
            medicalReportRxDTO.setShow((Boolean) compile.execute(map));
            medicalReportRxDTOS.add(medicalReportRxDTO);
        });
        List<MedicalReportRxDTO> sort = medicalReportRxDTOS.stream().sorted(Comparator.comparing(MedicalReportRxDTO::getRxExpiry).reversed()).collect(Collectors.toList());
        contextData.setMedicalReportRxDTOS(sort);
        return this;
    }


    /**
     * 自动触发释放资源
     */
    @Override
    public void close() {
        data.remove();
        log.debug("MedicalReportContainer.data.release!!!");
    }

    /**
     * 报告原始数据
     */
    @Data
    public static class ReportData {
        /**
         * ducc
         */
        private DuccConfig duccConfig;
        /**
         * 请求入参
         */
        private MedicalReportOrderRequest request;
        /**
         * 订单
         */
        private JdOrderDTO jdOrder;
        /**
         * 履约单
         */
        private JdhPromise jdhPromise;
        /**
         * 检测详情
         */
        private InspectSheetInfoBO inspectSheetInfoBO;
        /**
         * 检测单
         */
        private List<MedicalPromiseDTO> medicalPromiseList;
        /**
         * 服务单
         */
        private List<ServiceItemDto> serviceItemList;
        /**
         * 检测历史
         */
        private List<MedPromiseHistoryDTO> promiseHistoryList;
        /**
         * 报告
         */
        private List<MedicalReport> reportList;
        /**
         * 报告oss 文件解析结果
         */
        private Map<String, StructQuickReportContentDTO> fileList;
        /**
         * 检测指标
         */
        private List<ServiceIndicatorDto> indicatorList;
        /**
         * 检测报告
         */
        private MedicalReportOrderDTO medicalReportOrderDTO;
        /**
         * 用户信息
         */
        private UserBaseInfoBo userBaseInfoBo;

        /**
         * 用户营销信息list
         */
        private List<UserMarketDTO> userMarketDTOS;
        /**
         * 存储医疗报告处方信息的列表。
         */
        private List<MedicalReportRxDTO> medicalReportRxDTOS;

        /**
         * 医生查看报告
         */
        private Boolean doctorView;

    }
}

