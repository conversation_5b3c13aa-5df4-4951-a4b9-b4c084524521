package com.jdh.o2oservice.application.trade.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.health.medical.examination.export.dto.AppointmentInfoDTO;
import com.jd.health.medical.examination.export.param.QueryAppointmentInfoParam;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angel.service.AngelQueryApplication;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.convert.PromiseApplicationConverter;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.settlement.service.JdServiceSettleApplication;
import com.jdh.o2oservice.application.support.service.FeeConfigurationApplication;
import com.jdh.o2oservice.application.support.service.PatientApplication;
import com.jdh.o2oservice.application.trade.TradeExtApplication;
import com.jdh.o2oservice.application.trade.convert.*;
import com.jdh.o2oservice.application.trade.handler.order.CustomOrderApplicationContext;
import com.jdh.o2oservice.application.trade.handler.order.CustomOrderHandlerContext;
import com.jdh.o2oservice.application.trade.handler.order.CustomOrderStrategy;
import com.jdh.o2oservice.application.trade.service.InspectionSheetApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderRefundApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.config.AbTestConfiguration;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.DiscountFeeAction;
import com.jdh.o2oservice.base.ducc.model.fee.DynamicFee;
import com.jdh.o2oservice.base.ducc.model.fee.JdhFeeTimeConfig;
import com.jdh.o2oservice.base.ducc.model.order.CustomOrderDetailServiceInfoBo;
import com.jdh.o2oservice.base.enums.OrderTypeEnum;
import com.jdh.o2oservice.base.enums.*;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.ErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.model.AbTestExp;
import com.jdh.o2oservice.base.model.AbilityCode;
import com.jdh.o2oservice.base.model.DomainAbility;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.statemachine.AbilityExecutor;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.core.domain.angel.enums.AngelStationInventoryBusinessTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.context.QueryAvailableTimeContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelShipDomainService;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.product.model.JdhAreaFeeConfig;
import com.jdh.o2oservice.core.domain.product.model.JdhAreaFeeConfigdentifier;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhAreaFeeConfigRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.JdhAreaFeeConfigQuery;
import com.jdh.o2oservice.core.domain.promise.context.SubmitAppointmentDraftContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.PromiseService;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseAppointmentDraftRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseHistoryRepQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.service.JdhPromiseDomainService;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.enums.*;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JdhAddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.AddressDetailBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.PriceInfoResponseBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.PriceResultBO;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.PromiseGoRpcService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.ship.param.CommonCheckPreCreateOrderBo;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.bo.*;
import com.jdh.o2oservice.core.domain.trade.context.*;
import com.jdh.o2oservice.core.domain.trade.enums.*;
import com.jdh.o2oservice.core.domain.trade.event.OrderCreateEventBody;
import com.jdh.o2oservice.core.domain.trade.event.OrderSplitEventBody;
import com.jdh.o2oservice.core.domain.trade.factory.JdOrderFactory;
import com.jdh.o2oservice.core.domain.trade.model.*;
import com.jdh.o2oservice.core.domain.trade.repository.db.*;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderCalculationQueryServiceRpc;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.core.domain.trade.rpc.SelfAppointRecordExportServiceRpc;
import com.jdh.o2oservice.core.domain.trade.rpc.VtpOrderInfoRpc;
import com.jdh.o2oservice.core.domain.trade.service.JdhOrderDomainService;
import com.jdh.o2oservice.core.domain.trade.service.SettleUrlService;
import com.jdh.o2oservice.core.domain.trade.service.TradeDomainService;
import com.jdh.o2oservice.core.domain.trade.vo.*;
import com.jdh.o2oservice.export.angel.cmd.ConfirmPreemptionInventoryCmd;
import com.jdh.o2oservice.export.angel.cmd.PreemptionInventoryCmd;
import com.jdh.o2oservice.export.angel.cmd.ReleaseInventoryCmd;
import com.jdh.o2oservice.export.angel.dto.*;
import com.jdh.o2oservice.export.angel.query.*;
import com.jdh.o2oservice.export.angelpromise.dto.AngelShipDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.angelpromise.dto.LastCompleteAngelDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.angelpromise.query.GetDetailByPromiseIdQuery;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.AddProductDto;
import com.jdh.o2oservice.export.product.dto.InspectProjectChildDetailDTO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ScriptDto;
import com.jdh.o2oservice.export.product.query.JdhAddProductRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuAreaFeeQuery;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.promise.cmd.CreateVoucherCmd;
import com.jdh.o2oservice.export.promise.cmd.SubmitAppointmentDraftCmd;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.enums.VoucherOpEnum;
import com.jdh.o2oservice.export.promise.query.PromiseListRequest;
import com.jdh.o2oservice.export.promise.query.VoucherPageRequest;
import com.jdh.o2oservice.export.support.dto.PatientDto;
import com.jdh.o2oservice.export.support.query.PatientListRequest;
import com.jdh.o2oservice.export.trade.cmd.CalcTradeServiceFeeCmd;
import com.jdh.o2oservice.export.trade.dto.*;
import com.jdh.o2oservice.export.trade.query.*;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePatientPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePatientPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode.UNKNOWN_ERROR;

/**
 * @author: yangxiyu
 * @date: 2024/1/4 2:27 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class TradeApplicationImpl implements TradeApplication, TradeExtApplication {

    /**
     * jdhPromiseDomainService
     */
    @Resource
    private JdhPromiseDomainService jdhPromiseDomainService;

    /**
     * promiseAppointmentDraftRepository
     */
    @Resource
    private PromiseAppointmentDraftRepository promiseAppointmentDraftRepository;

    /**
     * settleUrlService
     */
    @Resource
    private SettleUrlService settleUrlService;

    /**
     * voucherRepository
     */
    @Resource
    private VoucherRepository voucherRepository;

    /**
     * jdOrderItemRepository
     */
    @Resource
    private JdOrderItemRepository jdOrderItemRepository;

    /**
     * jdOrderExtRepository
     */
    @Resource
    private JdOrderExtRepository jdOrderExtRepository;
    /**
     * jdOrderExtRepository
     */
    @Resource
    private JdServiceSettleApplication jdServiceSettleApplication;

    /**
     * jdOrderMoneyRepository
     */
    @Resource
    private JdOrderMoneyRepository jdOrderMoneyRepository;

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;
    /**
     * conditions
     */
    private final Map<String, DomainAbility> conditions = Maps.newConcurrentMap();

    /**
     * actions
     */
    private final Map<String, DomainAbility> actions = Maps.newConcurrentMap();

    /**
     * applicationContext
     */
    @Resource
    private ApplicationContext applicationContext;

    /**
     * jdOrderRefundApplication
     */
    @Resource
    private JdOrderRefundApplication jdOrderRefundApplication;
    /**
     * medicalPromiseApplication
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;
    /**
     * promiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;

    @Resource
    private TradeDomainService tradeDomainService;

    @Resource
    private TradeApplicationConverter tradeApplicationConverter;
    /**
     * feeConfigurationApplication
     */
    @Resource
    private FeeConfigurationApplication feeConfigurationApplication;
    @Resource
    private Cluster jimClient;
    /**
     * redisLockUtil
     */
    @Resource
    private RedisLockUtil redisLockUtil;

    @Resource
    private JdOrderApplication jdOrderApplication;
    /**
     * 生成ID工厂
     */
    @Resource
    private GenerateIdFactory generateIdFactory;
    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * jdhOrderDomainService
     */
    @Autowired
    private JdhOrderDomainService jdhOrderDomainService;

    /**
     * serviceFeeApplicaitonConverter
     */
    @Resource
    private ServiceFeeApplicationConverter serviceFeeApplicationConverter;

    @Resource
    private ProductApplication productApplication;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * promiseApplication
     */
    @Autowired
    private PromiseApplication promiseApplication;

    /**
     * inspectionSheetApplication
     */
    @Resource
    private InspectionSheetApplication inspectionSheetApplication;

    @Resource
    private SkuInfoRpc skuInfoRpc;

    /**
     * jdOrderRefundTaskRepository
     */
    @Autowired
    private JdOrderRefundTaskRepository jdOrderRefundTaskRepository;

    @Resource
    private JdhSkuRepository jdhSkuRepository;

    @Autowired
    private JdhAreaFeeConfigRepository jdhAreaFeeConfigRepository;

    @Autowired
    private StationApplication stationApplication;

    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * 线程池
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    @Autowired
    private VoucherApplication voucherApplication;

    @Autowired
    private AngelWorkRepository angelWorkRepository;

    @Autowired
    private PromiseHistoryRepository promiseHistoryRepository;

    @Resource
    private OrderInfoRpc orderInfoRpc;

    @Resource
    private VtpOrderInfoRpc vtpOrderInfoRpc;

    /**
     * promisePatientPoMapper
     */
    @Autowired
    private JdhPromisePatientPoMapper promisePatientPoMapper;

    @Autowired
    private SelfAppointRecordExportServiceRpc selfAppointRecordExportServiceRpc;

    /**
     * promiseGoRpcService
     */
    @Autowired
    private PromiseGoRpcService promiseGoRpcService;

    /**
     * 门店信息
     */
    @Autowired
    private ProviderStoreExportServiceRpc providerStoreExportServiceRpc;

    @Resource
    private AbTestConfiguration abTestConfiguration;
    @Autowired
    private AngelQueryApplication angelQueryApplication;

    /**
     * 健康地址rpc
     */
    @Resource
    private JdhAddressRpc jdhAddressRpc;

    @Resource
    private AngelShipDomainService angelShipDomainService;

    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * 服务者履约工单
     */
    @Resource
    private AngelWorkApplication angelWorkApplication;

    @Resource
    private PatientApplication patientApplication;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveDraft(SubmitAppointmentDraftCmd cmd) {
        SubmitAppointmentDraftContext context = PromiseApplicationConverter.INS.cmd2SubmitAppointmentDraftContext(cmd);
        context.init(PromiseEventTypeEnum.PROMISE_DRAFT_SUBMIT);

        log.info("TradeApplicationImpl->saveDraft context={}", JSON.toJSONString(context));
        jdhPromiseDomainService.submitDraft(context);
        promiseAppointmentDraftRepository.save(context.getDraft());

        // 创建结算页url
        GenerateSettleUrlContext settleUrlContext = TradeSettleApplicationConverter.ins.cmd2SubmitContext(cmd, context.getDraft().getMedicalLocId());
        settleUrlContext.init();
        // 构建结算页URL
        return settleUrlService.generateSettleUrl(settleUrlContext);
    }

    /**
     * 获取订单下预约单列表
     *
     * @param request
     * @return
     */
    @Override
    public List<OrderPromiseDto> queryOrderPromiseByList(OrderPromiseListRequest request) {
        if (StringUtils.isBlank(request.getOrderId())) {
            throw new BusinessException(TradeErrorCode.ORDER_ID_NULL);
        }
        JdOrder jdOrder = new JdOrder();
        jdOrder.setOrderId(Long.valueOf(request.getOrderId()));
        jdOrder.setUserPin(request.getUserPin());
        //查询订单信息
        JdOrder orderDetail = jdOrderRepository.findOrderDetail(jdOrder);
        //如果没有订单，返回空数据
        if (Objects.isNull(orderDetail)) {
            return new ArrayList<>();
        }

        PromiseRepQuery query = new PromiseRepQuery();
        //根据订单号查询服务单列表，将服务单id放入入参
        query.setVoucherIds(parseVoucherIds(Long.valueOf(request.getOrderId()), orderDetail.getVerticalCode()));
        //如果没有服务单，返回空数据
        if (CollectionUtil.isEmpty(query.getVoucherIds())) {
            return new ArrayList<>();
        }

        //查询预约单列表
        List<JdhPromise> list = promiseRepository.findList(query);
        log.info("TradeApplicationImpl->queryOrderPromiseByList list={}", JSON.toJSONString(list));
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        // 服务单ID
        List<Long> voucherIds = list.stream().map(JdhPromise::getVoucherId).collect(Collectors.toList());
        List<JdhVoucher> vouchers = voucherRepository.listByVoucherIds(voucherIds);
        log.info("TradeApplicationImpl->queryOrderPromiseByList vouchers={}", JSON.toJSONString(vouchers));
        Map<Long, JdhVoucher> vouMap = vouchers.stream().collect(Collectors.toMap(JdhVoucher::getVoucherId, Function.identity(), (o, n) -> o));

        List<OrderPromiseDto> data = new ArrayList<>(list.size());
        for (JdhPromise promise : list) {
            OrderPromiseDto promiseDto = buildOrderPromiseDto(promise, orderDetail, vouMap);
            data.add(promiseDto);
        }
        return data;
    }

    @Override
    public OrderUserActionDTO executeAction(OrderUserActionParam orderUserActionParam) {
        handelServiceUpgradeSelected(orderUserActionParam);
        buildAddressId(orderUserActionParam);
        buildPartnerSourceOrderId(orderUserActionParam);
        OrderUserActionContext orderUserActionContext = tradeApplicationConverter.convertOrderUserActionInfo(orderUserActionParam);
        OrderUserActionValueObject orderUserActionValueObject = tradeDomainService.executeAction(orderUserActionContext);
        OrderUserActionDTO orderUserActionDTO = tradeApplicationConverter.convertOrderUserActionDTO(orderUserActionValueObject);
        log.info("TradeApplicationImpl executeAction 结算页行为 orderUserActionContext:{} orderUserActionValueObject:{} orderUserActionDTO:{}", JSONObject.toJSONString(orderUserActionContext), JSONObject.toJSONString(orderUserActionValueObject), JSONObject.toJSONString(orderUserActionDTO));
        bundleShoppinglist(orderUserActionDTO, orderUserActionParam);
        buildAmountInfo(orderUserActionDTO, orderUserActionParam);
        buildServiceFeeDetail(orderUserActionDTO, orderUserActionParam);
        //增强服务类型 用于订单备注 知情同意书的动态展示
        enhanceServiceType(orderUserActionDTO, orderUserActionParam);
        orderUserActionDTO.setNotSupportServiceItemList(orderUserActionParam.getNotSupportServiceItemList());
        //处理商品加项逻辑
        handleAddProductLogic(orderUserActionDTO);

        // 构建意向护士选项
        buildIntendedNurseOption(orderUserActionDTO);
        //处理服务人员升级
        handelServiceUpgrade(orderUserActionDTO, orderUserActionParam);
        // 处理升级按钮点击切换的业务场景
        handelToggleServiceUpgrade(orderUserActionDTO, orderUserActionParam);
        // 放在最后，结算会改改业务类型（ 骑手->护士）处理eta预估信息
        handleEtaScript(orderUserActionDTO,orderUserActionParam);
        // 构建患者档案信息
        buildPatientDTOList(orderUserActionDTO, orderUserActionParam);
        // 如果用户点击了立即购买，set一天的缓存
        if (UserActionEnum.BUY_NOW.getUserActionType().equals(orderUserActionParam.getUserActionType())){
            log.info("TradeApplicationImpl.executeAction.setCache");
            setSkuCache4InventoryLogic(orderUserActionDTO);
        }
        return orderUserActionDTO;
    }

    /**
     * 处理升级按钮点击切换的业务场景
     *
     * @param orderUserActionDTO
     * @param orderUserActionParam
     */
    @LogAndAlarm
    private void handelToggleServiceUpgrade(OrderUserActionDTO orderUserActionDTO, OrderUserActionParam orderUserActionParam){
        // 1有预约时间
        if (Objects.isNull(orderUserActionParam.getAppointmentTimeParam())){
            log.info("TradeApplicationImpl handelNonInventory AppointmentTimeParam is null");
            return;
        }
        // 2 弹窗框有值场景
        if(Objects.nonNull(orderUserActionDTO) && Objects.nonNull(orderUserActionDTO.getServiceUpgradeDTO()) && Objects.nonNull(orderUserActionDTO.getServiceUpgradeDTO().getSelectedExclude())){
            log.info("TradeApplicationImpl handelNonInventory SelectedExclude:{} ", orderUserActionDTO.getServiceUpgradeDTO().getSelectedExclude());
            return;
        }

        // 3上次行为和本次行为在升级护士的选择上是否不一致
        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.EXECUTE_ACTION_BUY_NOW_PIN_SKU_LIST_KEY, orderUserActionParam.getUserPin());
        OrderUserActionDTO LastOrderUserActionDTO = JsonUtil.parseObject(jimClient.get(redisKey), OrderUserActionDTO.class);
        Boolean lastServiceUpgradeSelected = null;
        Boolean serviceUpgradeSelected = null;
        if(Objects.nonNull(LastOrderUserActionDTO) && Objects.nonNull(LastOrderUserActionDTO.getServiceUpgradeDTO()) && CollectionUtils.isNotEmpty(LastOrderUserActionDTO.getServiceUpgradeDTO().getServiceUpgradeItemList())){
            lastServiceUpgradeSelected = LastOrderUserActionDTO.getServiceUpgradeDTO().getServiceUpgradeItemList().get(0).getServiceUpgradeSelected();
        }
        if(Objects.nonNull(orderUserActionDTO) && Objects.nonNull(orderUserActionDTO.getServiceUpgradeDTO()) && CollectionUtils.isNotEmpty(orderUserActionDTO.getServiceUpgradeDTO().getServiceUpgradeItemList())){
            serviceUpgradeSelected = orderUserActionDTO.getServiceUpgradeDTO().getServiceUpgradeItemList().get(0).getServiceUpgradeSelected();
        }
        if(Objects.isNull(lastServiceUpgradeSelected)){
            lastServiceUpgradeSelected = false;
        }
        if(Objects.isNull(serviceUpgradeSelected)){
            serviceUpgradeSelected = false;
        }
        if(serviceUpgradeSelected.equals(lastServiceUpgradeSelected)){
            log.info("TradeApplicationImpl handelNonInventory lastServiceUpgradeSelected = serviceUpgradeSelected");
            return;
        }

        // 4有无库存
        boolean nonInventory = false;
        try {
            queryInventory(tradeApplicationConverter.convertoAppointmentTimeValueObject(orderUserActionParam.getAppointmentTimeParam()), orderUserActionDTO);
        } catch (Exception e){
            nonInventory = true;
        }
        if(nonInventory){
            log.info("TradeApplicationImpl handelNonInventory nonInventory is true");
            if(Objects.nonNull(orderUserActionDTO) && Objects.nonNull(orderUserActionDTO.getServiceUpgradeDTO())){
                orderUserActionDTO.getServiceUpgradeDTO().setSelectedExclude(2);
                if(String.valueOf(ServiceTypeNewEnum.ANGEL_TEST.getType()).equals(orderUserActionDTO.getServiceType())){
                    orderUserActionDTO.getServiceUpgradeDTO().setSelectedExcludeTips("抱歉，所选时间段护士已约满，选择升级后需重新选择其它上门时间");
                } else if(String.valueOf(ServiceTypeNewEnum.KNIGHT_TEST.getType()).equals(orderUserActionDTO.getServiceType())){
                    orderUserActionDTO.getServiceUpgradeDTO().setSelectedExcludeTips("抱歉，所选时间段送检员已约满，取消升级后需重新选择其它上门时间");
                }
            }
        }/* else {
            log.info("TradeApplicationImpl handelNonInventory nonInventory is false");
            if(Objects.nonNull(orderUserActionDTO) && Objects.nonNull(orderUserActionDTO.getServiceUpgradeDTO())){
                orderUserActionDTO.getServiceUpgradeDTO().setSelectedExclude(3);
                if(String.valueOf(ServiceTypeNewEnum.ANGEL_TEST.getType()).equals(orderUserActionDTO.getServiceType())){
                    orderUserActionDTO.getServiceUpgradeDTO().setSelectedExcludeTips("升级服务成功，将为您安排护士上门采样检测");
                } else if(String.valueOf(ServiceTypeNewEnum.KNIGHT_TEST.getType()).equals(orderUserActionDTO.getServiceType())){
                    orderUserActionDTO.getServiceUpgradeDTO().setSelectedExcludeTips("已取消服务升级，将为您安排送检员上门采样检测");
                }
            }
        }*/

    }

    private void handelServiceUpgradeSelected(OrderUserActionParam orderUserActionParam) {
        try {
            log.info("handelServiceUpgradeSelected handelServiceUpgradeSelected orderUserActionParam={}", JSON.toJSONString(orderUserActionParam));
            orderUserActionParam.setServiceUpgradeSwitchEnable(true);
            //2025-02-21 夜间服务费逻辑，判断互斥逻辑https://joyspace.jd.com/pages/cvv7KUNGmt1sub2q589i
            feeExcludeHandle(orderUserActionParam);
            // 服务人员升级选中
            if (BooleanUtils.isTrue(orderUserActionParam.getServiceUpgradeSelected())){
                log.info("TradeApplicationImpl handelServiceUpgradeSelected serviceUpgradeSelected true");
                //如果用户选中了服务人员升级，但楼层禁用了，移除选中楼层并返回提示
                if (!orderUserActionParam.getServiceUpgradeSwitchEnable()) {
                    orderUserActionParam.setServiceUpgradeSelected(false);
                    orderUserActionParam.setServiceUpgradeSelectedExclude(true);
                    return;
                }
                RedisKeyEnum keyEnum = RedisKeyEnum.TRADE_ACTION_SERVICE_UPGRADE_KEY;
                String cacheKey = RedisKeyEnum.getRedisKey(keyEnum, orderUserActionParam.getUserPin());
                jimClient.setEx(cacheKey, "true", keyEnum.getExpireTime(), keyEnum.getExpireTimeUnit());
            }
        } catch (Exception e) {
            log.error("TradeApplicationImpl handelServiceUpgradeSelected error e", e);
        }
    }

    /**
     * 判断服务费之间互斥逻辑
     * @param orderUserActionParam
     */
    private void feeExcludeHandle(OrderUserActionParam orderUserActionParam) {
        try {
            if (CollectionUtils.isNotEmpty(orderUserActionParam.getSkuInfoParamList())) {
                //取 sku 的 service Type
                String skuId = orderUserActionParam.getSkuInfoParamList().get(0).getSkuId();
                JdhSkuRequest request = new JdhSkuRequest();
                request.setSkuId(Long.valueOf(skuId));
                JdhSkuDto jdhSkuDto = productApplication.queryJdhSkuInfo(request);
                if(Objects.nonNull(jdhSkuDto)){
                    //查询当前业务身份下是否开启了夜间服务费
                    JdhFeeTimeConfig jdhFeeTimeConfig = getJdhFeeTimeConfig(orderUserActionParam, jdhSkuDto.getServiceType());
                    //查询将要升级的业务身份下是否开启了夜间服务费
                    JdhFeeTimeConfig upGradeFeeTimeConfig = getJdhFeeTimeConfig(orderUserActionParam, ServiceTypeNewEnum.ANGEL_TEST.getType());
                    //判断用户预约上门时间是否为夜间服务时间段
                    boolean isNightService = false;
                    if (Objects.nonNull(jdhFeeTimeConfig) && Objects.nonNull(orderUserActionParam.getAppointmentTimeParam())) {
                        LocalTime beginTime = LocalTime.parse(orderUserActionParam.getAppointmentTimeParam().getAppointmentStartTime().split(" ")[1], DateTimeFormatter.ofPattern("HH:mm"));
                        LocalTime endTime = beginTime.plusHours(1).withMinute(0).withSecond(0).withNano(0);
                        //处理开始时间为23:00的情况，加一小时后endtime变为0:00，需改为23:59
                        if (endTime.getHour() < beginTime.getHour() && endTime.getHour() == 0) {
                            endTime = endTime.plusMinutes(-1);
                        }
                        ArrayList<TimeIntervalIntersection.TimeInterval> timeIntervalList = Lists.newArrayList();
                        if (StringUtils.isNotBlank(jdhFeeTimeConfig.getMidnightStart()) && StringUtils.isNotBlank(jdhFeeTimeConfig.getMidnightEnd())) {
                            timeIntervalList.add(new TimeIntervalIntersection.TimeInterval(
                                    StringUtil.isNotBlank(jdhFeeTimeConfig.getMidnightStart()) ? LocalTime.parse(jdhFeeTimeConfig.getMidnightStart(), DateTimeFormatter.ofPattern("HH:mm")) : null,
                                    StringUtil.isNotBlank(jdhFeeTimeConfig.getMidnightEnd()) ? LocalTime.parse(jdhFeeTimeConfig.getMidnightEnd(), DateTimeFormatter.ofPattern("HH:mm")) : null));
                        }
                        if (StringUtils.isNotBlank(jdhFeeTimeConfig.getAfterMidnightStart()) && StringUtils.isNotBlank(jdhFeeTimeConfig.getAfterMidnightEnd())) {
                            timeIntervalList.add(new TimeIntervalIntersection.TimeInterval(
                                    StringUtil.isNotBlank(jdhFeeTimeConfig.getAfterMidnightStart()) ? LocalTime.parse(jdhFeeTimeConfig.getAfterMidnightStart(), DateTimeFormatter.ofPattern("HH:mm")) : null,
                                    StringUtil.isNotBlank(jdhFeeTimeConfig.getAfterMidnightEnd()) ? LocalTime.parse(jdhFeeTimeConfig.getAfterMidnightEnd(), DateTimeFormatter.ofPattern("HH:mm")) : null));
                        }

                        Optional<List<TimeIntervalIntersection.TimeInterval>> optional = TimeIntervalIntersection.groupCalculateIntersection(
                                new TimeIntervalIntersection.TimeInterval(beginTime,endTime),
                                timeIntervalList
                        );
                        isNightService = optional.isPresent();
                    }
                    orderUserActionParam.setNightServiceSelected(isNightService);
                    //如果预约上门时间是当前业务身份配置的夜间时间段，且将要升级的业务身份下配置夜间服务并且未开启夜间服务
                    if (isNightService && (Objects.nonNull(upGradeFeeTimeConfig) && !upGradeFeeTimeConfig.isEnable())) {
                        //服务升级楼层禁用
                        orderUserActionParam.setServiceUpgradeSwitchEnable(false);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("TradeApplicationImpl feeExcludeHandle error e ", e);
        }
    }

    /**
     *
     * @param orderUserActionParam
     * @param serviceType
     * @return
     */
    private JdhFeeTimeConfig getJdhFeeTimeConfig(OrderUserActionParam orderUserActionParam, Integer serviceType) {
        JdhFeeTimeConfig jdhFeeTimeConfig = null;
        Map<String, JdhFeeTimeConfig> nightServiceTimeMap = duccConfig.getNightServiceTimeMap();
        if (CollUtil.isNotEmpty(nightServiceTimeMap)) {
            StringBuilder stringBuilder = new StringBuilder();
            PartnerSourceEnum partnerSourceEnum = PartnerSourceEnum.fromCode(orderUserActionParam.getPartnerSource());
            if (Objects.nonNull(partnerSourceEnum)) {
                stringBuilder.append(partnerSourceEnum.getCode());
            }
            stringBuilder.append(serviceType);
            stringBuilder.append(Objects.nonNull(orderUserActionParam.getAppointmentTimeParam()) && Objects.equals(orderUserActionParam.getAppointmentTimeParam().getIsImmediately(), true) ? CommonConstant.ONE_STR : CommonConstant.ZERO_STR);
            log.info("TradeApplicationImpl -> feeExcludeHandle stringBuilder={}", stringBuilder.toString());
            ServiceHomeTypeEnum serviceHomeTypeEnum = ServiceHomeTypeEnum.getServiceHomeTypeEnum(stringBuilder.toString());
            if (Objects.nonNull(serviceHomeTypeEnum)) {
                jdhFeeTimeConfig = JSONUtil.toBean(
                        JSONUtil.toJsonStr(nightServiceTimeMap.get(serviceHomeTypeEnum.getVerticalCode())), JdhFeeTimeConfig.class);
            }
        }
        return jdhFeeTimeConfig;
    }


    private void handelServiceUpgrade(OrderUserActionDTO orderUserActionDTO, OrderUserActionParam orderUserActionParam){
        try {
            log.info("TradeApplicationImpl handelServiceUpgrade orderUserActionParam={}, orderUserActionDTO={}"
                    ,JSON.toJSONString(orderUserActionParam),JSON.toJSONString(orderUserActionDTO));

            List<String> skuList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderUserActionDTO.getBundleInfoDTOList())){
                // 获取sku信息
                List<String> bundleSkuList = orderUserActionDTO.getBundleInfoDTOList().get(0).getSkuItemDTOList().stream()
                        .map(SkuItemDTO::getSkuId).distinct().collect(Collectors.toList());
                log.info("TradeApplicationImpl handelServiceUpgrade bundleSkuList={}", JSON.toJSONString(bundleSkuList));
                if (CollectionUtils.isNotEmpty(bundleSkuList)){
                    skuList.addAll(bundleSkuList);
                }
            }

            // 加项品
            List<AddSkuItemDTO> addSkuItemDTOS = orderUserActionDTO.getAddSkuItemDTOS();
            if (CollectionUtils.isNotEmpty(addSkuItemDTOS)){
                List<Long> addItemSkuList = addSkuItemDTOS.stream().map(AddSkuItemDTO::getSkuId).distinct().collect(Collectors.toList());
                List<String> addItemSkuStrList = addItemSkuList.stream().map(Object::toString) .collect(Collectors.toList());
                skuList.addAll(addItemSkuStrList);
            }
            log.info("TradeApplicationImpl handelServiceUpgrade skuList={}", JSON.toJSONString(skuList));
            if (CollectionUtils.isEmpty(skuList)){
                return;
            }

            List<JdhSku> jdhSkuList = jdhSkuRepository.queryMultiSku(skuList.stream().map(s -> JdhSku.builder().skuId(Long.valueOf(s)).build())
                    .filter(Objects::nonNull).collect(Collectors.toList()));
            log.info("TradeApplicationImpl handelServiceUpgrade jdhSkuList={}", JSON.toJSONString(jdhSkuList));
            if (CollectionUtils.isEmpty(jdhSkuList)){
                log.info("TradeApplicationImpl handelServiceUpgrade jdhSkuList empty");
                return;
            }
            Map<String, String> verticalCodeBySkuInfoMap = duccConfig.getVerticalCodeBySkuInfoMap();
            String key = jdhSkuList.get(0).getChannelId() + "*" + jdhSkuList.get(0).getServiceType();
            // 业务身份
            String verticalCode = verticalCodeBySkuInfoMap.get(key);
            log.info("TradeApplicationImpl handelServiceUpgrade verticalCode={}", verticalCode);
            if (StringUtils.isBlank(verticalCode)){
                log.info("TradeApplicationImpl handelServiceUpgrade verticalCode empty");
                return;
            }
            // 判断骑手业务身份
            if (!Arrays.asList(ServiceHomeTypeEnum.XFYL_HOME_TEST_PHASE1.getVerticalCode(),ServiceHomeTypeEnum.NH_HOME_TEST_PHASE1.getVerticalCode())
                    .contains(verticalCode)){
                log.info("TradeApplicationImpl handelServiceUpgradeSelected no test_phase1");
                return;
            }
            // 地址信息
            AddressInfoDTO addressInfoDTO = orderUserActionDTO.getAddressInfoDTOList().get(0);
            // 计算服务费入参
            CalcTradeServiceFeeCmd calcTradeServiceFeeCmd = new CalcTradeServiceFeeCmd();
            calcTradeServiceFeeCmd.setUserPin(orderUserActionParam.getUserPin());
            calcTradeServiceFeeCmd.setSkuIds(new HashSet<>(skuList));
            // 预约时间
            calcTradeServiceFeeCmd.setAppointmentTime(orderUserActionParam.getAppointmentTimeParam());
            // 预约地址
            AddressUpdateParam addressInfo = new AddressUpdateParam();
            addressInfo.setProvinceId(addressInfoDTO.getProvinceId());
            addressInfo.setCityId(addressInfoDTO.getCityId());
            addressInfo.setCountyId(addressInfoDTO.getCountyId());
            addressInfo.setTownId(addressInfoDTO.getTownId());
            addressInfo.setAddressDetail(addressInfoDTO.getAddressDetail());
            calcTradeServiceFeeCmd.setAddressInfo(addressInfo);
            calcTradeServiceFeeCmd.setSearchServiceUpgrade(true);

            // 计算服务费
            List<TradeServiceFeeInfoDTO> tradeServiceFeeList = calcServiceFee(calcTradeServiceFeeCmd);
            log.info("TradeApplicationImpl handelServiceUpgrade calcTradeServiceFeeCmd={}, tradeServiceFeeList={}", JSON.toJSONString(calcTradeServiceFeeCmd)
                    , JSON.toJSONString(tradeServiceFeeList));
            if (CollectionUtils.isEmpty(tradeServiceFeeList)){
                log.info("TradeApplicationImpl handelServiceUpgrade tradeServiceFeeList empty");
                return;
            }
            tradeServiceFeeList = tradeServiceFeeList.stream().filter(t-> FeeAggregateTypeEnum.DYNAMIC_FEE.getSubType().equals(t.getAggregateSubType()))
                    .collect(Collectors.toList());
            log.info("TradeApplicationImpl handelServiceUpgrade filter tradeServiceFeeList={}", JSON.toJSONString(tradeServiceFeeList));
            if (CollectionUtils.isEmpty(tradeServiceFeeList)){
                log.info("TradeApplicationImpl handelServiceUpgrade filter tradeServiceFeeList empty");
                return;
            }
            TradeServiceFeeInfoDTO tradeServiceFee = tradeServiceFeeList.get(0);
//            if (tradeServiceFee.getServiceFee().compareTo(BigDecimal.ZERO) <= 0){
//                log.info("TradeApplicationImpl handelServiceUpgrade serviceFee zero");
//                return;
//            }

            List<TradeServiceFeeDetailDTO> serviceFeeDetailList = tradeServiceFee.getServiceFeeDetailList();
            serviceFeeDetailList = serviceFeeDetailList.stream().filter(t->t.getType().equals(JdOrderFeeTypeEnum.UPGRADE_ANGEL.getType())).collect(Collectors.toList());
            log.info("TradeApplicationImpl handelServiceUpgrade filter serviceFeeDetailList={}", JSON.toJSONString(serviceFeeDetailList));
            if (CollectionUtils.isEmpty(serviceFeeDetailList)){
                log.info("TradeApplicationImpl handelServiceUpgrade serviceFeeDetailList empty");
                return;
            }
            TradeServiceFeeDetailDTO tradeServiceFeeDetailDTO = serviceFeeDetailList.get(0);

            if (Objects.isNull(tradeServiceFeeDetailDTO) || Objects.isNull(tradeServiceFeeDetailDTO.getFee())){
                log.info("TradeApplicationImpl handelServiceUpgra  de fee null");
                return;
            }

            // 升级费为0 并且 升级减免为0 则不处理
            if (tradeServiceFeeDetailDTO.getFee().compareTo(BigDecimal.ZERO) <= 0 && (Objects.isNull(tradeServiceFeeDetailDTO.getDiscountFee()) || tradeServiceFeeDetailDTO.getDiscountFee().compareTo(BigDecimal.ZERO) <= 0)){
                log.info("TradeApplicationImpl handelServiceUpgrade fee zero");
                return;
            }

            JSONObject serviceUpgradeConfig = JSON.parseObject(duccConfig.getServiceUpgradeConfig());
            ServiceUpgradeDTO serviceUpgradeDTO = new ServiceUpgradeDTO();
            serviceUpgradeDTO.setMainTitle(Objects.equals(orderUserActionParam.getServiceUpgradeSwitchEnable(), false) ? Optional.ofNullable(serviceUpgradeConfig.getString("mainTitleDisable")).orElse("当前时段不支持护土升级") : serviceUpgradeConfig.getString("mainTitle"));
            serviceUpgradeDTO.setEnable(orderUserActionParam.getServiceUpgradeSwitchEnable());
            if(orderUserActionParam.getServiceUpgradeSelectedExclude()) {
                serviceUpgradeDTO.setSelectedExclude(1);
            }
            serviceUpgradeDTO.setSelectedExcludeTips(Objects.equals(orderUserActionParam.getServiceUpgradeSelectedExclude(), true)
                            ? "当前时段不支持护士升级，已为您移除升级服务" : "");

            List<ServiceUpgradeItemDTO> serviceUpgradeItemList = new ArrayList<>();
            ServiceUpgradeItemDTO serviceUpgradeItem = new ServiceUpgradeItemDTO();
            serviceUpgradeItem.setUpgradeAngelFee(tradeServiceFeeDetailDTO.getFee());
            serviceUpgradeItem.setOriginalFee(tradeServiceFeeDetailDTO.getOriginalFee());
            serviceUpgradeItem.setDiscountFee(tradeServiceFeeDetailDTO.getDiscountFee());
            serviceUpgradeItem.setTitle(serviceUpgradeConfig.getString("title"));
            serviceUpgradeItem.setShortTitle(serviceUpgradeConfig.getString("shortTitle"));
            serviceUpgradeItem.setDescription(serviceUpgradeConfig.getString("description"));
            serviceUpgradeItem.setIcon(serviceUpgradeConfig.getString("icon"));
            serviceUpgradeItem.setLabel(serviceUpgradeConfig.getString("label"));
            serviceUpgradeItem.setServiceUpgradeSelected(orderUserActionParam.getServiceUpgradeSelected());

            if(BooleanUtils.isFalse(serviceUpgradeDTO.getEnable())) {
                serviceUpgradeItem.setServiceUpgradeSelected(false);
            }

            Map<String, String> extendMap = tradeServiceFeeDetailDTO.getExtendMap();
            if(MapUtils.isNotEmpty(extendMap) && extendMap.containsKey(ServiceFeeExtendEnum.DISCOUNT_FEE_ACTION.getExtType())){
                DiscountFeeAction discountFeeAction = JSON.parseObject(extendMap.get(ServiceFeeExtendEnum.DISCOUNT_FEE_ACTION.getExtType()), ServiceFeeExtendEnum.DISCOUNT_FEE_ACTION.getClazz());
                if(Objects.nonNull(discountFeeAction) ){
                    serviceUpgradeItem.setAutoSelected(discountFeeAction.getAutoSelected());
                    serviceUpgradeItem.setInvertSelect(discountFeeAction.getInvertSelect());
                    // 自动选中
                    if(discountFeeAction.getAutoSelected() && BooleanUtils.isTrue(serviceUpgradeDTO.getEnable())) {
                        // 不能反选
                        if(BooleanUtils.isNotTrue(discountFeeAction.getInvertSelect())){
                            serviceUpgradeItem.setServiceUpgradeSelected(true);
                        } else {
                            // 能反选 且 用户没有操作升级按钮
                            if(Objects.isNull(orderUserActionParam.getServiceUpgradeSelected())){
                                serviceUpgradeItem.setServiceUpgradeSelected(true);
                            }
                        }
                    }
                    if(Objects.nonNull(discountFeeAction.getDisplay())) {
                        serviceUpgradeDTO.setTips(discountFeeAction.getDisplay().getTips());
                    }
                    // 互医场景下，自动选中升级+有减免 24小时内给出一个toast提示
                    if (orderUserActionParam.getPartnerSource() != null && PartnerSourceEnum.fromCode(orderUserActionParam.getPartnerSource()) != null && (PartnerSourceEnum.JDH_NETDIAG.getCode().intValue() == orderUserActionParam.getPartnerSource().intValue())){
                        if (serviceUpgradeItem.getDiscountFee().compareTo(BigDecimal.ZERO) >= 0 && discountFeeAction.getAutoSelected()){
                            String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.NH_SETTLE_DISCOUNT_FEE_CONFIRM, orderUserActionParam.getUserPin());
                            if(!jimClient.exists(redisKey) && Objects.nonNull(discountFeeAction.getDisplay())){
                                serviceUpgradeDTO.setNhToast(discountFeeAction.getDisplay().getNhToast());
                                jimClient.set(redisKey, "1", RedisKeyEnum.NH_SETTLE_DISCOUNT_FEE_CONFIRM.getExpireTime(), RedisKeyEnum.NH_SETTLE_DISCOUNT_FEE_CONFIRM.getExpireTimeUnit(), false);
                            }
                        }
                    }
                }
            }
            serviceUpgradeItemList.add(serviceUpgradeItem);

            serviceUpgradeDTO.setServiceUpgradeItemList(serviceUpgradeItemList);
            orderUserActionDTO.setServiceUpgradeDTO(serviceUpgradeDTO);
            // 服务人员升级选中
            if (BooleanUtils.isTrue(serviceUpgradeItem.getServiceUpgradeSelected())){
                // 若选择升级为护士上门，则展示《京东健康护士上门服务知情同意书》
                orderUserActionDTO.setServiceType(String.valueOf(2));
                // 若选择了升级为护士上门，则业务身份为“护士上门检测”，不展示预计出报告时间。若不选择升级为护士上门，则为“骑手上门检测”计算预计出报告时间；
                orderUserActionDTO.setScriptDto(null);
            }
        } catch (Exception e) {
            log.error("TradeApplicationImpl handelServiceUpgrade error e", e);
        } finally {
            String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.TRADE_ACTION_SERVICE_UPGRADE_KEY, orderUserActionParam.getUserPin());
            jimClient.del(cacheKey);
        }
    }

    /**
     * 处理 eta 脚本
     *
     * @param orderUserActionDTO   orderUserActionDTO
     * @param orderUserActionParam orderUserActionParam
     */
    private void handleEtaScript(OrderUserActionDTO orderUserActionDTO, OrderUserActionParam orderUserActionParam) {
        try {
            //如果总开关是打开的
            if (Boolean.TRUE.equals(duccConfig.getSettlementEtaSwitch())) {
                //用行为返回的serviceType进行判断，如果这里没有返回，下面在buildAppointmentServiceList方法中会取到商品上的serviceType判断。
                String userActionDTOServiceType = orderUserActionDTO.getServiceType();
                if(StrUtil.isNotEmpty(userActionDTOServiceType)
                        && !ServiceTypeNewEnum.KNIGHT_TEST.getType().equals(Integer.parseInt(userActionDTOServiceType))){
                    log.info("TradeApplicationImpl handleEtaScript 非骑手，不处理eta信息");
                    return;
                }

                //构建promisego商品信息入参
                List<PromisegoRequestAppointmentService> appointmentServiceList = buildAppointmentServiceList(orderUserActionDTO);
                log.info("TradeApplicationImpl handleEtaScript appointmentServiceList:{}",JSON.toJSONString(appointmentServiceList));
                if(CollUtil.isEmpty(appointmentServiceList)) {
                    log.info("TradeApplicationImpl handleEtaScript appointmentServiceList 商品信息为空，不处理预估数据");
                    return;
                }


                //构建promisego预约地址入参
                PromisegoRequestAddress requestAddress = buildPromisegoRequestAddress(orderUserActionDTO,appointmentServiceList);
                log.info("TradeApplicationImpl handleEtaScript requestAddress:{}",JSON.toJSONString(requestAddress));
                if(Objects.isNull(requestAddress)) {
                    log.info("TradeApplicationImpl handleEtaScript requestAddress 地址信息为空，不处理预估数据");
                    return;
                }

                //构建promisego的预约时间查询入参
                PromisegoRequestAppointmentTime requestAppointmentTime = buildPromisegoRequestAppointmentTime(requestAddress,appointmentServiceList,orderUserActionParam,orderUserActionDTO);
                log.info("TradeApplicationImpl handleEtaScript requestAppointmentTime:{}",JSON.toJSONString(requestAppointmentTime));
                if(Objects.isNull(requestAppointmentTime)) {
                    log.info("TradeApplicationImpl handleEtaScript requestAppointmentTime 预约时间为空，不处理预估数据");
                    return;
                }


                //查询预测信息
                PreUserPromisegoBo promisegoBo = promiseGoRpcService.queryPreUserPromisego(PreUserPromisegoRequestBo.builder()
                        .businessMode(ServiceTypeNewEnum.getByType(appointmentServiceList.get(0).getServiceType()).getBusinessModeEnum().getCode())
                        .scene(PromisegoQuerySceneEnum.SETTLEMENT.getScene())
                        .appointmentTime(requestAppointmentTime)
                        .appointmentAddress(requestAddress)
                        .appointmentServiceList(appointmentServiceList)
                        .preSampleFlag(orderUserActionParam.getPreSampleFlag())
                        .build());
                log.info("TradeApplicationImpl handleEtaScript promisegoBo:{}",JSON.toJSONString(promisegoBo));
                if(Objects.nonNull(promisegoBo) && Objects.nonNull(promisegoBo.getScript()) && StrUtil.isNotBlank(promisegoBo.getScript().getScriptContent())) {
                    //是否使用eta结果
                    if(duccConfig.getUseSettlementEtaSwitch()){
                        //如果白名单为空，或者白名单不为空，但是当前pin在白名单里
                        if(CollUtil.isEmpty(duccConfig.getSettlementEtaPin())
                                || (CollUtil.isNotEmpty(duccConfig.getSettlementEtaPin()) && duccConfig.getSettlementEtaPin().contains(orderUserActionDTO.getUserPin()))
                            ){
                            try {
                                //处理 ab逻辑
                                AbTestExp etaAb = abTestConfiguration.callAbTestResult(orderUserActionDTO.getUserPin(), "etaAb");
                                if(Objects.nonNull(etaAb) && !"base".equals(etaAb.getLabel())){
                                    orderUserActionDTO.setScriptDto(ScriptDto.builder().scriptContent(promisegoBo.getScript().getScriptContent()).build());
                                }
                            } catch (Exception e) {
                                log.error("getProductDetailEta callAbTestResult exception",e);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("TradeApplicationImpl handleEtaScript exception",e);
        }

    }

    /**
     * buildAppointmentServiceList
     *
     * @param orderUserActionDTO orderUserActionDTO
     * @return {@link List }<{@link PromisegoRequestAppointmentService }>
     */
    private List<PromisegoRequestAppointmentService> buildAppointmentServiceList(OrderUserActionDTO orderUserActionDTO){
        //全部商品，按 isSelected  isAdded 过滤
        if(CollectionUtil.isEmpty(orderUserActionDTO.getBundleInfoDTOList())){
            return null;
        }
        List<SkuItemDTO> skuItemDTOList = orderUserActionDTO.getBundleInfoDTOList().get(0).getSkuItemDTOList();
        if(CollectionUtil.isEmpty(skuItemDTOList)){
            return null;
        }
        List<SkuItemDTO> selectedSkuList = skuItemDTOList.stream().filter(ele -> Boolean.TRUE.equals(ele.getIsSelected())).collect(Collectors.toList());
        if(CollUtil.isEmpty(selectedSkuList)){
            return null;
        }


        Map<Long, SkuItemDTO> selectedSkuMap = selectedSkuList.stream().collect(Collectors.toMap(ele -> Long.parseLong(ele.getSkuId()), Function.identity()));
        //获取商品配置的item信息。
        Map<Long, JdhSkuDto> skuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder().skuIdList(selectedSkuMap.keySet()).queryServiceItem(Boolean.TRUE).build());

        if(MapUtil.isEmpty(skuDtoMap)){
            return null;
        }

        List<PromisegoRequestAppointmentService> result = new ArrayList<>();
        for (Map.Entry<Long, SkuItemDTO> itemDTOEntry : selectedSkuMap.entrySet()) {
            Long skuId = itemDTOEntry.getKey();
            SkuItemDTO skuItemDTO = itemDTOEntry.getValue();
            JdhSkuDto jdhSkuDto = skuDtoMap.get(skuId);

            ServiceTypeNewEnum serviceTypeNewEnum = ServiceTypeNewEnum.getByType(jdhSkuDto.getServiceType());
            if(ServiceTypeNewEnum.KNIGHT_TEST != serviceTypeNewEnum){
                log.info("TradeApplicationImpl buildAppointmentServiceList 构建商品信息，非骑手业务，不处理");
                return null;
            }
            PromisegoRequestAppointmentService ele = new PromisegoRequestAppointmentService();
            ele.setSkuId(skuId);
            ele.setSkuType(skuItemDTO.getIsAdded());
            ele.setServiceType(jdhSkuDto.getServiceType());

            List<PromisegoRequestAppointmentServiceItem> serviceItemList = jdhSkuDto.getServiceItemList().stream().map(serviceItem ->
                            PromisegoRequestAppointmentServiceItem.builder().itemId(serviceItem.getItemId()).itemName(serviceItem.getItemName()).build())
                    .collect(Collectors.toList());
            ele.setItemList(serviceItemList);

            result.add(ele);
        }
        return result;
    }

    /**
     * 构建promisego请求地址
     *
     * @param orderUserActionDTO orderUserActionDTO
     * @return {@link PromisegoRequestAddress }
     */
    private PromisegoRequestAddress buildPromisegoRequestAddress(OrderUserActionDTO orderUserActionDTO,List<PromisegoRequestAppointmentService> appointmentServiceList){
        //入参 预约地址 按 selected 选中状态过滤一条
        List<AddressInfoDTO> addressInfoDTOList = orderUserActionDTO.getAddressInfoDTOList();
        if(CollUtil.isEmpty(addressInfoDTOList)){
            return null;
        }
        List<AddressInfoDTO> filtedAddressList = addressInfoDTOList.stream().filter(ele -> Boolean.TRUE.equals(ele.isSelected())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(filtedAddressList)){
            return null;
        }
        AddressInfoDTO addressInfoDTO = filtedAddressList.get(NumConstant.NUM_0);

        String addressId = String.valueOf(addressInfoDTO.getId());
        //查服务站列表
        AddressDetail addressDetail = new AddressDetail();
        addressDetail.setAddressId(addressId);
        addressDetail.setFullAddress(addressInfoDTO.getFullAddress());

        StationGeoForManQuery stationQuery = new StationGeoForManQuery();
        //过滤非主品的商品
        stationQuery.setSkuNos(appointmentServiceList.stream().filter(ele -> !NumConstant.NUM_1.equals(ele.getSkuType())).map(PromisegoRequestAppointmentService::getSkuId).collect(Collectors.toSet()));
        stationQuery.setAddressDetailList(CollUtil.toList(addressDetail));
        List<JdhStationDto> jdhStationDtoList = stationApplication.queryJdhStationGeoForMan(stationQuery);
        log.info("TradeApplicationImpl buildPromisegoRequestAddress jdhStationDtoList: {}", JSON.toJSONString(jdhStationDtoList));
        if(CollUtil.isEmpty(jdhStationDtoList)){
            return null;
        }
        Map<String, JdhStationDto> addressStationMap = jdhStationDtoList.stream().collect(Collectors.toMap(JdhStationDto::getAddressId, Function.identity()));
        JdhStationDto jdhStationDto = addressStationMap.get(addressId);
        List<AngelStationDto> stationDtoList = jdhStationDto.getStationDtoList();
        List<StoreInfoBo> storeInfoBos = providerStoreExportServiceRpc.listByStoreIds(stationDtoList.stream().map(AngelStationDto::getStationId).collect(Collectors.toSet()));
        if(CollUtil.isEmpty(storeInfoBos)){
            return null;
        }

        Map<String, StoreInfoBo> storeInfoBoMap = storeInfoBos.stream().collect(Collectors.toMap(StoreInfoBo::getJdStoreId, Function.identity()));

        List<PromisegoAngelStation> jdhStationList = new ArrayList<>();
        for (AngelStationDto angelStationDto : stationDtoList) {
            PromisegoAngelStation promisegoAngelStation = station2PromisegoAngelStation(angelStationDto, storeInfoBoMap.get(angelStationDto.getStationId()));
            jdhStationList.add(promisegoAngelStation);
        }

        //构建返回
        return PromisegoRequestAddress.builder()
                .addressId(addressId)
                .provinceId(addressInfoDTO.getProvinceId())
                .cityId(addressInfoDTO.getCityId())
                .countyId(addressInfoDTO.getCountyId())
                .townId(Objects.nonNull(addressInfoDTO.getTownId()) && addressInfoDTO.getTownId() > 0 ? addressInfoDTO.getTownId() : null)
                .provinceName(addressInfoDTO.getProvinceName())
                .cityName(addressInfoDTO.getCityName())
                .countyName(addressInfoDTO.getCountyName())
                .townName(StrUtil.isNotBlank(addressInfoDTO.getTownName()) ? addressInfoDTO.getTownName() : null)
                .fullAddress(addressInfoDTO.getFullAddress())
                .latitude(addressInfoDTO.getGcLat())
                .longitude(addressInfoDTO.getGcLng())
                .coordType(addressInfoDTO.getCoordType())
                .angelStationList(jdhStationList)
                .build();
    }

    /**
     * station2PromisegoAngelStation
     *
     * @param angelStationDto 服务站
     * @return {@link PromisegoAngelStation }
     */
    private PromisegoAngelStation station2PromisegoAngelStation(AngelStationDto angelStationDto,StoreInfoBo storeInfoBo) {
        PromisegoAngelStation jdhStation = new PromisegoAngelStation();
        jdhStation.setAngelStationId(angelStationDto.getAngelStationId());
        jdhStation.setAngelStationName(angelStationDto.getAngelStationName());
        jdhStation.setStationId(angelStationDto.getStationId());
        jdhStation.setStationName(angelStationDto.getStationName());
        jdhStation.setStationProvinceId(storeInfoBo.getProvinceId());
        jdhStation.setStationCityId(storeInfoBo.getCityId());
        jdhStation.setStationCountyId(storeInfoBo.getCountyId());
        jdhStation.setStationTownId(null);
        jdhStation.setStationProvinceName(storeInfoBo.getProvinceName());
        jdhStation.setStationCityName(storeInfoBo.getCityName());
        jdhStation.setStationCountyName(storeInfoBo.getCountyName());
        jdhStation.setStationTownName(null);
        jdhStation.setStationFullAddress(storeInfoBo.getStoreAddr());
        jdhStation.setStationLongitude(Double.valueOf(storeInfoBo.getLng()));
        jdhStation.setStationLatitude(Double.valueOf(storeInfoBo.getLat()));
        jdhStation.setStationCoordType(null);
        return jdhStation;
    }

    /**
     * 构建promisego请求预约时间
     *
     * @param orderUserActionParam 订单用户操作参数
     * @return {@link PromisegoRequestAppointmentTime }
     */
    private PromisegoRequestAppointmentTime buildPromisegoRequestAppointmentTime(PromisegoRequestAddress requestAddress,List<PromisegoRequestAppointmentService> appointmentServiceList,
                                                                                 OrderUserActionParam orderUserActionParam,OrderUserActionDTO orderUserActionDTO){

        Date now = new Date();
        //入参 预约时间
        AppointmentTimeParam appointmentTimeParam = orderUserActionParam.getAppointmentTimeParam();

        //如果用户没有选择时间，系统反查，取当天最早可约时间段
        if(Objects.isNull(appointmentTimeParam)){
            List<Long> skuIds = appointmentServiceList.stream().map(PromisegoRequestAppointmentService::getSkuId).collect(Collectors.toList());
            XfylAppointDateTimeDTO recentlyAvailableAppointmentTime = findRecentlyAvailableAppointmentTime(orderUserActionDTO.getUserPin(), skuIds, requestAddress.getAddressId(), requestAddress.getFullAddress());
            log.info("TradeApplicationImpl  buildPromisegoRequestAppointmentTime recentlyAvailableAppointmentTime:{}",JSON.toJSONString(recentlyAvailableAppointmentTime));
            if(Objects.isNull(recentlyAvailableAppointmentTime)){
                return null;
            }else{
                AppointmentTimeParam recentlyTime = new AppointmentTimeParam();
                recentlyTime.setDateType(recentlyAvailableAppointmentTime.getDateType());
                recentlyTime.setIsImmediately(recentlyAvailableAppointmentTime.getIsImmediately());
                recentlyTime.setAppointmentStartTime(recentlyAvailableAppointmentTime.getAppointmentStartTime());
                recentlyTime.setAppointmentEndTime(recentlyAvailableAppointmentTime.getAppointmentEndTime());
                appointmentTimeParam = recentlyTime;
            }
        } else{
            try {
                //如果有库存则不抛异常
                queryInventory(tradeApplicationConverter.convertoAppointmentTimeValueObject(orderUserActionParam.getAppointmentTimeParam()), orderUserActionDTO);
            } catch (Exception e){
                log.error("TradeApplicationImpl  buildPromisegoRequestAppointmentTime 入参的预约时段 无库存可用",e);
                return null;
            }
        }

        if(!DateUtil.isSameDay(now,TimeUtils.timeStrToDate(appointmentTimeParam.getAppointmentStartTime(), TimeFormat.LONG_PATTERN_LINE_NO_S))){
            log.info("TradeApplicationImpl  buildPromisegoRequestAppointmentTime recentlyAvailableAppointmentTime 非当天可约时段");
            return null;
        }

        //转换为查询的预约时间入参
        return PromisegoRequestAppointmentTime.builder()
                .appointmentStartTime(TimeUtils.timeStrToLocalDate(appointmentTimeParam.getAppointmentStartTime(), TimeFormat.LONG_PATTERN_LINE_NO_S))
                .appointmentEndTime(TimeUtils.timeStrToLocalDate(appointmentTimeParam.getAppointmentEndTime(), TimeFormat.LONG_PATTERN_LINE_NO_S))
                .immediately(appointmentTimeParam.getIsImmediately())
                .dateType(appointmentTimeParam.getDateType())
                .build();
    }

    /**
     * 查找最近可用预约时间
     *
     * @param userPin           用户pin
     * @param skuIds             skuIds
     * @param addressId         addressId
     * @param fullAddress       fullAddress
     * @return {@link XfylAppointDateTimeDTO }
     */
    private XfylAppointDateTimeDTO findRecentlyAvailableAppointmentTime(String userPin, List<Long> skuIds,
                                                                        String addressId,String fullAddress){
        AvaiableAppointmentTimeParam appointmentTimeParam = new AvaiableAppointmentTimeParam();
        appointmentTimeParam.setUserPin(userPin);
        appointmentTimeParam.setSkuIds(skuIds);
        appointmentTimeParam.setFullAddress(fullAddress);
        appointmentTimeParam.setAddressId(addressId);

        AvaiableAppointmentTimeDTO availableAppointmentTime = this.queryAvaiableAppointmentTime(appointmentTimeParam);
        if(Objects.nonNull(availableAppointmentTime)){
            List<XfylAppointDateDTO> compatibleGroupDto = availableAppointmentTime.getCompatibleGroupDTO();
            if(CollUtil.isNotEmpty(compatibleGroupDto)){
                Date now = new Date();
                for (XfylAppointDateDTO xfylAppointDateDTO : compatibleGroupDto) {
                    List<XfylAppointDateTimeGroupDTO> appointDateTimeGroupDTOList = xfylAppointDateDTO.getAppointDateTimeGroupDTOList();
                    Integer dateType = xfylAppointDateDTO.getDateType();
                    for (XfylAppointDateTimeGroupDTO xfylAppointDateTimeGroupDTO : appointDateTimeGroupDTOList) {
                        for (XfylAppointDateTimeDTO xfylAppointDateTimeDTO : xfylAppointDateTimeGroupDTO.getDateTimeDTOList()) {
                            Integer status = xfylAppointDateTimeDTO.getStatus();
                            String appointmentStartTime = xfylAppointDateTimeDTO.getAppointmentStartTime();
                            Date appointmentStartTimeDate = TimeUtils.timeStrToDate(appointmentStartTime, TimeFormat.LONG_PATTERN_LINE_NO_S);
                            //如果可约,并且是今天的
                            if(NumConstant.NUM_1.equals(status) && DateUtil.isSameDay(now,appointmentStartTimeDate)){
                                xfylAppointDateTimeDTO.setDateType(dateType);
                                return xfylAppointDateTimeDTO;
                            }
                        }

                    }
                }
            }
        }
        return null;
    }



    private void buildIntendedNurseOption(OrderUserActionDTO orderUserActionDTO) {
        try {
            List<BundleInfoDTO> bundleInfoDTOList = orderUserActionDTO.getBundleInfoDTOList();
            if (CollectionUtils.isEmpty(bundleInfoDTOList)){
                log.info("TradeApplicationImpl buildIntendedNurseOption bundleInfoDTOList empty");
                return;
            }
            List<SkuItemDTO> skuItemDTOList = bundleInfoDTOList.get(0).getSkuItemDTOList().stream().filter(b -> Boolean.TRUE.equals(b.getIsSelected()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(skuItemDTOList)){
                log.info("TradeApplicationImpl buildIntendedNurseOption skuItemDTOList empty");
                return;
            }
            SkuItemDTO skuItemDTO = skuItemDTOList.get(0);
            JdhSkuRequest request = new JdhSkuRequest();
            request.setSkuId(Long.valueOf(skuItemDTO.getId()));
            JdhSkuDto jdhSkuDto = productApplication.queryJdhSkuInfo(request);
            List<Integer> serviceTypeConfigList = JSON.parseArray(duccConfig.getIntendedNurseOptionServiceTypeConfig(), Integer.class);
            if (serviceTypeConfigList.contains(jdhSkuDto.getServiceType())){
                orderUserActionDTO.setIntendedNurseDisplay(true);
            }
        } catch (Exception e) {
            log.error("TradeApplicationImpl buildIntendedNurseOption error e", e);
        }
    }

    /**
     *  设置sku缓存用于库存逻辑的计算
     * @param orderUserActionDTO
     */
    private void setSkuCache4InventoryLogic(OrderUserActionDTO orderUserActionDTO) {
        log.info("TradeApplicationImpl.setSkuCache4InventoryLogic.orderUserActionDTO={}", JSON.toJSONString(orderUserActionDTO));
        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.EXECUTE_ACTION_BUY_NOW_PIN_SKU_LIST_KEY, orderUserActionDTO.getUserPin());
        jimClient.set(redisKey,JSON.toJSONString(orderUserActionDTO));
        log.info("TradeApplicationImpl.setSkuCache4InventoryLogic.redisKey={},value={}", redisKey,JSON.toJSONString(orderUserActionDTO));
        jimClient.expire(redisKey, RedisKeyEnum.EXECUTE_ACTION_BUY_NOW_PIN_SKU_LIST_KEY.getExpireTime(), RedisKeyEnum.EXECUTE_ACTION_BUY_NOW_PIN_SKU_LIST_KEY.getExpireTimeUnit());
    }

    /**
     * 处理商品加项逻辑后置逻辑
     *
     * @param
     * @param orderUserActionDTO
     */
    private void handleAddProductLogic(OrderUserActionDTO orderUserActionDTO) {
        try {
            /**step1.查询加项品*/
            Map<Long, AddProductDto> addProductMap = queryAddProductDto(orderUserActionDTO);
            /**step2.组装加项品dto并维护是否选中状态*/
            List<AddSkuItemDTO> addSkuItemDTOS =  convertAddProductMap2AddSkuItemDTOS(addProductMap,orderUserActionDTO);
            //将加项商品List维护到对应的实体中
            orderUserActionDTO.setAddSkuItemDTOS(addSkuItemDTOS);
            /**step3.将原有orderUserActionDTO.bundleInfoDTOList以及skuItemDTOList打上标识：是否为加项 */
            handleBundleAndSkuItemDtoLogic(addProductMap,orderUserActionDTO);
        }catch (Exception e){
            log.error("TradeApplicationImpl.handleAddProductLogic has error",e);
        }

    }

    /**
     * 处理分堆的商家信息和sku信息 打上是否为加项品的标记
     * @param addProductMap
     * @param orderUserActionDTO
     */
    private void handleBundleAndSkuItemDtoLogic(Map<Long, AddProductDto> addProductMap, OrderUserActionDTO orderUserActionDTO) {
        //所有加项品的list
        Set<Long> addProductSet =new HashSet<>();
        addProductMap.values().forEach(addSkuDto -> {
            for (AddSkuItemDTO addSkuItemDTO : addSkuDto.getAddSkuItemDTOS()) {
                addProductSet.add(addSkuItemDTO.getSkuId());
            }
        });
        log.info("TradeApplicationImpl.handleBundleAndSkuItemDtoLogic.addProductMap={},addProductSet={}",JSON.toJSONString(addProductMap),JSON.toJSONString(addProductSet));
        List<SkuItemDTO> skuItemDtoList = orderUserActionDTO.getBundleInfoDTOList().get(0).getSkuItemDTOList();
        //orderUserActionDTO.bundleInfoDTOList 是否为加项品字段维护
        for (VenderInfoDTO venderInfoDTO : orderUserActionDTO.getVenderInfoDTOList()) {
            for (BundleInfoDTO bundleInfoDTO : venderInfoDTO.getBundleInfoDTOList()) {
                for (SkuItemDTO skuItemDTO : bundleInfoDTO.getSkuItemDTOList()) {
                    //是否为加项品 字段维护
                    skuItemDTO.setIsAdded(addProductSet.contains(Long.valueOf(skuItemDTO.getId()))? IsAddedEnum.IS_ADDED.getValue():IsAddedEnum.IS_NOT_ADDED.getValue());
                }
            }
        }
        log.info("TradeApplicationImpl.handleBundleAndSkuItemDtoLogic.addProductMap={},orderUserActionDTO={}",JSON.toJSONString(addProductMap),JSON.toJSONString(orderUserActionDTO));
    }

    /**
     * 将查询回的加项品转换为加项品并维护是否选中状态
     * @param addProductMap
     * @param orderUserActionDTO
     * @return
     */
    private List<AddSkuItemDTO> convertAddProductMap2AddSkuItemDTOS(Map<Long, AddProductDto> addProductMap,OrderUserActionDTO orderUserActionDTO) {
        log.info("TradeApplicationImpl.convertAddProductMap2AddSkuItemDTOS.addProductMap={},orderUserActionDTO={}",JSON.toJSONString(addProductMap),JSON.toJSONString(orderUserActionDTO));
        //a.已经加过购物车的skuList，为skuListInCart
        List<String> skuListInCart = new ArrayList<>();
        List<BundleInfoDTO> bundleInfoDTOList = orderUserActionDTO.getBundleInfoDTOList();
        if (CollectionUtils.isNotEmpty(bundleInfoDTOList) && CollectionUtils.isNotEmpty(bundleInfoDTOList.get(0).getSkuItemDTOList())){
            skuListInCart = bundleInfoDTOList.get(0).getSkuItemDTOList().stream().map(SkuItemDTO::getId).collect(Collectors.toList());
        }
        //b.加项商品的addSkuItemDTOS
        List<AddSkuItemDTO> addSkuItemDTOS = new ArrayList<>();
        if (MapUtils.isNotEmpty(addProductMap)){
            for (AddProductDto addProductDto : addProductMap.values()) {
                for (AddSkuItemDTO addSkuItemDTO : addProductDto.getAddSkuItemDTOS()) {
                    AddSkuItemDTO addSkuItem = JSON.parseObject(JSON.toJSONString(addSkuItemDTO), AddSkuItemDTO.class);
                    //维护加项dto里的isSelected字段
                    addSkuItem.setIsSelected(skuListInCart.contains(String.valueOf(addSkuItem.getSkuId())));
                    //将加项商品添加到加项list中
                    addSkuItemDTOS.add(addSkuItem);
                }
            }
            //过滤掉重复元素
            addSkuItemDTOS = addSkuItemDTOS.stream().distinct().collect(Collectors.toList());
        }
        log.info("TradeApplicationImpl.convertAddProductMap2AddSkuItemDTOS.addSkuItemDTOS={}",JSON.toJSONString(addSkuItemDTOS));
        return addSkuItemDTOS;
    }


    /**
     * 查询加项商品
     * @param orderUserActionDTO
     * @return
     */
    private Map<Long, AddProductDto> queryAddProductDto(OrderUserActionDTO orderUserActionDTO) {
        log.info("TradeApplicationImpl.queryAddProductDto.orderUserActionDTO={}", JSON.toJSONString(orderUserActionDTO));
        /**Step1.构造查询加项品dto的参数*/
        JdhAddProductRequest request = new JdhAddProductRequest();
        //地址实体
        List<AddressInfoDTO> addressInfoDTOList = orderUserActionDTO.getAddressInfoDTOList();
        if (CollectionUtils.isEmpty(addressInfoDTOList)) {
            return new HashMap<>();
        }
        AddressInfoDTO addressInfoDTO = addressInfoDTOList.get(0);
        //商品实体
        List<SkuItemDTO> skuItemDTOList = orderUserActionDTO.getBundleInfoDTOList().get(0).getSkuItemDTOList();
        //主品：过滤掉加项商品的sku列表
        Set<Long> mainSkuSet = new HashSet<>();
        List<Long> addSkuList = new ArrayList<>();
        Optional.ofNullable(skuItemDTOList).map(List::stream).orElseGet(Stream::empty).
                forEach(skuItemDTO -> {
                    if (IsAddedEnum.IS_ADDED.getValue().equals(skuItemDTO.getIsAdded())){
                        addSkuList.add(Long.valueOf(skuItemDTO.getId()));
                    }else {
                        mainSkuSet.add(Long.valueOf(skuItemDTO.getId()));
                    }
                });
        //构造查询参数
        request.setMainSkuSet(mainSkuSet);
        request.setAddressId(addressInfoDTO.getId());
        request.setFullAddress(addressInfoDTO.getFullAddress());
        request.setUserPin(orderUserActionDTO.getUserPin());
        /**Step2.查询加项品dto*/
        Map<Long, AddProductDto> addProductMap = productApplication.queryAddProductDtoBySkuList(request);
        log.info("TradeApplicationImpl.queryAddProductDto.addProductMap={}", JSON.toJSONString(addProductMap));
        return addProductMap;
    }



    /**
     * 根据skuId 查询服务类型 用于订单备注 知情同意书的动态展示
     *
     * @param orderUserActionDTO
     */

    private void enhanceServiceType(OrderUserActionDTO orderUserActionDTO, OrderUserActionParam orderUserActionParam) {
        try {
            List<String> skuList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderUserActionDTO.getBundleInfoDTOList())){
                // 获取sku信息
                List<String> bundleSkuList = orderUserActionDTO.getBundleInfoDTOList().get(0).getSkuItemDTOList().stream()
                        .map(SkuItemDTO::getSkuId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(bundleSkuList)){
                    skuList.addAll(bundleSkuList);
                }
            }

            // 加项品
            List<AddSkuItemDTO> addSkuItemDTOS = orderUserActionDTO.getAddSkuItemDTOS();
            if (CollectionUtils.isNotEmpty(addSkuItemDTOS)){
                List<Long> addItemSkuList = addSkuItemDTOS.stream().map(AddSkuItemDTO::getSkuId).distinct().collect(Collectors.toList());
                List<String> addItemSkuStrList = addItemSkuList.stream().map(Object::toString) .collect(Collectors.toList());
                skuList.addAll(addItemSkuStrList);
            }
            log.info("TradeApplicationImpl enhanceServiceType skuList={}", JSON.toJSONString(skuList));
            if (CollectionUtils.isEmpty(skuList)){
                return;
            }
            List<JdhSku> jdhSkus = jdhSkuRepository.queryMultiSku(skuList.stream().map(s -> JdhSku.builder().skuId(Long.valueOf(s)).build()).filter(Objects::nonNull).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(jdhSkus)) {
                Integer serviceTypePivot = jdhSkus.get(0).getServiceType();
                //第一个服务类型为基准服务类型
                jdhSkus.stream().forEach(jdhSku -> {
                    if (!serviceTypePivot.equals(jdhSku.getServiceType())) {
                        throw new BusinessException(BusinessErrorCode.TRADE_SKUS_HAS_DIFF_SERVICE_TYPES);
                    }
                });
                orderUserActionDTO.setServiceType(String.valueOf(serviceTypePivot));
            }


            //是骑手上门，并且在白名单内
            if(ServiceTypeNewEnum.KNIGHT_TEST.getType().toString().equals(orderUserActionDTO.getServiceType())
                    && (PartnerSourceEnum.JDH_XFYL.getCode().equals(orderUserActionParam.getPartnerSource())
                        || Objects.isNull(orderUserActionParam.getPartnerSource()))){
                Long mainSkuId = null;
                try {
                    //取出来主品
                    List<SkuItemDTO> skuItemDTOList = orderUserActionDTO.getBundleInfoDTOList().get(0)
                            .getSkuItemDTOList()
                            .stream().filter(ele -> NumConstant.NUM_0.equals(ele.getIsAdded())).collect(Collectors.toList());
                    mainSkuId = Long.valueOf(skuItemDTOList.get(NumConstant.NUM_0).getSkuId());
                } catch (Exception e) {
                    log.error("获取行为返回中的主品sku失败",e);
                }

                List<Long> preSampleFlagSkuList = duccConfig.getPreSampleFlagSkuList();
                if(CollUtil.isNotEmpty(preSampleFlagSkuList) && preSampleFlagSkuList.contains(mainSkuId)){
                    orderUserActionDTO.setShowPreSampleFlag(Boolean.TRUE);
                }
            }

        }catch (Exception e){
            log.error("TradeApplicationImpl.enhanceServiceType has error",e);
        }
    }

    private OrderUserActionParam buildPartnerSourceOrderId(OrderUserActionParam orderUserActionParam) {
        if (orderUserActionParam == null || orderUserActionParam.getPartnerSource() == null || PartnerSourceEnum.fromCode(orderUserActionParam.getPartnerSource()) == null || orderUserActionParam.getPartnerSourceOrderId() == null) {
            return orderUserActionParam;
        }
        if (orderUserActionParam.getSkuInfoParamList() != null && orderUserActionParam.getSkuInfoParamList().size() > 0 && orderUserActionParam.getSkuInfoParamList().stream().filter(s -> BooleanUtils.isNotFalse(s.getIsSelected())).collect(Collectors.toList()).size() == 0) {
            log.error("TradeApplicationImpl buildPartnerSourceOrderId 检测单:{} 下商品全部未选中", orderUserActionParam.getPartnerSourceOrderId());
            throw new BusinessException(BusinessErrorCode.TRADE_LASGEST_SKU_INFO);
        }
        if (PartnerSourceEnum.JDH_NETDIAG.getCode().intValue() == orderUserActionParam.getPartnerSource().intValue() || PartnerSourceEnum.JDH_HOMEDIAG.getCode().intValue() == orderUserActionParam.getPartnerSource().intValue()) {
            if (!StringUtils.isNumeric(orderUserActionParam.getPartnerSourceOrderId())) {
                log.error("TradeApplicationImpl buildPartnerSourceOrderId 无效的partnerSourceOrderId orderUserActionParam:{}", JSONObject.toJSONString(orderUserActionParam));
                return orderUserActionParam;
            }
            InspectionSheetButtonParam inspectionSheetButtonParam = new InspectionSheetButtonParam();
            inspectionSheetButtonParam.setSheetId(Long.valueOf(orderUserActionParam.getPartnerSourceOrderId()));
            inspectionSheetButtonParam.setUserPin(orderUserActionParam.getUserPin());
            if (orderUserActionParam.getAddressUpdateParam() != null && orderUserActionParam.getAddressUpdateParam().getId() > 0) {
                inspectionSheetButtonParam.setAddressId(orderUserActionParam.getAddressUpdateParam().getId());
            }
            List<InspectProjectChildDetailDTO> sheetResult = inspectionSheetApplication.querySettlementProjectChild(inspectionSheetButtonParam);
            if (CollectionUtils.isNotEmpty(sheetResult) && CollectionUtils.isEmpty(orderUserActionParam.getPatientIds())){
                orderUserActionParam.setPatientIds(Collections.singletonList(sheetResult.get(0).getPatientId()));
            }
            // 存在历史SKU记录的情况下，忽略检验单中产生的数据
            if (orderUserActionParam.getSkuInfoParamList() == null || orderUserActionParam.getSkuInfoParamList().size() == 0) {
                // 兑换sku结果
                orderUserActionParam.setSkuInfoParamList(sheetResult.stream().filter(s -> Objects.nonNull(s.getSkuId())).map(s -> {
                    SkuInfoParam skuInfoParam = new SkuInfoParam();
                    skuInfoParam.setSkuId(String.valueOf(s.getSkuId()));
                    skuInfoParam.setBuyNum(1);
                    return skuInfoParam;
                }).collect(Collectors.toList()));
            }
            // 不支持的服务项列表
            orderUserActionParam.setNotSupportServiceItemList(sheetResult.stream().filter(s -> Objects.isNull(s.getSkuId())).map(s -> s.getChildName()).collect(Collectors.toList()));
            log.info("TradeApplicationImpl buildPartnerSourceOrderId 结果 orderUserActionParam:{}, skuInfoParamList:{}, notSupportServiceItemList:{}", JSONObject.toJSONString(orderUserActionParam), JSONObject.toJSONString(orderUserActionParam.getSkuInfoParamList()), JSONObject.toJSONString(orderUserActionParam.getNotSupportServiceItemList()));
            if (orderUserActionParam.getSkuInfoParamList().size() == 0) {
                log.error("TradeApplicationImpl buildPartnerSourceOrderId 检测单:{} 下无商品", orderUserActionParam.getPartnerSourceOrderId());
                throw new BusinessException(BusinessErrorCode.TRADE_LACK_SKU_INFO);
            }
        } else if(PartnerSourceEnum.OUT_HOSPITAL_PAID_GUIDANCE_A.getCode().intValue() == orderUserActionParam.getPartnerSource().intValue()){
            log.info("TradeApplicationImpl buildPartnerSourceOrderId 院内自费导诊解决方案A订单 orderUserActionParam:{}", JSONObject.toJSONString(orderUserActionParam));
            // 上海儿中心saleChannelId=1
            PartnerSourceOrderQuery query = PartnerSourceOrderQuery.builder()
                    .partnerSourceOrderId(orderUserActionParam.getPartnerSourceOrderId())
                    .partnerSource(SubPartnerSourceEnum.OUT_HOSPITAL_PAID_GUIDANCE_SHANGHAI_CHILD_CENTER.getPartnerSourceEnum().getCode().intValue())
                    .saleChannelId(String.valueOf(SubPartnerSourceEnum.OUT_HOSPITAL_PAID_GUIDANCE_SHANGHAI_CHILD_CENTER.getSubCode()))
                    .userPin(orderUserActionParam.getUserPin())
                    .build();
            if (orderUserActionParam.getAddressUpdateParam() != null && orderUserActionParam.getAddressUpdateParam().getId() > 0) {
                query.setAddressId(orderUserActionParam.getAddressUpdateParam().getId());
            }
            PartnerSourceOrderBO partnerSourceOrderBO = inspectionSheetApplication.queryOuterOrder(query);
            if(Objects.isNull(partnerSourceOrderBO) || CollectionUtils.isEmpty(partnerSourceOrderBO.getSkuIds())){
                log.error("TradeApplicationImpl.buildPartnerSourceOrderId 院内自费导诊解决方案A订单 未查询到外部订单信息 query:{}", JSON.toJSONString(query));
                throw new BusinessException(BusinessErrorCode.TRADE_LACK_SKU_INFO);
            }
            log.info("TradeApplicationImpl buildPartnerSourceOrderId 院内自费导诊解决方案A订单 partnerSourceOrderBO:{}", JSONObject.toJSONString(partnerSourceOrderBO));
            // 存在历史SKU记录的情况下，忽略检验单中产生的数据
            if (orderUserActionParam.getSkuInfoParamList() == null || orderUserActionParam.getSkuInfoParamList().size() == 0) {
                // 兑换sku结果
                orderUserActionParam.setSkuInfoParamList(partnerSourceOrderBO.getSkuIds().stream().map(s -> {
                    SkuInfoParam skuInfoParam = new SkuInfoParam();
                    skuInfoParam.setSkuId(String.valueOf(s));
                    skuInfoParam.setBuyNum(1);
                    return skuInfoParam;
                }).collect(Collectors.toList()));
            }
        }
        return orderUserActionParam;
    }

    /**
     * 构建地址
     *
     * @param orderUserActionParam
     */
    private void buildAddressId(OrderUserActionParam orderUserActionParam) {
        // 切换地址行为保存选择的地址ID到缓存
        if (Objects.nonNull(orderUserActionParam) && Objects.nonNull(orderUserActionParam.getAddressUpdateParam()) && UserActionEnum.ADDRESS_SELECT.getUserActionType().equalsIgnoreCase(orderUserActionParam.getUserActionType())) {
            if (Objects.nonNull(orderUserActionParam.getAddressUpdateParam().getId())) {
                try {
                    jimClient.set(RedisKeyEnum.getRedisKey(RedisKeyEnum.USER_LAST_ADDRESS_ID_KEY, orderUserActionParam.getUserPin()), String.valueOf(orderUserActionParam.getAddressUpdateParam().getId()));
                } catch (Exception e) {
                    log.info("TradeApplicationImpl saveUserLastAddressId 更新用户缓存地址ID失败, userPin:{}, addressId:{}", orderUserActionParam.getUserPin(), orderUserActionParam.getAddressUpdateParam().getId(), e);
                }
            }
        }
        // 其他行为如果入参没有地址ID，使用缓存地址ID
        if (Objects.nonNull(orderUserActionParam) && (Objects.isNull(orderUserActionParam.getAddressUpdateParam()) || Objects.isNull(orderUserActionParam.getAddressUpdateParam().getId()))){
            String cacheAddressIdString = jimClient.get(RedisKeyEnum.getRedisKey(RedisKeyEnum.USER_LAST_ADDRESS_ID_KEY, orderUserActionParam.getUserPin()));
            if(Objects.nonNull(cacheAddressIdString)){
                AddressUpdateParam addressUpdateParam = new AddressUpdateParam();
                addressUpdateParam.setId(Long.parseLong(cacheAddressIdString));
                orderUserActionParam.setAddressUpdateParam(addressUpdateParam);
            }
        }
    }

    /**
     * 构建患者档案信息
     * @param orderUserActionDTO
     * @param orderUserActionParam
     */
    public void buildPatientDTOList(OrderUserActionDTO orderUserActionDTO, OrderUserActionParam orderUserActionParam){
        try {
            log.info("TradeApplicationImpl -> buildPatientDTOList orderUserActionParam={}", JSON.toJSONString(orderUserActionParam));
            if (CollectionUtils.isEmpty(orderUserActionParam.getPatientIds())){
                return;
            }
            List<SkuInfoParam> selectedSkuInfoParamList = orderUserActionParam.getSkuInfoParamList().stream().filter(item -> BooleanUtils.isTrue(item.getIsSelected())).collect(Collectors.toList());
            log.info("TradeApplicationImpl -> buildPatientDTOList selectedSkuInfoParamList={}", JSON.toJSONString(selectedSkuInfoParamList));
            if (CollectionUtils.isEmpty(selectedSkuInfoParamList)){
                return;
            }

            PatientListRequest patientListRequest = new PatientListRequest();
            patientListRequest.setUserPin(orderUserActionParam.getUserPin());
            Set<Long> skuIdList = new HashSet<>();
            selectedSkuInfoParamList.forEach(s->{
                skuIdList.add(Long.valueOf(s.getSkuId()));
            });
            patientListRequest.setSkuIdList(skuIdList);
            List<PatientDto> patientDtoList = patientApplication.getPatientList(patientListRequest);
            log.info("TradeApplicationImpl -> buildPatientDTOList patientListRequest={}, patientDtoList={}", JSON.toJSONString(patientListRequest), JSON.toJSONString(patientDtoList));
            if (CollectionUtils.isEmpty(patientDtoList)){
                return;
            }
            patientDtoList = patientDtoList.stream().filter(p -> orderUserActionParam.getPatientIds().contains(p.getPatientId())).collect(Collectors.toList());
            orderUserActionDTO.setPatientDTOList(tradeApplicationConverter.convertPatientDTOList(patientDtoList));
        } catch (Exception e) {
            log.error("TradeApplicationImpl -> buildPatientDTOList error e", e);
        }
    }

    /**
     * 暂时业务上不需要按商家分堆逻辑，后期补充
     *
     * @param orderUserActionDTO 入参
     */
    private void bundleShoppinglist(OrderUserActionDTO orderUserActionDTO, OrderUserActionParam orderUserActionParam) {
        if (orderUserActionDTO == null || orderUserActionDTO.getVenderInfoDTOList() == null) {
            return;
        }
        List<BundleInfoDTO> bundleInfoList = orderUserActionDTO.getBundleInfoDTOList();
        if (orderUserActionParam != null && orderUserActionParam.getSkuInfoParamList() != null && orderUserActionParam.getSkuInfoParamList().size() > 0) {
            // 互医检验单场景下，选中状态切换后，需要保持SKU顺序不变
            if (orderUserActionParam.getPartnerSource() != null && PartnerSourceEnum.fromCode(orderUserActionParam.getPartnerSource()) != null && (PartnerSourceEnum.JDH_NETDIAG.getCode().intValue() == orderUserActionParam.getPartnerSource().intValue() || PartnerSourceEnum.JDH_HOMEDIAG.getCode().intValue() == orderUserActionParam.getPartnerSource().intValue())) {

                // 写在这里也行
                List<SkuItemDTO> unSelectedSkuItemDTOList = tradeApplicationConverter.convertSkuItemDTOList(orderUserActionParam.getSkuInfoParamList().stream().filter(item -> BooleanUtils.isFalse(item.getIsSelected())).collect(Collectors.toList()));
                // 补充未选中商品的价格及优惠

                AddressDetailBO addressDetail = null;
                if (CollectionUtils.isNotEmpty(orderUserActionDTO.getAddressInfoDTOList())){
                    AddressInfoDTO addressInfoDTO = orderUserActionDTO.getAddressInfoDTOList().get(0);
                    addressDetail = new AddressDetailBO();
                    addressDetail.setProvinceId(addressInfoDTO.getProvinceId());
                    addressDetail.setCityId(addressInfoDTO.getCityId());
                    addressDetail.setCountyId(addressInfoDTO.getCountyId());
                    addressDetail.setTownId(addressInfoDTO.getTownId());
                }
                PriceInfoResponseBO priceInfoResponseBO = skuInfoRpc.getSkuPriceAndPromotionByPin(unSelectedSkuItemDTOList.stream().map(SkuItemDTO::getSkuId).collect(Collectors.toSet()), orderUserActionParam.getUserPin(), addressDetail);
                if (priceInfoResponseBO != null && priceInfoResponseBO.getPriceMap() != null) {
                    Map<String, PriceResultBO> skuPrice = priceInfoResponseBO.getPriceMap();
                    unSelectedSkuItemDTOList.forEach(item -> {
                        if (skuPrice.containsKey(item.getSkuId()) && skuPrice.get(item.getSkuId()) != null) {
                            item.setPrice(NumberUtil.decimalFormat("#.##", new BigDecimal(skuPrice.get(item.getSkuId()).getJdPrice())));
                            item.setDiscount("0");
                        }
                    });
                }
                // 补充未选中的商品
                bundleInfoList.forEach(item -> {
                    item.getSkuItemDTOList().addAll(unSelectedSkuItemDTOList);
                    // 保持入参的顺序
                    Collections.sort(item.getSkuItemDTOList(), new Comparator<SkuItemDTO>() {
                        @Override
                        public int compare(SkuItemDTO o1, SkuItemDTO o2) {
                            List<SkuInfoParam> skuinfoParamList = orderUserActionParam.getSkuInfoParamList();
                            SkuInfoParam skuinfoParamO1 = skuinfoParamList.stream().filter(s -> s.getSkuId().equals(o1.getSkuId())).findFirst().get();
                            SkuInfoParam skuinfoParamO2 = skuinfoParamList.stream().filter(s -> s.getSkuId().equals(o2.getSkuId())).findFirst().get();
                            return Integer.compare(skuinfoParamList.indexOf(skuinfoParamO1), skuinfoParamList.indexOf(skuinfoParamO2));
                        }
                    });
                });
            }
        }
        orderUserActionDTO.getVenderInfoDTOList().forEach(item -> {
            item.setBundleInfoDTOList(bundleInfoList);
        });
    }

    /**
     * 补充结算页价格中的起价标记、合计等
     *
     * @param orderUserActionDTO
     * @param orderUserActionParam
     */
    private void buildAmountInfo(OrderUserActionDTO orderUserActionDTO, OrderUserActionParam orderUserActionParam) {
        try {
            //获取配置一口价商品的skuList
            Set<String> skuFreeFeeList = feeConfigurationApplication.queryAllFixedSkuConfig();
//            Set<String> skuFreeFeeList = duccConfig.getSkuFreeFeeList();
            AtomicBoolean oneTimePriceFlag=new AtomicBoolean(true);
            orderUserActionDTO.getBundleInfoDTOList().forEach(i -> i.getSkuItemDTOList().forEach(j -> {
                //遍历分堆后的商品 如果有一个不包含在一口价商品中 设置标记位置为false
                if (!skuFreeFeeList.contains(j.getSkuId())){
                    oneTimePriceFlag.set(false);
                }
            }));
            log.info("TradeApplicationImpl.buildAmountInfo.oneTimePriceFlag={},orderUserActionDTO={}",oneTimePriceFlag,JSON.toJSONString(orderUserActionDTO));
            //如果加购的都是一口价商品或者用户的预约时间段以及地址已确定
            if (oneTimePriceFlag.get() || orderUserActionParam.getAppointmentTimeParam() != null && !orderUserActionDTO.getAddressInfoDTOList().isEmpty()){
                orderUserActionDTO.getAmountInfoDTO().setPriceFlag(null);
            }
            /*//没有配置一口价，才能展示此文案
            if (!CollectionUtils.isEmpty(skuFreeFeeList)) {
                Set<Long> skuIds = new HashSet<>();
                orderUserActionDTO.getBundleInfoDTOList().forEach(i -> i.getSkuItemDTOList().forEach(j -> {
                    skuIds.add(Long.parseLong(j.getSkuId()));
                }));
                // 是否包含非白名单商品
                AtomicBoolean nonWhiteSkuFlag = new AtomicBoolean(false);
                for (Long i : skuIds) {
                    if (!skuFreeFeeList.contains(i)) {
                        nonWhiteSkuFlag.set(true);
                        break;
                    }
                }
                log.info("TradeApplicationImpl.buildAmountInfo.orderUserActionParam={},orderUserActionDTO={},nonWhiteSkuFlag={}",JSON.toJSONString(orderUserActionParam),JSON.toJSONString(orderUserActionDTO),nonWhiteSkuFlag.get());
                // 预约时间或者地址 任一 不存在 则展示起始价标记
                if (oneTimePriceFlag.get() && (orderUserActionParam.getAppointmentTimeParam() == null || orderUserActionDTO.getAddressInfoDTOList().isEmpty())) {
                    orderUserActionDTO.getAmountInfoDTO().setPriceFlag(1);
                }
            }*/
            // 合计 = 服务费 + 商品金额
            ServiceFeeInfoDTO serviceFeeInfoDTO = orderUserActionDTO.getServiceFeeInfoDTO();
            if (serviceFeeInfoDTO != null && serviceFeeInfoDTO.getServiceFeeItemInfoDTOList() != null) {
                BigDecimal totalServiceFee = serviceFeeInfoDTO.getServiceFeeItemInfoDTOList().stream().map(item -> item.getServiceFee()).reduce(BigDecimal.ZERO, BigDecimal::add);
                orderUserActionDTO.getAmountInfoDTO().setTotalOrderAmount(orderUserActionDTO.getAmountInfoDTO().getGoodsAmount().add(totalServiceFee));
            }
            log.info("TradeApplicationImpl.buildAmountInfo.orderUserActionDTO={}",JSON.toJSONString(orderUserActionDTO));

        } catch (Exception e) {
            log.error("TradeApplicationImpl buildAmountInfo 结算页行为补充价格标记异常 orderUserActionParam={}, orderUserActionDTO={}", JSONObject.toJSONString(orderUserActionParam), JSONObject.toJSONString(orderUserActionDTO), e);
        }
    }

    /**
     * 补充结算页中服务费细项
     *
     * @param orderUserActionDTO
     * @param orderUserActionParam
     */
    private void buildServiceFeeDetail(OrderUserActionDTO orderUserActionDTO, OrderUserActionParam orderUserActionParam) {
        try {
            log.info("TradeApplicationImpl buildServiceFeeDetail orderUserActionParam={}, orderUserActionDTO={}", JSON.toJSONString(orderUserActionParam), JSON.toJSONString(orderUserActionDTO));
            Set<Long> skuIds = new HashSet<>();
            orderUserActionDTO.getVenderInfoDTOList().stream().findFirst().get().getBundleInfoDTOList().forEach(i -> i.getSkuItemDTOList().forEach(j -> {
                skuIds.add(Long.parseLong(j.getSkuId()));
            }));
            JdhSkuListRequest request = new JdhSkuListRequest();
            request.setSkuIdList(skuIds);
            request.setQueryServiceItem(true);
            Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(request);
            if (jdhSkuDtoMap != null && jdhSkuDtoMap.size() > 0) {
                orderUserActionDTO.getVenderInfoDTOList().stream().findFirst().get().getBundleInfoDTOList().forEach(i -> i.getSkuItemDTOList().forEach(j -> {
                    JdhSkuDto skuDTO = jdhSkuDtoMap.get(Long.parseLong(j.getSkuId()));
                    if (skuDTO != null) {
                        j.setServiceItemName(skuDTO.getServiceItemNames());
                    }
                }));
            }

            // 预约开始时间为空或者无服务费时，不计算服务费明细信息
            if (orderUserActionParam.getAppointmentTimeParam() == null || orderUserActionParam.getAppointmentTimeParam().getAppointmentStartTime() == null || orderUserActionDTO.getServiceFeeInfoDTO() == null) {
                log.info("TradeApplicationImpl buildServiceFeeDetail appointmentTimeParam empty");
                return;
            }
            StringBuilder builder = new StringBuilder("");
            if (orderUserActionParam.getAppointmentTimeParam() != null && orderUserActionParam.getAppointmentTimeParam().getIsImmediately()) {
                builder.append("立即,");
            }
            OrderAppointmentTimeValueObject orderAppointmentTimeValueObject = new OrderAppointmentTimeValueObject();
            orderAppointmentTimeValueObject.setAppointmentStartTime(orderUserActionParam.getAppointmentTimeParam().getAppointmentStartTime());
            if (orderAppointmentTimeValueObject.appointmentDayIsHoliday()) {
                builder.append("节假日,");
            }

            // 夜间服务费
            if (orderUserActionParam.getNightServiceSelected() != null && orderUserActionParam.getNightServiceSelected()) {
                builder.append("夜间,");
            }

            orderUserActionDTO.getServiceFeeInfoDTO().getServiceFeeItemInfoDTOList().stream().filter(item -> FeeAggregateTypeEnum.TIME_PERIOD_FEE.getCode().equals(item.getServiceFeeType())).forEach(x -> {
                if (builder.length() > 0) {
                    x.setDetailTips(builder.substring(0, builder.length() - 1));
                }
            });

            JSONObject serviceUpgradeConfig = JSON.parseObject(duccConfig.getServiceUpgradeConfig());
            // 动态调整费
            List<DynamicFee> dynamicFeeList = duccConfig.getDynamicFeeConfig();
            StringBuilder dynamicBuilder = new StringBuilder("");
            if (BooleanUtils.isTrue(orderUserActionParam.getServiceUpgradeSelected())) {
                dynamicBuilder.append(serviceUpgradeConfig.getString("upgrageAngelFeeName")+",");
            }
            if (CollectionUtils.isNotEmpty(dynamicFeeList)){
                for (DynamicFee dynamicFee : dynamicFeeList) {
                    if (dynamicFee.getFee().compareTo(BigDecimal.ZERO) <= 0){
                        continue;
                    }
                    dynamicBuilder.append(dynamicFee.getFeeTips()+",");
                }
            }
            orderUserActionDTO.getServiceFeeInfoDTO().getServiceFeeItemInfoDTOList().forEach(item->{
                if (FeeAggregateTypeEnum.DYNAMIC_FEE.getCode().equals(item.getServiceFeeType())){
                    item.setDetailTips(dynamicBuilder.substring(0, dynamicBuilder.length() - 1));
                }
            });
        } catch (Exception e) {
            log.error("TradeApplicationImpl buildAmountInfo 结算页行为补充结算页中服务费细项 orderUserActionParam={}, orderUserActionDTO={}", JSONObject.toJSONString(orderUserActionParam), JSONObject.toJSONString(orderUserActionDTO), e);
        }
    }


/*    public static void main(String[] args) {
        Boolean serviceUpgradeSelected = true;
        List<DynamicFee> dynamicFeeConfig = new ArrayList<>();
        DynamicFee d1 = new DynamicFee();
        d1.setFee(new BigDecimal(1));
        d1.setFeeTips("因天气原因配送费增加");
        DynamicFee d2 = new DynamicFee();
        d2.setFee(new BigDecimal(0));
        d2.setFeeTips("辛苦费");
        dynamicFeeConfig.add(d1);
        dynamicFeeConfig.add(d2);

        StringBuilder dynamicBuilder = new StringBuilder("");
        if (serviceUpgradeSelected) {
            dynamicBuilder.append("服务人员升级费,");
        }
        if (CollectionUtils.isNotEmpty(dynamicFeeConfig)){
            for (DynamicFee dynamicFee : dynamicFeeConfig) {
                if (dynamicFee.getFee().compareTo(BigDecimal.ZERO) <= 0){
                    continue;
                }
                dynamicBuilder.append(dynamicFee.getFeeTips()+",");
            }
        }

        String detailTips = dynamicBuilder.substring(0, dynamicBuilder.length() - 1);
        System.out.println(detailTips);


    }*/

    @Override
    public SubmitOrderDTO submitOrder(SubmitOrderParam submitOrderParam) {
        OrderSubmitContext orderSubmitContext = tradeApplicationConverter.convertOrderSubmitInfo(submitOrderParam);
        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.EXECUTE_ACTION_BUY_NOW_PIN_SKU_LIST_KEY, submitOrderParam.getUserPin());
        OrderUserActionDTO orderUserActionDTO = JsonUtil.parseObject(jimClient.get(redisKey), OrderUserActionDTO.class);
        log.info("TradeApplicationImpl submitOrder 提交订单 orderUserActionDTO:{}", JSONObject.toJSONString(orderUserActionDTO));
        /**判断是否为骑手场景*/
        /**Step2.从缓存里面拿当前pin的skuSet*/
        // 服务人员升级选中
        if (submitOrderParam.getServiceUpgradeSelected() && serviceUpgradeNoExclude(submitOrderParam, orderUserActionDTO)){
            RedisKeyEnum keyEnum = RedisKeyEnum.TRADE_ACTION_SERVICE_UPGRADE_KEY;
            String cacheKey = RedisKeyEnum.getRedisKey(keyEnum, orderSubmitContext.getUserPin());
            jimClient.setEx(cacheKey, "true", keyEnum.getExpireTime(), keyEnum.getExpireTimeUnit());
        }
        /**step1.处理库存逻辑，查询有库存的服务站并进行库存预占，返回库存单号 */
        Long inventoryId = handleInventoryLogic(orderSubmitContext, orderUserActionDTO);
        /**step2.向交易中台进行提单 */
        OrderSubmitValueObject orderSubmitValueObject = tradeDomainService.submitOrder(orderSubmitContext);
        log.info("TradeApplicationImpl submitOrder 提交订单 orderSubmitContext:{} orderSubmitValueObject:{}", JSONObject.toJSONString(orderSubmitContext), JSONObject.toJSONString(orderSubmitValueObject));
        if (orderSubmitValueObject.getErrorCode() != null) {
            ErrorCode errorCode = orderSubmitValueObject.getErrorCode();
            throw BusinessException.asBusinessException(errorCode);
        }

        // 转换出参
        SubmitOrderDTO submitOrderDTO = tradeApplicationConverter.convertSubmitOrderDTO(orderSubmitValueObject);

        /**3.确认预占库存逻辑*/
        log.info("TradeApplicationImpl.submitOrder.submitOrderParam={},inventoryId={},submitOrderDTO={}", JSON.toJSONString(submitOrderParam), inventoryId, JSON.toJSONString(submitOrderDTO));
        confirmPreemptionInventory(submitOrderParam, inventoryId, orderSubmitValueObject);
        /**4.插入日志记录，发布事件等 发生异常需要进行库存释放*/
        try {
            // 预约中心应用usp为null，下面逻辑在预约中心已包含
            if(submitOrderParam != null && submitOrderParam.getUsp() != null) {
                JdOrderSaveParam jdOrderSaveParam = JdOrderConverter.INSTANCE.convertToJdOrderSaveParam(submitOrderParam);
                jdOrderSaveParam.setOrderId(String.valueOf(orderSubmitValueObject.getOrderId()));
                jdOrderSaveParam.setUserPin(submitOrderParam.getUserPin());
                jdOrderSaveParam.setChannelName(submitOrderParam.getChannelName());
                jdOrderSaveParam.setServiceUpgradeSelected(orderSubmitContext.getServiceUpgradeSelected());
                JdOrder jdOrder = jdOrderApplication.saveJdOrder(jdOrderSaveParam);
                log.info("TradeApplicationImpl submitOrder 提交订单-订单落库结束 jdOrderSaveParam:{} ", JSONObject.toJSONString(jdOrderSaveParam));

                OrderPayUrlParam orderPayUrlParam = tradeApplicationConverter.convertOrderPayUrlParam(submitOrderParam);
                orderPayUrlParam.setOrderId(orderSubmitValueObject.getOrderId());
                String orderPayUrl = getOrderPayUrl(orderPayUrlParam);
                log.info("TradeApplicationImpl submitOrder 提交订单-获取支付链接结束 orderPayUrlParam:{}, orderPayUrl:{} ", JSONObject.toJSONString(orderPayUrlParam), orderPayUrl);

                submitOrderDTO.setOrderPayUrl(orderPayUrl);

                // 发布事件
                eventCoordinator.publish(EventFactory.newDefaultEvent(jdOrder, TradeEventTypeEnum.SELF_ORDER_CREATE, new OrderCreateEventBody(jdOrder)));
            }
            return submitOrderDTO;
        } catch (Exception e) {
            log.error("TradeApplicationImpl submitOrder has error ", e);
            releaseInventoryLogic(inventoryId, submitOrderParam);
            throw e;
        }
    }

    /**
     * 判断服务升级与夜间服务费不互斥
     * @param submitOrderParam
     * @param orderUserActionDTO
     * @return true-不互斥 false-互斥
     */
    private boolean serviceUpgradeNoExclude(SubmitOrderParam submitOrderParam, OrderUserActionDTO orderUserActionDTO) {
        try {
            if (Objects.isNull(submitOrderParam) || Objects.isNull(submitOrderParam.getAppointmentTimeParam())
                    || Objects.isNull(submitOrderParam.getPartnerSource())) {
                return true;
            }
            if (Objects.isNull(orderUserActionDTO) || StringUtils.isBlank(orderUserActionDTO.getServiceType())) {
                return true;
            }
            OrderUserActionParam orderUserActionParam = new OrderUserActionParam();
            orderUserActionParam.setAppointmentTimeParam(submitOrderParam.getAppointmentTimeParam());
            orderUserActionParam.setPartnerSource(submitOrderParam.getPartnerSource());
            //查询当前业务身份下是否开启了夜间服务费
            JdhFeeTimeConfig jdhFeeTimeConfig = getJdhFeeTimeConfig(orderUserActionParam, Integer.valueOf(orderUserActionDTO.getServiceType()));
            //查询将要升级的业务身份下是否开启了夜间服务费
            JdhFeeTimeConfig upGradeFeeTimeConfig = getJdhFeeTimeConfig(orderUserActionParam, ServiceTypeNewEnum.ANGEL_TEST.getType());
            //判断用户预约上门时间是否为夜间服务时间段
            boolean isNightService = false;
            if (Objects.nonNull(jdhFeeTimeConfig)) {
                LocalTime beginTime = LocalTime.parse(submitOrderParam.getAppointmentTimeParam().getAppointmentStartTime().split(" ")[1], DateTimeFormatter.ofPattern("HH:mm"));
                LocalTime endTime = beginTime.plusHours(1).withMinute(0).withSecond(0).withNano(0);
                //处理开始时间为23:00的情况，加一小时后endtime变为0:00，需改为23:59
                if (endTime.getHour() < beginTime.getHour() && endTime.getHour() == 0) {
                    endTime = endTime.plusMinutes(-1);
                }
                ArrayList<TimeIntervalIntersection.TimeInterval> timeIntervalList = Lists.newArrayList();
                if (StringUtils.isNotBlank(jdhFeeTimeConfig.getMidnightStart()) && StringUtils.isNotBlank(jdhFeeTimeConfig.getMidnightEnd())) {
                    timeIntervalList.add(new TimeIntervalIntersection.TimeInterval(
                            StringUtil.isNotBlank(jdhFeeTimeConfig.getMidnightStart()) ? LocalTime.parse(jdhFeeTimeConfig.getMidnightStart(), DateTimeFormatter.ofPattern("HH:mm")) : null,
                            StringUtil.isNotBlank(jdhFeeTimeConfig.getMidnightEnd()) ? LocalTime.parse(jdhFeeTimeConfig.getMidnightEnd(), DateTimeFormatter.ofPattern("HH:mm")) : null));
                }
                if (StringUtils.isNotBlank(jdhFeeTimeConfig.getAfterMidnightStart()) && StringUtils.isNotBlank(jdhFeeTimeConfig.getAfterMidnightEnd())) {
                    timeIntervalList.add(new TimeIntervalIntersection.TimeInterval(
                            StringUtil.isNotBlank(jdhFeeTimeConfig.getAfterMidnightStart()) ? LocalTime.parse(jdhFeeTimeConfig.getAfterMidnightStart(), DateTimeFormatter.ofPattern("HH:mm")) : null,
                            StringUtil.isNotBlank(jdhFeeTimeConfig.getAfterMidnightEnd()) ? LocalTime.parse(jdhFeeTimeConfig.getAfterMidnightEnd(), DateTimeFormatter.ofPattern("HH:mm")) : null));
                }

                Optional<List<TimeIntervalIntersection.TimeInterval>> optional = TimeIntervalIntersection.groupCalculateIntersection(
                        new TimeIntervalIntersection.TimeInterval(beginTime,endTime),
                        timeIntervalList
                );
                isNightService = optional.isPresent();
            }
            //如果预约上门时间是当前业务身份配置的夜间时间段，且将要升级的业务身份下配置夜间服务并且未开启夜间服务
            if (isNightService && (Objects.nonNull(upGradeFeeTimeConfig) && !upGradeFeeTimeConfig.isEnable())) {
                log.info("TradeApplicationImpl serviceUpgradeNoExclude 服务升级与夜间服务费互斥:{}", JSONObject.toJSONString(upGradeFeeTimeConfig));
                return false;
            }
        } catch (Exception e) {
            log.info("TradeApplicationImpl serviceUpgradeNoExclude error", e);
        }
        return true;
    }

    private Boolean isKnightLogicSubmitOrder(OrderUserActionDTO orderUserActionDTO) {
        return String.valueOf(ServiceTypeNewEnum.KNIGHT_TEST.getType()).equals(orderUserActionDTO.getServiceType());
    }

    /**
     * @param inventoryId
     * @param submitOrderParam
     */
    private void releaseInventoryLogic(Long inventoryId, SubmitOrderParam submitOrderParam) {
        ReleaseInventoryCmd cmd = new ReleaseInventoryCmd();
        cmd.setInventoryId(inventoryId);
        cmd.setPin(submitOrderParam.getUserPin());
        stationApplication.releaseInventory(cmd);
    }

    /**
     * 确认预占库存
     *
     * @param submitOrderParam
     * @param inventoryId
     * @param orderSubmitValueObject
     * @return
     */
    private Boolean confirmPreemptionInventory(SubmitOrderParam submitOrderParam, Long inventoryId, OrderSubmitValueObject orderSubmitValueObject) {
        ConfirmPreemptionInventoryCmd cmd = new ConfirmPreemptionInventoryCmd();
        cmd.setInventoryId(inventoryId);
        cmd.setPin(submitOrderParam.getUserPin());
        cmd.setBusinessId(String.valueOf(orderSubmitValueObject.getOrderId()));
        return stationApplication.confirmPreemptionInventory(cmd);
    }

    private SkuAngelStationDto queryInventory(AppointmentTimeValueObject appointmentTimeValueObject,OrderUserActionDTO orderUserActionDTO) {
        /**Step1.查询库存，并按照库存最大规则选中服务站：stationId*/
        QuerySkuAngelStationRequest request = new QuerySkuAngelStationRequest();
        request.setAngelType(AngelTypeEnum.DELIVERY.getType());
        Set<Long> skuNos = orderUserActionDTO.getBundleInfoDTOList().get(0).getSkuItemDTOList().stream().filter(skuItemDTO -> IsAddedEnum.IS_NOT_ADDED.getValue().
                equals(skuItemDTO.getIsAdded()) && skuItemDTO.getIsSelected()).map(item -> Long.valueOf(item.getId())).collect(Collectors.toSet());
        request.setSkuNos(skuNos);
        List<AddressDetail> addressDetailList = new ArrayList<>();
        AddressDetail addressDetail = new AddressDetail();
        addressDetail.setFullAddress(orderUserActionDTO.getAddressInfoDTOList().get(0).getFullAddress());
        addressDetail.setAddressId(String.valueOf(orderUserActionDTO.getAddressInfoDTOList().get(0).getId()));
        addressDetailList.add(addressDetail);
        request.setAddressList(addressDetailList);
        String appointmentTimeStr = formatAppointmentTime(appointmentTimeValueObject.getAppointmentStartTime(), appointmentTimeValueObject.getAppointmentEndTime());
        String[] split = appointmentTimeStr.split("&&&");
        request.setScheduleDay(split[0]);
        request.setBookTimeSpan(split[1]);
        if ("1".equals(orderUserActionDTO.getServiceType())) {
            request.setInventoryChannelNo(1);
        } else if ("2".equals(orderUserActionDTO.getServiceType())) {
            request.setInventoryChannelNo(2);
        } else if ("3".equals(orderUserActionDTO.getServiceType())) {
            request.setInventoryChannelNo(3);
        } else if("5".equals(orderUserActionDTO.getServiceType())){
            request.setInventoryChannelNo(3);
        }
        /**查询服务站信息*/
        log.info("TradeApplicationImpl.handleInventoryLogic.querySkuAngelStations.request={}", JSON.toJSONString(request));
        SkuAngelStationResultDto skuAngelStationResultDto = stationApplication.querySkuAngelStations(request);
        log.info("TradeApplicationImpl.handleInventoryLogic.querySkuAngelStations.skuAngelStationResultDto={}", JSON.toJSONString(skuAngelStationResultDto));
        if (Objects.isNull(skuAngelStationResultDto)) {
            throw new BusinessException(UNKNOWN_ERROR, "当前条件未获取到服务站信息");
        }
        /**库存最大规则*/
        SkuAngelStationDto stationWithMaxInventoryNum = Optional.ofNullable(skuAngelStationResultDto.getSkuAngelStationDtos()).map(List::stream).orElseGet(Stream::empty).
                max(Comparator.comparingInt(skuAngelStationDto -> skuAngelStationDto.getSkuInventoryDto().getInventoryNum())).get();
        if (Objects.isNull(stationWithMaxInventoryNum.getSkuInventoryDto()) ||
                Objects.isNull(stationWithMaxInventoryNum.getSkuInventoryDto().getInventoryNum())
                || stationWithMaxInventoryNum.getSkuInventoryDto().getInventoryNum() <= 0) {
            throw new BusinessException(TradeErrorCode.INVENTORY_OUT_OFF);
        }
        return stationWithMaxInventoryNum;
    }

    private Long handleInventoryLogic(OrderSubmitContext orderSubmitContext,OrderUserActionDTO orderUserActionDTO) {
        SkuAngelStationDto stationWithMaxInventoryNum = queryInventory(orderSubmitContext.getAppointmentTimeValueObject(), orderUserActionDTO);

        AppointmentTimeValueObject appointmentTimeValueObject = orderSubmitContext.getAppointmentTimeValueObject();
        String appointmentTimeStr = formatAppointmentTime(appointmentTimeValueObject.getAppointmentStartTime(), appointmentTimeValueObject.getAppointmentEndTime());
        String[] split = appointmentTimeStr.split("&&&");
        /**选中服务站*/
        Long stationId = stationWithMaxInventoryNum.getAngelStationId();
        /**Step2.预占服务站库存*/
        PreemptionInventoryCmd cmd = new PreemptionInventoryCmd();
        cmd.setPin(orderSubmitContext.getUserPin());
        cmd.setPreemptionNum(1);
        cmd.setBusinessType(AngelStationInventoryBusinessTypeEnum.ORDER.getType());
        cmd.setScheduleDay(split[0]);
        cmd.setScheduleTime(split[1]);
        cmd.setAngelStationId(stationId);
        if("1".equals(orderUserActionDTO.getServiceType())){
            cmd.setInventoryChannelNo(1);
        } else if("2".equals(orderUserActionDTO.getServiceType())){
            cmd.setInventoryChannelNo(2);
        } else if("3".equals(orderUserActionDTO.getServiceType())){
            cmd.setInventoryChannelNo(3);
        } else if("5".equals(orderUserActionDTO.getServiceType())){
            cmd.setInventoryChannelNo(3);
        }
        return stationApplication.preemptionInventory(cmd);
    }

    /**
     * 订单支付收银台
     *
     * @param orderPayUrlParam
     * @return
     */
    @Override
    public String getOrderPayUrl(OrderPayUrlParam orderPayUrlParam) {
        AssertUtils.nonNull(orderPayUrlParam.getOrderId(), TradeErrorCode.ORDER_ID_NULL);
        SkipPayUrlContext context = tradeApplicationConverter.convertSkipPayUrlContext(orderPayUrlParam);

        log.info("TradeApplicationImpl getOrderPayUrl orderPayUrlParam:{} ", JsonUtil.toJSONString(context));

        JdOrder jdOrdertemp = JdOrder.builder().orderId(orderPayUrlParam.getOrderId()).userPin(orderPayUrlParam.getUserPin()).build();
        //查询订单信息
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(jdOrdertemp);
        AssertUtils.nonNull(jdOrder, TradeErrorCode.ORDER_IS_NULL);
        List<JdOrderItem> jdOrderItems = jdOrderItemRepository.listByOrderId(orderPayUrlParam.getOrderId());
        context.initOrder(jdOrder, jdOrderItems);
        return settleUrlService.skipPayUrl(context);
    }

    /**
     * 取消待支付订单
     *
     * @param cancelOrderParam
     * @return
     */
    @Override
    public Boolean cancelPayOrder(CancelOrderParam cancelOrderParam) {
        AssertUtils.nonNull(cancelOrderParam.getOrderId(), TradeErrorCode.ORDER_ID_NULL);
        JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(cancelOrderParam.getOrderId()));
        AssertUtils.nonNull(jdOrder, TradeErrorCode.ORDER_IS_NULL);

//        JdOrderContext jdOrderContext = TradeOrderConverter.INSTANCE.convertToJdOrderContext(jdOrder);
//        jdOrderContext.setJdOrder(jdOrder);
//        jdOrderContext.init(TradeEventTypeEnum.SELF_CANCEL_ORDER);
//        orderStatemachine.fireEvent(ResolveOrderConditionEnum.ORDER_WAIT_PAY, TradeEventTypeEnum.SELF_CANCEL_ORDER, jdOrderContext);
        // 更新订单状态
        jdOrder.setOrderStatus(OrderStatusEnum.ORDER_CANCEL.getStatus());
        jdOrderRepository.cancalOrderByOrderId(jdOrder);

        eventCoordinator.publish(EventFactory.newDefaultEvent(jdOrder, TradeEventTypeEnum.SELF_CANCEL_ORDER, new OrderSplitEventBody(jdOrder)));
        return Boolean.TRUE;
    }

    @Override
    public JdOrderDTO getOrderDetail(OrderDetailParam orderDetailParam) {
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(Long.valueOf(orderDetailParam.getOrderId())).userPin(orderDetailParam.getPin()).build());
        if (Objects.isNull(jdOrder)) {
            return null;
        }
        String orderId=orderDetailParam.getOrderId();
        Boolean isAddSkuItemLogic=jdOrder.getParentId() != 0L && Objects.nonNull(orderDetailParam.getQuerySource())&&orderDetailParam.getQuerySource().equals("C");
        //处理加项逻辑 传入的肯定是子单 先判断是否有父单
        if (isAddSkuItemLogic) {
            JdOrder jdOrderParent = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(jdOrder.getParentId()).userPin(orderDetailParam.getPin()).build());
            jdOrder.setOrderId(jdOrderParent.getOrderId());
            orderId=String.valueOf(jdOrderParent.getOrderId());
            log.info("TradeApplicationImpl.getOrderDetail.jdOrderParent={}",JSON.toJSONString(jdOrderParent));
            jdOrder.setOrderAmount(jdOrderParent.getOrderAmount());
            jdOrder.setOrderCoupon(jdOrderParent.getOrderCoupon());
            jdOrder.setOrderDiscount(jdOrderParent.getOrderDiscount());
        }
        List<JdOrderItem> jdOrderItems = jdOrderItemRepository.listByOrderId(Long.valueOf(orderId));
        log.info("TradeApplicationImpl.getOrderDetail.jdOrderItems={}",JSON.toJSONString(jdOrderItems));
        JdOrderExt jdOrderExtDetail = jdOrderExtRepository.findJdOrderExtDetail(Long.valueOf(orderDetailParam.getOrderId()), OrderExtTypeEnum.APPOINTMENT_INFO.getType());
        JdOrderExt jdOrderExtDetailFee = jdOrderExtRepository.findJdOrderExtDetail(Long.valueOf(orderId), OrderExtTypeEnum.SERVICE_FEE_INFO.getType());
        //非快检模式-查询用户收货地址
        JdOrderExt orderAddressExtDetail = jdOrderExtRepository.findJdOrderExtDetail(Long.valueOf(orderId), OrderExtTypeEnum.ORDER_ADDRESS.getType());
        List<JdOrderRefundTask> jdOrderRefundTaskList =new ArrayList<>();
        //处理加项逻辑
        if (isAddSkuItemLogic) {
            List<JdOrder> orders = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(Long.valueOf(orderId)).build());
            log.info("TradeApplicationImpl.getOrderDetail.orders={}", JSON.toJSONString(orders));
            for (JdOrder order : orders) {
                JdOrderRefundTask jdOrderRefundTask = new JdOrderRefundTask();
                jdOrderRefundTask.setOrderId(order.getOrderId());
                List<JdOrderRefundTask> jdOrderRefundTaskChildList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
                log.info("TradeApplicationImpl.getOrderDetail.order={},jdOrderRefundTaskChildList={}", JSON.toJSONString(order), JSON.toJSONString(jdOrderRefundTaskChildList));
                jdOrderRefundTaskList.addAll(jdOrderRefundTaskChildList);
            }
        } else {
            JdOrderRefundTask jdOrderRefundTask = new JdOrderRefundTask();
            jdOrderRefundTask.setOrderId(Long.valueOf(orderId));
            jdOrderRefundTaskList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
        }
        log.info("TradeApplicationImpl.getOrderDetail.jdOrderRefundTaskList={}",JSON.toJSONString(jdOrderRefundTaskList));
        jdOrder.setJdOrderItemList(jdOrderItems);
        jdOrder.setJdOrderExtList(Arrays.asList(jdOrderExtDetail,orderAddressExtDetail));
        JdOrderDTO jdOrderDTO = JdOrderConverter.INSTANCE.JdOrderToDTO(jdOrder);
        jdOrderDTO.setAddressInfo(JdOrderConverter.INSTANCE.getAddressInfoDTO(jdOrderExtDetail));
        jdOrderDTO.setAppointmentTimeDto(JdOrderConverter.INSTANCE.getAppointmentTimeDto(jdOrderExtDetail));
        jdOrderDTO.setJdOrderServiceFeeInfos(JdOrderConverter.INSTANCE.getJdOrderFeeDTO(jdOrderExtDetailFee));
        jdOrderDTO.setRefundAmount(JdOrderConverter.INSTANCE.getRefundAmount(jdOrderRefundTaskList));
        if (isAddSkuItemLogic) {
            jdOrderDTO.setOrderId(Long.valueOf(orderDetailParam.getOrderId()));
        }
        return jdOrderDTO;
    }

    /**
     *
     * @param orderDetailParam
     * @return
     */
    private JdOrderDTO getOrderItemAndFeeInfo(OrderDetailParam orderDetailParam) {
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(Long.valueOf(orderDetailParam.getOrderId())).userPin(orderDetailParam.getPin()).build());
        if (Objects.isNull(jdOrder)) {
            return null;
        }
        String orderId=orderDetailParam.getOrderId();
        Boolean isAddSkuItemLogic=jdOrder.getParentId() != 0L && Objects.nonNull(orderDetailParam.getQuerySource())&&orderDetailParam.getQuerySource().equals("C");
        //处理加项逻辑 传入的肯定是子单 先判断是否有父单
        if (isAddSkuItemLogic) {
            JdOrder jdOrderParent = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(jdOrder.getParentId()).userPin(orderDetailParam.getPin()).build());
            jdOrder.setOrderId(jdOrderParent.getOrderId());
            orderId=String.valueOf(jdOrderParent.getOrderId());
            log.info("TradeApplicationImpl.getOrderDetail.jdOrderParent={}",JSON.toJSONString(jdOrderParent));
            jdOrder.setOrderAmount(jdOrderParent.getOrderAmount());
        }
        List<JdOrderItem> jdOrderItems = jdOrderItemRepository.itemListByOrderId(Long.valueOf(orderId));
        log.info("TradeApplicationImpl.getOrderDetail.jdOrderItems={}", JSON.toJSONString(jdOrderItems));
        JdOrderExt jdOrderExtDetail = jdOrderExtRepository.findJdOrderExtDetail(Long.valueOf(orderDetailParam.getOrderId()), OrderExtTypeEnum.APPOINTMENT_INFO.getType());
        JdOrderExt jdOrderExtDetailFee = jdOrderExtRepository.findJdOrderExtDetail(Long.valueOf(orderId), OrderExtTypeEnum.SERVICE_FEE_INFO.getType());
        List<JdOrderRefundTask> jdOrderRefundTaskList =new ArrayList<>();
        //处理加项逻辑
        if (isAddSkuItemLogic) {
            List<JdOrder> orders = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(Long.valueOf(orderId)).build());
            log.info("TradeApplicationImpl.getOrderDetail.orders={}", JSON.toJSONString(orders));
            for (JdOrder order : orders) {
                JdOrderRefundTask jdOrderRefundTask = new JdOrderRefundTask();
                jdOrderRefundTask.setOrderId(order.getOrderId());
                List<JdOrderRefundTask> jdOrderRefundTaskChildList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
                log.info("TradeApplicationImpl.getOrderDetail.order={},jdOrderRefundTaskChildList={}", JSON.toJSONString(order), JSON.toJSONString(jdOrderRefundTaskChildList));
                jdOrderRefundTaskList.addAll(jdOrderRefundTaskChildList);
            }
        } else {
            JdOrderRefundTask jdOrderRefundTask = new JdOrderRefundTask();
            jdOrderRefundTask.setOrderId(Long.valueOf(orderId));
            jdOrderRefundTaskList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
        }
        log.info("TradeApplicationImpl.getOrderDetail.jdOrderRefundTaskList={}",JSON.toJSONString(jdOrderRefundTaskList));
        jdOrder.setJdOrderItemList(jdOrderItems);
        jdOrder.setJdOrderExtList(Arrays.asList(jdOrderExtDetail));
        JdOrderDTO jdOrderDTO = JdOrderConverter.INSTANCE.JdOrderToDTO(jdOrder);
        jdOrderDTO.setAddressInfo(JdOrderConverter.INSTANCE.getAddressInfoDTO(jdOrderExtDetail));
        jdOrderDTO.setAppointmentTimeDto(JdOrderConverter.INSTANCE.getAppointmentTimeDto(jdOrderExtDetail));
        jdOrderDTO.setJdOrderServiceFeeInfos(JdOrderConverter.INSTANCE.getJdOrderFeeDTO(jdOrderExtDetailFee));
        jdOrderDTO.setRefundAmount(JdOrderConverter.INSTANCE.getRefundAmount(jdOrderRefundTaskList));
        if (isAddSkuItemLogic) {
            jdOrderDTO.setOrderId(Long.valueOf(orderDetailParam.getOrderId()));
        }
        return jdOrderDTO;
    }

    /**
     * @param orderDetailParam
     * @return
     */
    @Override
    public JdOrderDTO getOrderSettleDetail(OrderDetailParam orderDetailParam) {
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(Long.valueOf(orderDetailParam.getOrderId())).userPin(orderDetailParam.getPin()).build());
        if (Objects.isNull(jdOrder)) {
            return null;
        }
        List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(jdOrder.getOrderId());
        if (CollUtil.isNotEmpty(jdOrderMoneyList)) {
            jdOrder.setJdOrderMoneyList(jdOrderMoneyList);
        } else {
            jdOrder.setJdOrderMoneyList(createOrderMoneyInfo(jdOrder.getOrderId()));
        }
        JdOrderItem jdOrderItem = jdOrderItemRepository.findJdOrderItemDetail(jdOrder.getOrderId(), Long.parseLong(orderDetailParam.getServiceId()));
        jdOrder.setJdOrderItemList(Arrays.asList(jdOrderItem));
        JdOrderExt jdOrderExtDetailFee = jdOrderExtRepository.findJdOrderExtDetail(Long.valueOf(orderDetailParam.getOrderId()), OrderExtTypeEnum.SERVICE_FEE_SNAPSHOT.getType());
        JdOrderDTO jdOrderDTO = JdOrderConverter.INSTANCE.JdOrderToDTO(jdOrder);
        if (Objects.nonNull(jdOrderExtDetailFee)) {
            jdOrderDTO.setServiceFeeSnapshot(jdOrderExtDetailFee.getExtContext());
        }
        return jdOrderDTO;
    }

    /**
     *
     * @param orderId
     * @return
     */
    @Override
    public String findJdOrderExtContext(Long orderId) {
        JdOrderExt jdOrderExtDetailFee = jdOrderExtRepository.findJdOrderExtDetail(orderId, OrderExtTypeEnum.SERVICE_FEE_SNAPSHOT.getType());
        return jdOrderExtDetailFee == null ? "" : jdOrderExtDetailFee.getExtContext();
    }

    /**
     * @param orderDetailParam
     * @return
     */
    public JdOrderDTO getSplitOrderSettleDetail(OrderDetailParam orderDetailParam) {
        List<JdOrder> jdOrderList = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(orderDetailParam.getParentId()).build());
        if (CollUtil.isEmpty(jdOrderList)) {
            JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderDetailParam.getParentId()).build());
            jdOrderList = Arrays.asList(jdOrder);
        }
        String settleSnapshot = "";
        if(Objects.nonNull(orderDetailParam.getPromiseId())){
            settleSnapshot = jdServiceSettleApplication.findAngelWorkSettleSnapshot(orderDetailParam.getPromiseId());
        }
        for (JdOrder jdOrder : jdOrderList) {
            JdOrderItem jdOrderItem = jdOrderItemRepository.findJdOrderItemDetail(jdOrder.getOrderId(), Long.parseLong(orderDetailParam.getServiceId()));
            if (Objects.nonNull(jdOrderItem)) {
                jdOrder.setJdOrderItemList(Arrays.asList(jdOrderItem));
                List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(jdOrder.getOrderId());
                if (CollUtil.isEmpty(jdOrderMoneyList)) {
                    jdOrderMoneyList = createOrderMoneyInfo(jdOrder.getOrderId());
                }
                if (CollUtil.isNotEmpty(jdOrderMoneyList)) {
                    jdOrder.setJdOrderMoneyList(jdOrderMoneyList);
                }
                JdOrderDTO jdOrderDTO = JdOrderConverter.INSTANCE.JdOrderToDTO(jdOrder);
                if (StringUtil.isNotBlank(settleSnapshot)) {
                    jdOrderDTO.setServiceFeeSnapshot(settleSnapshot);
                }
                return jdOrderDTO;
            }
        }
        return null;
    }

    /**
     * @param orderId
     * @return
     */
    @Override
    public List<JdOrderMoneyDTO> getJdOrderMoneyList(Long orderId) {
        List<JdOrder> jdOrderList = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(orderId).build());
        if (CollUtil.isEmpty(jdOrderList)) {
            JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderId).build());
            jdOrderList = Arrays.asList(jdOrder);
        }
        List<JdOrderMoney> jdOrderMoneyList = new ArrayList<>();
        for (JdOrder jdOrder : jdOrderList) {
            List<JdOrderMoney> childOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(jdOrder.getOrderId());
            if (CollUtil.isEmpty(childOrderMoneyList)) {
                jdOrderMoneyList.addAll(createOrderMoneyInfo(jdOrder.getOrderId()));
            } else {
                jdOrderMoneyList.addAll(childOrderMoneyList);
            }
        }

        return JdOrderConverter.INSTANCE.JdOrderMoneyToDTOList(jdOrderMoneyList);
    }

    /**
     * 可约时段
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public AvaiableAppointmentTimeDTO queryAvailableAppointmentTime(AvaiableAppointmentTimeParam param) {
        return this.queryAvaiableAppointmentTime(param);
    }

    /**
     *
     * @param skuIds
     * @param areaFeeConfigId
     * @return
     */
    @Override
    public String getUpgrageAngelFee(Set<String> skuIds, Long areaFeeConfigId) {
        JdhAreaFeeConfig jdhAreaFeeConfig = jdhAreaFeeConfigRepository.find(new JdhAreaFeeConfigdentifier(areaFeeConfigId));
        String upgrageAngelFee = "";
        if(Objects.nonNull(jdhAreaFeeConfig) && StringUtil.isNotBlank(jdhAreaFeeConfig.getUpgrageAngelFee())){
            String upgrageSkuList = jdhAreaFeeConfig.getUpgrageSkuList();
            if(StringUtils.isBlank(upgrageSkuList)){
                upgrageAngelFee = jdhAreaFeeConfig.getUpgrageAngelFee();
            } else {
                for (String skuId : skuIds) {
                    if (!upgrageSkuList.contains(skuId)) {
                        return "";
                    }
                    upgrageAngelFee = jdhAreaFeeConfig.getUpgrageAngelFee();
                }
            }
        }
        return upgrageAngelFee;
    }

    /**
     *
     * @param jdhSkuAreaFeeQuery
     * @return
     */
    @Override
    public String getUpgrageAngelFeeByParam(JdhSkuAreaFeeQuery jdhSkuAreaFeeQuery) {
        JdhAreaFeeConfigQuery jdhAreaFeeConfigQuery = new JdhAreaFeeConfigQuery();
        jdhAreaFeeConfigQuery.setProvinceCode(jdhSkuAreaFeeQuery.getProvinceCode());
        jdhAreaFeeConfigQuery.setChannelId(jdhSkuAreaFeeQuery.getChannelId());
        jdhAreaFeeConfigQuery.setServiceType(String.valueOf(jdhSkuAreaFeeQuery.getServiceType()));
        List<JdhAreaFeeConfig> jdhAreaFeeConfigList = jdhAreaFeeConfigRepository.queryJdhAreaFeeConfigList(jdhAreaFeeConfigQuery);
        if(CollUtil.isNotEmpty(jdhAreaFeeConfigList)){
            Set<String> skuIds = jdhSkuAreaFeeQuery.getSkuIds();
            JdhAreaFeeConfig jdhAreaFeeConfig = jdhAreaFeeConfigList.get(0);
            for (String skuId: skuIds) {
                String upgrageSkuList = jdhAreaFeeConfig.getUpgrageSkuList();
                if (upgrageSkuList.contains(skuId)) {
                    return jdhAreaFeeConfig.getUpgrageAngelFee();
                }
            }
        }
        return "";
    }

    /**
     * 查询订单详情
     *
     * @param orderDetailParam
     */
    @Override
    public JdOrderDTO queryJdOrderDTO(OrderDetailParam orderDetailParam) {
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(Long.valueOf(orderDetailParam.getOrderId())).userPin(orderDetailParam.getPin()).build());
        if (Objects.isNull(jdOrder)) {
            return null;
        }
        JdOrderDTO jdOrderDTO = JdOrderConverter.INSTANCE.JdOrderToDTO(jdOrder);
        return jdOrderDTO;
    }

    @Override
    public AvaiableAppointmentTimeDTO queryAvaiableAppointmentTime(AvaiableAppointmentTimeParam param) {
        buildParamByScene(param);
        Map<Long, JdhSkuDto> skuInfoMap = productApplication.queryJdhSkuInfoList(tradeApplicationConverter.convertJdhSkuListRequest(param));
        if (skuInfoMap == null || skuInfoMap.size() == 0) {
            log.error("TradeApplicationImpl queryAvaiableAppointmentTime 商品不存在 avaiableAppointmentTimeParam:{} ", JSONObject.toJSONString(param));
            return null;
        }
        /**判断业务类型，返回是否为骑手*/
        Boolean isKnight=isKnightLogicBySku(skuInfoMap);
        Boolean isCare=isCareLogicBySku(skuInfoMap);
        AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO = calcAppointmentTime(new ArrayList<>(skuInfoMap.values()), isCare);
        log.info("TradeApplicationImpl.queryAvaiableAppointmentTime.avaiableAppointmentTimeDTO={}", JSON.toJSONString(avaiableAppointmentTimeDTO));
        /**库存逻辑：step1.获取库存可预约时间列表*/
        List<Date> avaiableInventoryList = new ArrayList<>();
        Boolean isSelfTestTransport = this.isSelfTestTransport(skuInfoMap);
        if(isSelfTestTransport){
            //非快检模式
            avaiableInventoryList = handleJdLogisticsDateLogic(param);
            if(CollectionUtils.isEmpty(avaiableInventoryList)){
                log.error("TradeApplicationImpl queryAvaiableAppointmentTime 非快检模式 查询物流接口 返回可约时段为空!!!");
                return null;
            }
            //排序处理
            avaiableInventoryList = avaiableInventoryList.stream().sorted().collect(Collectors.toList());
            avaiableAppointmentTimeDTO = new AvaiableAppointmentTimeDTO(
                    new Date(),
                    avaiableInventoryList.stream().max(Date::compareTo).orElse(null),
                    "00:00", "24:00", 0,null,null);
        }else{
            log.info("TradeApplicationImpl.queryAvaiableAppointmentTime.avaiableInventoryList={} isSelfTestTransport={}", JSON.toJSONString(avaiableInventoryList),isSelfTestTransport);
            avaiableInventoryList = handleInventoryDateLogic(param, getServiceType(skuInfoMap, param.getUserPin()));
        }
        log.info("TradeApplicationImpl.queryAvaiableAppointmentTime.avaiableInventoryList={} avaiableInventoryList={}", JSON.toJSONString(avaiableInventoryList),JSON.toJSONString(avaiableAppointmentTimeDTO));
        /**step1.5.获取夜间服务费项数据*/
        AppointTimeServiceFeeDetail serviceFeeDetail = getAppointTimeServiceFeeDetail(param, skuInfoMap);
        log.info("serviceFeeDetail={}",JSON.toJSONString(serviceFeeDetail));
        /**step2.获取前端展示时间并与库存可预约时间取交集-进行打标*/
        Map<String, Map<String, List<AppointmentTimeRangeDTO>>> displayDate = getDisplayDate(avaiableAppointmentTimeDTO, avaiableInventoryList,isKnight, serviceFeeDetail, isCare);
        log.info("displayDate={}",JSON.toJSONString(displayDate));
        /**step3.过滤displayDate的map中时间key没有库存的日期*/
        filterDisplayDateByInventory(displayDate, avaiableInventoryList);
        /**step4.兼容老版本排期列表*/
        List<XfylAppointDateDTO> compatibleGroupDTO = getCompatibleGroupDTO(displayDate, isCare);
        log.info("compatibleGroupDTO={} displayDate={}",JSON.toJSONString(compatibleGroupDTO),JSON.toJSONString(displayDate));
        /**step5.赋值返回结果*/
        avaiableAppointmentTimeDTO.setDisplayDate(displayDate);
        avaiableAppointmentTimeDTO.setCompatibleGroupDTO(compatibleGroupDTO);
//        log.info("TradeApplicationImpl queryAvaiableAppointmentTime.after.avaiableAppointmentTimeDTO={}", JsonUtil.toJSONString(avaiableAppointmentTimeDTO));
        if(param.getShowTimeType()!=null&&param.getShowTimeType().equals(CommonConstant.ONE)){
            //非快检新增逻辑,根据前端出来的标识,过滤不可约时间
            avaiableAppointmentTimeDTO.filterShowTime(param.getShowTimeType());
        }
        buildRetByScene(param, avaiableAppointmentTimeDTO);
        return avaiableAppointmentTimeDTO;
    }

    /**
     * 通过场景构建必传参数
     * @param param
     */
    private void buildParamByScene(AvaiableAppointmentTimeParam param){
        if (StringUtils.isBlank(param.getScene())){
            return;
        }
        if (AvailableAppointmentTimeSceneEnum.ANGEL_MODIFY_DATE.getName().equalsIgnoreCase(param.getScene())) {
            String angelPin = UserPinContext.get();
            AssertUtils.hasText(param.getAngelWorkId(), "工单ID不允许为空");
            AngelWork work = angelWorkRepository.authorityFind(new AngelWorkIdentifier(Long.parseLong(param.getAngelWorkId())), angelPin);
            if (Objects.isNull(work)){
                throw new SystemException(SystemErrorCode.DATA_AUTHORITY);
            }
            JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder()
                    .promiseId(work.getPromiseId())
                    .build());

            List<JdOrderItem> jdOrderItems = jdOrderItemRepository.listByOrderId(Long.valueOf(promise.getSourceVoucherId()));
            if (CollUtil.isNotEmpty(jdOrderItems)) {
                List<Long> lt = jdOrderItems.stream().filter(s -> (s != null && s.getSkuId() != null && s.getIsAdded() != null && CommonConstant.ZERO == s.getIsAdded())).map(JdOrderItem::getSkuId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(lt)) {
                    param.setSkuIds(lt);
                }
            } else {
                if (CollUtil.isNotEmpty(promise.getServices())) {
                    List<Long> lt = promise.getServices().stream().map(PromiseService::getServiceId).filter(Objects::nonNull).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(lt)) {
                        param.setSkuIds(lt);
                    }
                }
            }
            param.setAddressId(promise.getStore().getStoreId());
            param.setFullAddress(promise.getStore().getStoreAddr());
            param.setUserPin(promise.getUserPin());
            param.setAngelPin(angelPin);
        }
    }
    /**
     * 通过场景构建必传参数
     * @param param
     */
    private void buildRetByScene(AvaiableAppointmentTimeParam param, AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO){
        if (StringUtils.isBlank(param.getScene())){
            return;
        }
        if (AvailableAppointmentTimeSceneEnum.ANGEL_MODIFY_DATE.getName().equalsIgnoreCase(param.getScene())) {
            if (CollectionUtils.isNotEmpty(avaiableAppointmentTimeDTO.getCompatibleGroupDTO())) {
                // 查询护士排期
                AngelWorkDBQuery req = new AngelWorkDBQuery();
                req.setAngelPin(param.getAngelPin());
                req.setServiceStartTimeBegin(new Date());
                List<AngelWork> result = angelWorkRepository.findList(req);
                if (CollectionUtils.isEmpty(result)) {
                    return;
                }

                Map<String, List<AngelWork>> angelWorkAppointMap = new HashMap<>();
                DateFormat dayFormat = getAppointDateFormat();

                for (AngelWork angelWork : result) {
                    String startTime = dayFormat.format(angelWork.getWorkStartTime());
                    if (angelWorkAppointMap.containsKey(startTime)) {
                        angelWorkAppointMap.get(startTime).add(angelWork);
                    }else {
                        List<AngelWork> list = new ArrayList<>();
                        list.add(angelWork);
                        angelWorkAppointMap.put(startTime,list);
                    }
                }
                log.info("buildRetByScene angelWorkAppointMap={}", JSON.toJSONString(angelWorkAppointMap));
                for (XfylAppointDateDTO xfylAppointDateDTO : avaiableAppointmentTimeDTO.getCompatibleGroupDTO()) {
                    if (!angelWorkAppointMap.containsKey(xfylAppointDateDTO.getAppointDateDesc())) {
                        log.info("buildRetByScene !angelWorkAppointMap.containsKey {}", xfylAppointDateDTO.getAppointDateDesc());
                        continue;
                    }
                    for (XfylAppointDateTimeGroupDTO groupDTO : xfylAppointDateDTO.getAppointDateTimeGroupDTOList()) {
                        if (CollUtil.isEmpty(groupDTO.getDateTimeDTOList())) {
                            continue;
                        }
                        for (XfylAppointDateTimeDTO xfylAppointDateTimeDTO : groupDTO.getDateTimeDTOList()) {
                            Date startTime = TimeUtils.timeStrToDate(xfylAppointDateTimeDTO.getAppointmentStartTime(), TimeFormat.LONG_PATTERN_LINE_NO_S);
                            Date endTime = TimeUtils.timeStrToDate(xfylAppointDateTimeDTO.getAppointmentEndTime(), TimeFormat.LONG_PATTERN_LINE_NO_S);
                            log.info("buildRetByScene startTime {} endTime {}", startTime, endTime);
                            for (AngelWork angelWork : angelWorkAppointMap.get(xfylAppointDateDTO.getAppointDateDesc())) {
                                log.info("buildRetByScene angelWorkStartTime {} angelWorkEndTime {}", angelWork.getWorkStartTime(), angelWork.getWorkEndTime());
                                if ((angelWork.getWorkStartTime().compareTo(startTime) >= 0 && angelWork.getWorkStartTime().compareTo(endTime) < 0) || angelWork.getWorkEndTime().compareTo(startTime) > 0 && angelWork.getWorkEndTime().compareTo(endTime) <= 0) {
                                    xfylAppointDateTimeDTO.setTimeDescAppend("(已被预约)");
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public static void main(String[] args) {
        /*List<Date> avaiableInventoryList = JSON.parseArray("[1744246800000,1744250400000,1744250400000,1744254000000,1744254000000,1744257600000,1744257600000,1744261200000,1744261200000,1744264800000,1744264800000,1744268400000,1744268400000,1744272000000,1744272000000,1744275600000,1744275600000,1744279200000,1744279200000,1744282800000,1744282800000,1744286400000,1744286400000,1744290000000,1744082071000,1744085671000,1744084800000,1744088400000,1744088400000,1744092000000,1744092000000,1744095600000,1744095600000,1744099200000,1744099200000,1744102800000,1744102800000,1744106400000,1744106400000,1744110000000,1744110000000,1744113600000,1744113600000,1744117200000,1744160400000,1744164000000,1744164000000,1744167600000,1744167600000,1744171200000,1744171200000,1744174800000,1744174800000,1744178400000,1744178400000,1744182000000,1744182000000,1744185600000,1744185600000,1744189200000,1744189200000,1744192800000,1744192800000,1744196400000,1744196400000,1744200000000,1744200000000,1744203600000]",Date.class);
        avaiableInventoryList = avaiableInventoryList.stream().sorted().collect(Collectors.toList());
        AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO = new AvaiableAppointmentTimeDTO(
                avaiableInventoryList.stream().min(Date::compareTo).orElse(null),
                avaiableInventoryList.stream().max(Date::compareTo).orElse(null),
                "00:00", "24:00", 0,null,null);
        System.out.println(avaiableAppointmentTimeDTO);
        TradeApplicationImpl tradeApplication = new TradeApplicationImpl();
        Map<String, Map<String, List<AppointmentTimeRangeDTO>>> displayDate =tradeApplication.getDisplayDate(avaiableAppointmentTimeDTO, avaiableInventoryList,false, null, false);
        System.out.println(JSON.toJSONString(displayDate));*/


        Date d = TimeUtils.strToDate("2025-04-10");
        System.out.println(d);
    }

    /**
     * 查询京东物流可约时间
     * @param param
     * @return
     */
    private List<Date> handleJdLogisticsDateLogic(AvaiableAppointmentTimeParam param) {
        List<Date> dates = new ArrayList<>();

        QueryAvailableTimeContext queryAvailableTimeContext = new QueryAvailableTimeContext();
        queryAvailableTimeContext.setSenderFullAddress(param.getFullAddress());

        if(param.getPromiseId()!=null){
            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(param.getPromiseId()).build());
            if(CollectionUtils.isNotEmpty(medicalPromises)){
                queryAvailableTimeContext.setReceiverFullAddress(medicalPromises.get(0).getStationAddress());
            }
        }
        CommonCheckPreCreateOrderBo commonCheckPreCreateOrderBo = angelShipDomainService.queryAvailableTime(queryAvailableTimeContext);
        if(commonCheckPreCreateOrderBo==null||CollectionUtils.isEmpty(commonCheckPreCreateOrderBo.getPickupSliceTimes())){
            log.info("handleJdLogisticsDateLogic commonCheckPreCreateOrderBo为空");
            return dates;
        }
        commonCheckPreCreateOrderBo.getPickupSliceTimes().forEach(t->{
            if(CollectionUtils.isEmpty(t.getPickupSliceTimes())){
                return;
            }
            t.getPickupSliceTimes().forEach(m->{
                Date dayOfTomorrow = TimeUtils.getDateStart(TimeUtils.add(new Date(),2,Calendar.DAY_OF_MONTH));
                Date d = TimeUtils.strToDate(t.getDateKey());
                if(d.before(dayOfTomorrow)){
                    //按产品要求,排除掉后天的时间
                    dates.add(TimeUtils.timeStrToDate(t.getDateKey()+" "+m.getStartTime(),TimeFormat.LONG_PATTERN_LINE));
                    dates.add(TimeUtils.timeStrToDate(t.getDateKey()+" "+m.getEndTime(),TimeFormat.LONG_PATTERN_LINE));
                }
            });
        });
        return dates;
    }

    /**
     * 是否非快检模式
     * @param skuInfoMap
     * @return
     */
    private Boolean isSelfTestTransport(Map<Long, JdhSkuDto> skuInfoMap) {
        List<JdhSkuDto> collect = new ArrayList<>(skuInfoMap.values());
        JdhSkuDto jdhSkuDto = collect.get(0);
        return ServiceTypeNewEnum.TRANSPORT_TEST.getType().equals(jdhSkuDto.getServiceType());

    }

    /**
     *
     * @param param
     * @param skuInfoMap
     * @return
     */
    private AppointTimeServiceFeeDetail getAppointTimeServiceFeeDetail(AvaiableAppointmentTimeParam param, Map<Long, JdhSkuDto> skuInfoMap) {
        if (StringUtils.isBlank(param.getUserPin()) || StringUtils.isBlank(param.getAddressId())) {
            return null;
        }
        try{
            CalcOrderServiceFeeContext context = new CalcOrderServiceFeeContext();
            // 获取地址信息
            AddressDetailBO addressDetail = getAddressDetail(param.getUserPin(), Long.valueOf(param.getAddressId()));
            AddressInfoValueObject addressInfo = new AddressInfoValueObject();
            addressInfo.setTownId(addressDetail.getTownId());
            addressInfo.setCountyId(addressDetail.getCountyId());
            addressInfo.setCityId(addressDetail.getCityId());
            addressInfo.setProvinceId(addressDetail.getProvinceId());
            context.setAddressInfo(addressInfo);
            context.setFeeType(JdOrderFeeTypeEnum.NIGHT_SERVICE_FEE.getType());
            //增强垂直业务身份以及用于动态计算价格
            enhanceVerticalCode(context, Lists.newArrayList(skuInfoMap.values()));
            AppointTimeServiceFeeDetail serviceFeeDetail = jdhOrderDomainService.calcOrderAppointTimeServiceFee(context);
            return serviceFeeDetail;
        } catch (Exception e){
            log.warn("TradeApplicationImpl getAppointTimeServiceFeeDetail error", e);
            return null;
        }
    }

    private Boolean isKnightLogicBySku(Map<Long, JdhSkuDto> skuInfoMap) {
        List<JdhSkuDto> collect = new ArrayList<>(skuInfoMap.values());
        JdhSkuDto jdhSkuDto = collect.get(0);
        if(duccConfig.getAngelStationInventoryConfig()!=null && CollectionUtils.isNotEmpty(duccConfig.getAngelStationInventoryConfig().getServiceType())){
            return duccConfig.getAngelStationInventoryConfig().getServiceType().contains(jdhSkuDto.getServiceType());
        }else{
            return ServiceTypeNewEnum.KNIGHT_TEST.getType().equals(jdhSkuDto.getServiceType());
        }
    }

    private Boolean isCareLogicBySku(Map<Long, JdhSkuDto> skuInfoMap) {
        List<JdhSkuDto> collect = new ArrayList<>(skuInfoMap.values());
        JdhSkuDto jdhSkuDto = collect.get(0);
        return ServiceTypeNewEnum.isHomeCare(jdhSkuDto.getServiceType());
    }

    private Integer getServiceType(Map<Long, JdhSkuDto> skuInfoMap, String userPin) {
        List<JdhSkuDto> collect = new ArrayList<>(skuInfoMap.values());
        List<Long> skuIds = collect.stream().map(JdhSkuDto::getSkuId).collect(Collectors.toList());
        if(StringUtils.isNotBlank(userPin)){
            String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.EXECUTE_ACTION_BUY_NOW_PIN_SKU_LIST_KEY, userPin);
            OrderUserActionDTO orderUserActionDTO = JsonUtil.parseObject(jimClient.get(redisKey), OrderUserActionDTO.class);
            if(Objects.nonNull(orderUserActionDTO) && Objects.nonNull(orderUserActionDTO.getServiceType())){
                List<SkuItemDTO> skuItemDtoList = orderUserActionDTO.getBundleInfoDTOList().get(0).getSkuItemDTOList();
                if(CollectionUtils.isNotEmpty(skuItemDtoList) && skuItemDtoList.stream().map(s -> Long.parseLong(s.getId())).collect(Collectors.toList()).containsAll(skuIds)){
                    return Integer.parseInt(orderUserActionDTO.getServiceType());
                }
            }
        }
        JdhSkuDto jdhSkuDto = collect.get(0);
        return jdhSkuDto.getServiceType();
    }

    /**
     * 过滤map中时间key没有库存的日期
     *
     * @param displayDate
     * @param availableInventoryList
     */
    private void filterDisplayDateByInventory(Map<String, Map<String, List<AppointmentTimeRangeDTO>>> displayDate, List<Date> availableInventoryList) {
        Set<String> availableInventorySet = new HashSet<>();
        DateFormat dayFormat = getAppointDateFormat();
        Optional.ofNullable(availableInventoryList).map(List::stream).orElseGet(Stream::empty).forEach(date -> {
            availableInventorySet.add(dayFormat.format(date));
        });
        if (CollectionUtils.isNotEmpty(availableInventorySet)) {
            List<String> planRemoveKey = new ArrayList<>();
            for (String dateString : displayDate.keySet()) {
                if (!availableInventorySet.contains(dateString)) {
                    planRemoveKey.add(dateString);
                }
            }
            planRemoveKey.forEach(s -> {
                displayDate.remove(s);
            });
        } else {
            displayDate.clear();
        }
    }

    /**
     * 排期格式化
     * @return
     */
    private DateFormat getAppointDateFormat() {
        return new SimpleDateFormat("M月d日[E]");
    }

    public Map<String, Map<String, List<AppointmentTimeRangeDTO>>> getDisplayDate(AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO,
                                                                                  List<Date> availableInventoryList,
                                                                                  Boolean isKnight,
                                                                                  AppointTimeServiceFeeDetail serviceFeeDetail,
                                                                                  Boolean isCare) {
        //查询可用列表的时间窗口
        Integer o2oAvailableTimeWindow = duccConfig.getO2oAvailableTimeWindow();
        Map<String, Map<String, List<AppointmentTimeRangeDTO>>> result = new LinkedHashMap<>();
        List<AppointmentTimeRangeDTO> avaiableDateList = avaiableAppointmentTimeDTO.getAvaiableDateList(isCare, o2oAvailableTimeWindow);
        Calendar calendar = Calendar.getInstance();
        DateFormat dayFormat = getAppointDateFormat();
        DateFormat ampmFormat = new SimpleDateFormat("a");
        Map<String, List<AppointmentTimeRangeDTO>> dayGroup = avaiableDateList.stream().collect(Collectors.groupingBy(s -> dayFormat.format(s.getBeginTime()), LinkedHashMap::new, Collectors.toList()));
        dayGroup.forEach((k1, v1) -> {
            Map<String, List<AppointmentTimeRangeDTO>> ampmMap = new LinkedHashMap<>();
            v1.stream().collect(Collectors.groupingBy(s -> ampmFormat.format(s.getBeginTime()), LinkedHashMap::new, Collectors.toList())).forEach((k2, v2) -> {
                List<AppointmentTimeRangeDTO> ampmList = new ArrayList<>();
                v2.forEach(e -> {
                    calendar.setTime(e.getBeginTime());
                    // 整点场景的结束时间为下一个小时整点，非整点场景的结束时间为下两个小时整点
                    if (isCare){
                        if(Objects.nonNull(e.getIsImmediately()) && e.getIsImmediately()) {
                            calendar.add(Calendar.HOUR_OF_DAY, 2);
                        }else {
                            calendar.add(Calendar.HOUR_OF_DAY, 1);
                        }
                    }else {
                        calendar.add(Calendar.HOUR_OF_DAY, 1);
                        if (calendar.get(Calendar.MINUTE) != 0) {
                            calendar.set(Calendar.MINUTE, 0);
                            calendar.set(Calendar.SECOND, 0);
                        }
                    }
                    //2025-02-19 可下单时间优化需求改动。如果结束时间跨天，在第二天0-1点之内，默认设置为前一天23:59
                    if (calendar.get(Calendar.HOUR_OF_DAY) == 0) {
                        calendar.set(Calendar.HOUR_OF_DAY, 0);
                        calendar.set(Calendar.MINUTE, 0);
                        calendar.set(Calendar.SECOND, 0);
                        calendar.add(Calendar.SECOND,-1);
                    }
                    e.setEndTime(calendar.getTime());
                    //增强库存是否支持预约
                    enhanceInventoryIsAvailable(availableInventoryList, e);
                    //夜间服务费
                    if (Objects.nonNull(serviceFeeDetail)) {
                        LocalTime beginTime = LocalTime.parse(e.getBeginTimeLocalTimeStr(), DateTimeFormatter.ofPattern("HH:mm"));
                        LocalTime endTime = beginTime.plusHours(1).withMinute(0).withSecond(0).withNano(0);
                        //处理开始时间为23:00的情况，加一小时后endtime变为0:00，需改为23:59
                        if (endTime.getHour() < beginTime.getHour() && endTime.getHour() == 0) {
                            endTime = endTime.plusMinutes(-1);
                        }
                        Optional<List<TimeIntervalIntersection.TimeInterval>> optional = TimeIntervalIntersection.groupCalculateIntersection(
                                new TimeIntervalIntersection.TimeInterval(beginTime,endTime),
                                serviceFeeDetail.getTimeIntervalList()
                        );
                        e.setNightDoorFee(serviceFeeDetail.getFee());
                        e.setIsNightService(optional.isPresent());
                    }
                    ampmList.add(e);
                });
                ampmMap.put(k2, ampmList);
            });
            result.put(k1, ampmMap);
        });
//        log.info("tradeApplicationImpl.getDisplayDate.result={}",JsonUtil.toJSONString(result));
        return result;
    }

    /***
     * 库存是否支持预约
     * @param availableInventoryList
     * @param appointmentTimeRangeDTO
     * @return
     */
    private void enhanceInventoryIsAvailable(List<Date> availableInventoryList, AppointmentTimeRangeDTO appointmentTimeRangeDTO) {
        if (CollectionUtils.isEmpty(availableInventoryList)) {
            appointmentTimeRangeDTO.setIsAvailable(false);
        }
        if(log.isDebugEnabled()){
            log.debug("TradeApplicationImpl.enhanceInventoryIsAvailable.appointmentTimeRangeDTO={}", JsonUtil.toJSONString(appointmentTimeRangeDTO));
        }
        if (Objects.nonNull(appointmentTimeRangeDTO)) {
            //需要注意如果不是立即预约的话，需要将date类型向上取整秒 否则会出现 2024-06-19 19:00:00.018 毫秒值 无法进行时间范围的框定 开始时间向上取整 结束时间向下取整
            if (Objects.nonNull(appointmentTimeRangeDTO.getIsImmediately())&&!appointmentTimeRangeDTO.getIsImmediately()) {
                appointmentTimeRangeDTO.setBeginTime(TimeUtils.roundUpToSecond(appointmentTimeRangeDTO.getBeginTime()));
            }
            appointmentTimeRangeDTO.setEndTime(TimeUtils.floorDateToMinute(appointmentTimeRangeDTO.getEndTime()));

            if(log.isDebugEnabled()){
                log.debug("TradeApplicationImpl.enhanceInventoryIsAvailable.appointmentTimeRangeDTO.before={}", JsonUtil.toJSONString(appointmentTimeRangeDTO));
            }
            appointmentTimeRangeDTO.setIsAvailable(TimeUtils.isWithinTimeRanges(availableInventoryList, appointmentTimeRangeDTO.getBeginTime(), appointmentTimeRangeDTO.getEndTime()));
            if(log.isDebugEnabled()){
                log.debug("TradeApplicationImpl.enhanceInventoryIsAvailable.appointmentTimeRangeDTO.after={}", JsonUtil.toJSONString(appointmentTimeRangeDTO));
            }
        }

    }


    /**
     * 处理库存逻辑
     *
     * @param param
     * @return
     */
    private List<Date> handleInventoryDateLogic(AvaiableAppointmentTimeParam param, Integer serviceType) {
        QueryBatchSkuInventoryRequest request = new QueryBatchSkuInventoryRequest();
        request.setAngelType(AngelTypeEnum.DELIVERY.getType());
        request.setSkuNos(new HashSet<>(param.getSkuIds()));
        List<AddressDetail> addressList = new ArrayList<>();
        if(StringUtils.isNotBlank(param.getAddressId()) || StringUtils.isNotBlank(param.getFullAddress())) {
            AddressDetail addressDetail = new AddressDetail();
            addressDetail.setAddressId(param.getAddressId());
            addressDetail.setFullAddress(param.getFullAddress());
            addressList.add(addressDetail);
        }
        request.setAddressList(addressList);
        if(Integer.valueOf("1").equals(serviceType)){
            request.setInventoryChannelNo(1);
        } else if(Integer.valueOf("2").equals(serviceType)){
            request.setInventoryChannelNo(2);
        } else if(Integer.valueOf("3").equals(serviceType)){
            request.setInventoryChannelNo(3);
        } else if(Integer.valueOf("5").equals(serviceType)){
            request.setInventoryChannelNo(3);
        }
        log.info("TradeApplicationImpl.handleInventoryLogic.request={}", JSON.toJSONString(request));
        SkuInventoryResultDto skuInventoryResultDto;
        skuInventoryResultDto = stationApplication.querySkuInventory(request);
        log.info("TradeApplicationImpl.handleInventoryLogic.skuInventoryResultDto={}", JSON.toJSONString(skuInventoryResultDto));
        List<Date> availableDates = skuInventoryResultDto.getAvailableDates();
        log.info("TradeApplicationImpl.handleInventoryLogic.availableDates={}", JSON.toJSONString(availableDates));
        return availableDates;
    }

    /**
     * 计算服务费
     *
     * @param cmd cmd
     * @return {@link List}<{@link TradeServiceFeeInfoDTO}>
     */
    @Override
    public List<TradeServiceFeeInfoDTO> calcServiceFee(CalcTradeServiceFeeCmd cmd) {
        log.info("TradeApplicationImpl calcServiceFee cmd={}", JSON.toJSONString(cmd));
        CalcOrderServiceFeeContext context = serviceFeeApplicationConverter.calcServiceFeeCmd2Context(cmd);
        //增强垂直业务身份以及用于动态计算价格
        enhanceVerticalCode(context);
        List<JdOrderServiceFeeInfo> jdOrderServiceFeeInfos = jdhOrderDomainService.calcOrderServiceFee(context);
        return serviceFeeApplicationConverter.feeList2DtoList(jdOrderServiceFeeInfos);
    }

    /**
     * 申请退款
     *
     * @param param
     * @return
     */
    @Override
    public Boolean xfylOrderRefund(RefundOrderParam param) {
        log.info("TradeApplicationImpl xfylOrderRefund RefundOrderParam:{} ", JSON.toJSONString(param));
        //查询订单信息
        JdOrder orderDetail = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(Long.valueOf(param.getOrderId())).userPin(param.getUserPin()).build());
        AssertUtils.nonNull(orderDetail, TradeErrorCode.ORDER_IS_NULL);
        //判断当前订单是子单 还是父单 如果是加项场景，非小程序直接查询订单传入的是父单，其余场景传入的是子单
        boolean noParentOrder = orderDetail.getParentId().intValue() == 0;
        //判断当前订单来源是C端还是运营端
        boolean isRequestByC = Objects.nonNull(param.getRefundSource()) &&
                (param.getRefundSource().equals(CommonConstant.ONE_STR) || param.getRefundSource().equals(CommonConstant.THREE_STR));
        // (C端发起退款 且 订单包含加项)时，将父单下其他子单一并申请退款
        if (isRequestByC){
            List<JdOrder> childOrders;
            if (noParentOrder){//不拆单
                childOrders = this.getChildOrderList(orderDetail.getOrderId());
                if (HasAddedEnum.HAS_ADDED.getValue().equals(orderDetail.getHasAdded())){
                    addItemOrderRefund(param,childOrders,!isRequestByC);
                }else{
                    childOrders.forEach(s -> {
                        param.setOrderId(s.getOrderId());
                        buildRefundTask(s, param);
                    });
                }
            }else {
                //拆单有子单
                childOrders = this.getChildOrderList(orderDetail.getParentId());
                JdOrder parentOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderDetail.getParentId()).userPin(param.getUserPin()).build());
                if (HasAddedEnum.HAS_ADDED.getValue().equals(parentOrder.getHasAdded())){
                    addItemOrderRefund(param,childOrders,isRequestByC);
                    return true;
                }else {
                    this.buildRefundTask(orderDetail, param);
                }
            }

        }else {//如果是运营端 传啥就退啥
            if (!noParentOrder){
                JdOrder parentOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderDetail.getParentId()).userPin(param.getUserPin()).build());
                if (HasAddedEnum.HAS_ADDED.getValue().equals(parentOrder.getHasAdded())){
                    List<JdOrder> childOrders = this.getChildOrderList(orderDetail.getParentId());
                    addItemOrderRefund(param,childOrders,isRequestByC);
                    return true;
                }else{
                    // 非加项包正常退款
                    this.buildRefundTask(orderDetail, param);
                }
            }else{
                // 非加项包正常退款
                this.buildRefundTask(orderDetail, param);
            }
        }
        return true;
    }


    private void addItemOrderRefund(RefundOrderParam param,List<JdOrder> childOrders,Boolean isRequestByC){
        BigDecimal refundAmount = param.getRefundAmount();
        AtomicReference<BigDecimal> subtractAmount = new AtomicReference<>(refundAmount);
        param.setHasAdded(Boolean.TRUE);
        Iterator<JdOrder> iterator = childOrders.iterator();
        while (iterator.hasNext()){
            JdOrder childOrder = iterator.next();
            BigDecimal orderAmount = childOrder.getOrderAmount();
            if(HasAddedEnum.HAS_ADDED.getValue().equals(childOrder.getHasAdded())){
                param.setFreezeAndInvalid(Boolean.FALSE);
            }else{
                param.setFreezeAndInvalid(Boolean.TRUE);
            }
            if (!isRequestByC){
                if(subtractAmount.get().compareTo(orderAmount) > 0){
                    param.setRefundAmount(orderAmount);
                    subtractAmount.set(subtractAmount.get().subtract(orderAmount));
                }else{
                    param.setRefundAmount(subtractAmount.get());
                    subtractAmount.set(BigDecimal.ZERO);
                }
            }
            if(!iterator.hasNext()){
                param.setLastChildOrder(Boolean.TRUE);
            }
            param.setOrderId(childOrder.getOrderId());
            buildRefundTask(childOrder, param);
        }
    }

    /**
     *
     * @param orderId
     * @return
     */
    private List<JdOrder> getChildOrderList(Long orderId){
        List<JdOrder> childOrders = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(orderId).build());
        if (CollectionUtils.isEmpty(childOrders)){
            childOrders = jdOrderRepository.findOrderListByOrderId(JdOrderIdentifier.builder().orderId(orderId).build());
        }
        return childOrders;
    }
    /**
     * 生成退款任务
     * @param orderDetail
     * @param param
     */
    private void buildRefundTask(JdOrder orderDetail, RefundOrderParam param){
        String lockKey = MessageFormat.format(RedisKeyEnum.JDH_ORDER_REFUND_LOCK_KEY.getRedisKeyPrefix(), orderDetail.getOrderId());
        try{
            log.info("[TradeApplicationImpl.buildRefundTask],param={}",JSON.toJSONString(param));
            boolean lockFlag = redisLockUtil.tryLock(lockKey, UUID.fastUUID().toString(),
                    RedisKeyEnum.JDH_ORDER_REFUND_LOCK_KEY.getExpireTime(), RedisKeyEnum.JDH_ORDER_REFUND_LOCK_KEY.getExpireTimeUnit());
            if (!lockFlag) {
                throw new BusinessException(TradeErrorCode.REFUND_SUBMIT);
            }
            checkOrderMoneyDetail(orderDetail);
            calcPromiseRefundFreeze(param);
            selfOrderRefund(param,orderDetail);
        }catch (Exception e){
            log.error("[TradeApplicationImpl.buildRefundTask],Exception=",e);
            throw e;
        }finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     *
     * @param orderDetail
     */
    private void checkOrderMoneyDetail(JdOrder orderDetail){
        if(orderDetail.getOrderAmount().compareTo(BigDecimal.ZERO) > 0){
            Long orderId = orderDetail.getOrderId();
            List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(orderId);
            if(CollUtil.isEmpty(jdOrderMoneyList)){
                jdOrderMoneyList = createOrderMoneyInfo(orderId);
                jdOrderMoneyRepository.batchSave(jdOrderMoneyList);
            }
            AssertUtils.isNotEmpty(jdOrderMoneyList,TradeErrorCode.QUERY_REFUND_PAY_TYPE_NULL);
        }
    }
    /**
     * @param param
     * @return
     */
    private Boolean calcPromiseRefundFreeze(RefundOrderParam param) {
        List<Long> promisePatientIdList = new ArrayList<>();
        List<String> serviceIdList = new ArrayList<>();
        Integer refundType = param.getRefundType();
        if (RefundTypeEnum.ORDER_REFUND.getType().equals(refundType)) {
            if(param.getHasAdded()){
                List<OrderRefundTaskDto> list = jdOrderRefundApplication.queryAddOrderRefundRecord(Long.valueOf(param.getOrderId()));
                if (CollUtil.isNotEmpty(list)) {
                    throw new BusinessException(TradeErrorCode.ORDER_REFUND_EXIST);
                }
            }else{
                List<OrderRefundTaskDto> list = jdOrderRefundApplication.queryOrderRefundRecord(Long.valueOf(param.getOrderId()));
                if (CollUtil.isNotEmpty(list)) {
                    throw new BusinessException(TradeErrorCode.ORDER_REFUND_EXIST);
                }
            }
            param.setLastChildOrder(true);
        }else if (RefundTypeEnum.AMOUNT_REFUND.getType().equals(refundType)) {
            BigDecimal refundAmount = param.getRefundAmount();
            if (Objects.isNull(refundAmount) || refundAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BusinessException(TradeErrorCode.REFUND_AMOUNT_NULL);
            }
            if(param.getFreezeAndInvalid()){
                List<RefundOrderSku> refundOrderSkuList = param.getRefundOrderSkuList();
                AssertUtils.isNotEmpty(refundOrderSkuList, TradeErrorCode.ORDER_REFUND_PROMISEPATIENTID);
                promisePatientIdList = refundOrderSkuList.stream().map(RefundOrderSku::getPromisePatientId).collect(Collectors.toList());
                serviceIdList = refundOrderSkuList.stream().map(RefundOrderSku::getServiceId).collect(Collectors.toList());
                String serviceId = serviceIdList.get(0);
                MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
                medicalPromiseListRequest.setPromiseId(param.getPromiseId());
                List<MedicalPromiseDTO> medicalPromises = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
                List<Long> finalPromisePatientIdList1 = promisePatientIdList;

                List<MedicalPromiseDTO> selfMedicalPromisesList = medicalPromises.stream().filter(medical -> serviceId.equals(medical.getServiceId().toString())
                        && finalPromisePatientIdList1.contains(medical.getPromisePatientId())).collect(Collectors.toList());

                selfMedicalPromisesList = selfMedicalPromisesList.stream().filter(medical -> VoucherOpEnum.FREEZE.getStatus().equals(medical.getFreeze())
                        || MedicalPromiseStatusEnum.INVALID.getStatus().equals(medical.getStatus())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(selfMedicalPromisesList)) {
                    throw new BusinessException(TradeErrorCode.ORDER_REFUND_EXIST);
                }

                selfMedicalPromisesList = medicalPromises.stream().filter(medical -> serviceId.equals(medical.getServiceId().toString())
                        && !finalPromisePatientIdList1.contains(medical.getPromisePatientId())).collect(Collectors.toList());
                if (CollUtil.isEmpty(selfMedicalPromisesList)) {
                    param.setLastChildOrder(true);
                } else {
                    List<MedicalPromiseDTO> medicalPromisesOther = selfMedicalPromisesList.stream().filter(medical -> !MedicalPromiseStatusEnum.INVALID.getStatus().equals(medical.getStatus())
                            && CommonConstant.ZERO == medical.getFreeze()).collect(Collectors.toList());
                    log.info("PromiseLastRefundServiceAbility calcPromiseRefundFreeze medicalPromisesServiceList={}, medicalPromisesOther={}",
                            JSON.toJSONString(selfMedicalPromisesList), JSON.toJSONString(medicalPromisesOther));
                    if (CollUtil.isEmpty(medicalPromisesOther)) {
                        param.setLastChildOrder(true);
                    }
                }
            }
        }
        return Boolean.TRUE;
    }


    /**
     * @param param
     * @param orderDetail
     */
    private OrderRefundContext selfOrderRefund(RefundOrderParam param, JdOrder orderDetail) {
        log.info("TradeApplicationImpl selfOrderRefund param={}, orderDetail={}", JSON.toJSONString(param), JSON.toJSONString(orderDetail));
        OrderRefundContext context = TradeOrderRefundConverter.INSTANCE.convertToOrderRefundContext(param);
        if (CollUtil.isNotEmpty(param.getRefundOrderSkuList())) {
            List<RefundSku> refundSkuList = TradeOrderRefundConverter.INSTANCE.convertRefundSkuList(param.getRefundOrderSkuList());
            context.setRefundSkuList(refundSkuList);
        }

        context.setOrderDetail(orderDetail);
        context.setOrderUserPhone(orderDetail.getOrderUserPhone());
        context.setUserPin(orderDetail.getUserPin());
        context.setVerticalCode(orderDetail.getVerticalCode());
        context.setServiceType(orderDetail.getServiceType());
        context.setParentId(orderDetail.getParentId());
        log.info("TradeApplicationImpl.selfOrderRefund.context={}", JSON.toJSONString(context));
        /**退款原因 王雨*/
        context.setRefundReason(param.getRefundReason());
        context.setRefundReasonCode(param.getRefundReasonCode());

        TradeEventTypeEnum tradeEventTypeEnum = RefundTypeEnum.getTradeEventTypeEnum(param.getRefundType());
        context.init(tradeEventTypeEnum);
        AbilityExecutor executor = context.getExecutor();
        execute(executor, context);

        saveRefundTaskAndDetail(context);
        return context;
    }

    /**
     * 只想配置的condition和action
     * 这个execute和状态机调用的差异是，executor不由履约单状态控制
     *
     * @param executor
     * @param context
     */
    private void execute(AbilityExecutor executor, BusinessContext context) {
        // 未查询到当前状态+事件配置的执行器
        if (Objects.isNull(executor)) {
            log.error("TradeApplicationImpl submitDraft 未找到状态机执行器");
            throw new SystemException(SystemErrorCode.CONFIG_ERROR);
        }
        List<String> validConditions = executor.getConditionCodes();
        for (String code : validConditions) {
            DomainAbility condition = conditions.get(code);
            condition.execute(context);
            log.info("TradeApplicationImpl submitDraft conditionCode={}, context={}", code, JSON.toJSONString(context));
        }
        List<String> actionCodes = executor.getActionCodes();
        for (String code : actionCodes) {
            DomainAbility action = actions.get(code);
            action.execute(context);
            log.info("TradeApplicationImpl submitDraft cationCode={}, context={}", code, JSON.toJSONString(context));
        }
    }

    /**
     * init
     */
    @PostConstruct
    public void init() {
        Map<String, DomainAbility> map = applicationContext.getBeansOfType(DomainAbility.class);
        for (DomainAbility ability : map.values()) {
            if (!Objects.equals(ability.getAbilityCode().domainType().getCode(), DomainEnum.TRADE.getCode())) {
                log.info("TradeApplicationImpl init continue code={}", ability.getAbilityCode().getCode());
                continue;
            }
            if (Objects.equals(ability.getAbilityCode().getAbilityType(), AbilityCode.ACTION)) {
                actions.put(ability.getAbilityCode().getCode(), ability);
            } else if (Objects.equals(ability.getAbilityCode().getAbilityType(), AbilityCode.CONDITION)) {
                conditions.put(ability.getAbilityCode().getCode(), ability);
            }
        }
    }
    /**
     * 获取订单退款原因列表
     * @return
     */
    @Override
    public List<OrderCancelReasonDto> getOrderCancelReasons(RefundOrderParam param) {
        String orderCancelReasons = tradeDomainService.getOrderCancelReasons(param.getServiceType());
        if (StringUtils.isNotBlank(orderCancelReasons)) {
            return JsonUtil.parseArray(orderCancelReasons, OrderCancelReasonDto.class);
        }
        return null;
    }

    /**
     * findCompleteOrder
     *
     * @param request 请求
     * @return {@link CompleteOrderDto}
     */
    @Override
    public CompleteOrderDto findCompleteOrder(CompleteOrderRequest request) {
        JdOrderDTO orderDetail = this.getOrderDetail(OrderDetailParam.builder().orderId(request.getOrderId()).build());
        List<PromiseDto> promiseList = Collections.emptyList();
        if(Objects.nonNull(orderDetail)){

            // 填充业务模式
            if (StringUtils.isNotBlank(orderDetail.getVerticalCode())){
                JdhVerticalBusiness business = verticalBusinessRepository.find(orderDetail.getVerticalCode());
                orderDetail.setBusinessMode(business.getBusinessModeCode());
            }else {
                try {
                    // 解决订单还没有业务身份时，通过商品解析。
                    Optional<JdOrderItemDTO> orderItem = orderDetail.getJdOrderItemList().stream().findFirst();
                    if (orderItem.isPresent()) {
                        Long skuId = orderItem.get().getSkuId();
                        JdhSku sku = jdhSkuRepository.find(new JdhSkuIdentifier(skuId));
                        ServiceTypeNewEnum serviceTypeNewEnum = ServiceTypeNewEnum.getByType(sku.getServiceType());
                        orderDetail.setBusinessMode(serviceTypeNewEnum.getBusinessModeEnum().getCode());
                    }
                }catch (Exception e){
                    log.error("TradeApplicationImpl->findCompleteOrder find businessMode error", e);
                }
            }
            Long orderId = Objects.nonNull(orderDetail.getParentId()) && orderDetail.getParentId() > 0 ? orderDetail.getParentId() : orderDetail.getOrderId();
            List<Long> voucherIds = parseVoucherIds(orderId, orderDetail.getVerticalCode());
            if (CollUtil.isEmpty(voucherIds)) {
                return CompleteOrderDto.builder().jdOrder(orderDetail).build();
            }


            promiseList = promiseApplication.findByPromiseList(PromiseListRequest.builder().voucherIds(voucherIds).build());
        } else {
            JdOrderSaveParam jdOrderSaveParam = new JdOrderSaveParam();
            jdOrderSaveParam.setOrderId(String.valueOf(request.getOrderId()));
            jdOrderSaveParam.setUserPin(request.getPin());
            jdOrderSaveParam.setPartnerSource(PartnerSourceEnum.JDH_XFYL.getCode());
            JdOrder jdOrder = jdOrderApplication.saveJdOrder(jdOrderSaveParam);
            orderDetail = JdOrderConverter.INSTANCE.JdOrderToDTO(jdOrder);
        }

        return CompleteOrderDto.builder().jdOrder(orderDetail).promiseList(promiseList).build();
    }

    /**
     * 查询运营端退款提示信息
     *
     * @param param param
     * @return {@link List}<{@link ManRefundTipsInfoDto}>
     */
    @Override
    public ManRefundTipsInfoDto queryManRefundTipsInfo(ManRefundTipsParam param) {
        //参数校验
        if (Objects.isNull(param)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        if (Objects.isNull(param.getPromiseId())) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        List<ManRefundTipsOrderParam> tipsOrderList = param.getOrderList();
        if (CollUtil.isEmpty(tipsOrderList)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        tipsOrderList.forEach(ele -> {
            if (Objects.isNull(ele.getOrderId()) || Objects.isNull(ele.getPromisePatientId())) {
                throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
            }
        });

        checkPromiseId(param);

        ManRefundTipsOrderParam manRefundTipsOrderParam = tipsOrderList.get(0);
        Long orderId = manRefundTipsOrderParam.getOrderId();
        JdOrderDTO orderDetail = this.getOrderItemAndFeeInfo(OrderDetailParam.builder().orderId(orderId.toString()).build());
        JdOrderItemDTO orderItemDTO = orderDetail.getJdOrderItemList().get(0);
        List<JdOrderServiceFeeInfoDTO> jdOrderServiceFeeInfos = orderDetail.getJdOrderServiceFeeInfos();
        BigDecimal timePeriodAmount = null;
        BigDecimal homeVisitAmount = null;
        BigDecimal dynamicAmount = null;
//        BigDecimal upgraeFeeAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(jdOrderServiceFeeInfos)) {
            Map<Integer, List<JdOrderServiceFeeInfoDTO>> feeList = jdOrderServiceFeeInfos.stream().collect(Collectors.groupingBy(JdOrderServiceFeeInfoDTO::getAggregateSubType));
            List<JdOrderServiceFeeInfoDTO> timePeriodList = feeList.get(FeeAggregateTypeEnum.TIME_PERIOD_FEE.getSubType());
            if (CollUtil.isNotEmpty(timePeriodList)) {
                timePeriodAmount = timePeriodList.stream().map(JdOrderServiceFeeInfoDTO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                //maxRefundAmount = maxRefundAmount.add(timePeriodAmount);
            }
            List<JdOrderServiceFeeInfoDTO> dynamicList = feeList.get(FeeAggregateTypeEnum.DYNAMIC_FEE.getSubType());
            if (CollUtil.isNotEmpty(dynamicList)) {
                dynamicAmount = dynamicList.stream().map(JdOrderServiceFeeInfoDTO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
//            List<JdOrderServiceFeeInfoDTO> upgraeFeeList = feeList.get(FeeAggregateTypeEnum.UPGRADE_ANGEL_FEE.getSubType());
//            if (CollUtil.isNotEmpty(upgraeFeeList)) {
//                upgraeFeeAmount = upgraeFeeList.stream().map(JdOrderServiceFeeInfoDTO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
//            }
//            if(Objects.nonNull(dynamicAmount)){
//                upgraeFeeAmount = upgraeFeeAmount.add(dynamicAmount);
//            }

            List<JdOrderServiceFeeInfoDTO> homeVisitList = feeList.get(FeeAggregateTypeEnum.HOME_VISIT_FEE.getSubType());
            if (CollUtil.isNotEmpty(homeVisitList)) {
                homeVisitAmount = homeVisitList.stream().map(JdOrderServiceFeeInfoDTO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                //maxRefundAmount = maxRefundAmount.add(homeVisitAmount);
            }
        }

        BigDecimal maxRefundAmount = orderDetail.getOrderAmount();
        //BigDecimal maxRefundAmount = new BigDecimal("0");
        BigDecimal sugRefundAmount = null;

        QueryRefundAmountParam refundOrderParam = new QueryRefundAmountParam();
        refundOrderParam.setOrderId(orderId);
        refundOrderParam.setPromiseId(param.getPromiseId());
        refundOrderParam.setQueryAmountType(3);
        refundOrderParam.setPromisePatientIdList(param.getOrderList().stream().map(ManRefundTipsOrderParam::getPromisePatientId).collect(Collectors.toList()));
        OrderRefundAmountDto refundAmountDto = jdOrderRefundApplication.queryOrderRefundAmount(refundOrderParam);
        if (Objects.nonNull(refundAmountDto)) {
            sugRefundAmount = refundAmountDto.getSuggestRefundAmount();
            maxRefundAmount = refundAmountDto.getMostRefundAmount();
        }

        return ManRefundTipsInfoDto.builder()
                .orderId(orderId)
                .skuId(orderItemDTO.getSkuId().toString())
                .refundSkuNum(tipsOrderList.size())
                .skuAmount(orderItemDTO.getItemAmount())
                .timePeriodAmount(timePeriodAmount)
                .homeVisitAmount(homeVisitAmount)
                .dynamicAmount(dynamicAmount)
                .sugRefundAmount(sugRefundAmount)
                .maxRefundAmount(maxRefundAmount)
                .refundTips("最大退款金额" + maxRefundAmount + "元")
                .build();
    }

    /**
     * checkPromiseId
     *
     * @param param param
     */
    private void checkPromiseId(ManRefundTipsParam param){
        List<Long> promisePatientIdList = param.getOrderList().stream().map(ManRefundTipsOrderParam::getPromisePatientId).collect(Collectors.toList());
        LambdaQueryWrapper<JdhPromisePatientPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(JdhPromisePatientPo::getPromisePatientId,promisePatientIdList);
        List<JdhPromisePatientPo> jdhPromisePatientPos = promisePatientPoMapper.selectList(queryWrapper);
        Set<Long> promiseIdSet = jdhPromisePatientPos.stream().map(JdhPromisePatientPo::getPromiseId).collect(Collectors.toSet());
        if(promiseIdSet.size() > 1){
            throw new BusinessException(TradeErrorCode.PROMISE_ID_MULTI_ERROR);
        }
    }

    /**
     * @param context
     */
    private void saveRefundTaskAndDetail(OrderRefundContext context) {
        log.info("TradeApplicationImpl saveRefundTaskAndDetail context={}", JSON.toJSONString(context));
        JdOrderRefundTask jdOrderRefundTask = TradeOrderRefundConverter.INSTANCE.convertToJdOrderRefundTask(context);
        Long taskId = generateIdFactory.getId();
        jdOrderRefundTask.setTaskId(taskId);
        jdOrderRefundTask.setPatientId(context.getPatientId());
        jdOrderRefundTask.setOrderId(context.getOrderId());
        jdOrderRefundTask.setParentId(context.getParentId());
        jdOrderRefundTask.setVoucherId(context.getVoucherId());
        jdOrderRefundTask.setPromiseId(context.getPromiseId());
        OrderRefundDetailBo orderRefundDetailBo = context.getOrderRefundDetailBo();
        if (Objects.nonNull(orderRefundDetailBo)) {
            orderRefundDetailBo.setFreezeStatus(context.getFreezeStatus());
            jdOrderRefundTask.setRefundDetail(JsonUtil.toJSONString(context.getOrderRefundDetailBo()));
            orderRefundDetailBo.setFreezeAndInvalid(context.getFreezeAndInvalid());
        }
        jdOrderRefundTask.setOperator(context.getOperator());
        jdOrderRefundTask.setServiceId(context.getServiceId());
        jdOrderRefundTask.setRefundAmount(context.getRefundAmount());
        jdOrderRefundTask.setRefundStatus(RefundStatusEnum.NO_REFUND.getType());
        jdOrderRefundTask.setLastRefundTask(context.getLastPromise() ? 1 : 0);
        jdOrderRefundTask.setLastOrderTask(context.getLastChildOrder() ? 1 : 0);
        jdOrderRefundTask.setRefundReason(context.getRefundReason());
        jdOrderRefundTask.setRefundReasonCode(context.getRefundReasonCode());
        List<JdOrderRefundDetail> jdOrderRefundDetailList = new ArrayList<>();
        Map<Integer, BigDecimal> lastRefundAmountAndTypeMap = context.getLastRefundAmountAndTypeMap();
        for (Map.Entry<Integer, BigDecimal> entry : lastRefundAmountAndTypeMap.entrySet()) {
            Integer refundType = ReFundAmountTypeEnum.getRefundTypeOfType(entry.getKey());
            if (Objects.nonNull(refundType)) {
                JdOrderRefundDetail jdOrderRefundDetail = JdOrderRefundDetail.builder().build();
                jdOrderRefundDetail.setTaskId(taskId);
                Long taskDetailId = generateIdFactory.getId();
                jdOrderRefundDetail.setTaskDetailId(taskDetailId);
                jdOrderRefundDetail.setUserPin(context.getUserPin());
                jdOrderRefundDetail.setOrderUserPhone(context.getOrderUserPhone());
                jdOrderRefundDetail.setAmountType(entry.getKey());
                jdOrderRefundDetail.setKtRefundType(refundType);
                jdOrderRefundDetail.setTransactionNum(taskDetailId + "_" + refundType);
                jdOrderRefundDetail.setOrderId(context.getOrderId());
                jdOrderRefundDetail.setRefundAmount(entry.getValue());
                jdOrderRefundDetail.setRefundStatus(RefundStatusEnum.NO_REFUND.getType());
                jdOrderRefundDetailList.add(jdOrderRefundDetail);
            }
        }
        log.info("TradeApplicationImpl saveRefundTaskAndDetail jdOrderRefundTask={}, jdOrderRefundDetailList={}", JSON.toJSONString(jdOrderRefundTask), JSON.toJSONString(jdOrderRefundDetailList));
        jdOrderRefundApplication.saveJdOrderRefundTask(jdOrderRefundTask,jdOrderRefundDetailList);
        if(RefundTypeEnum.ORDER_REFUND.getType().equals(context.getRefundType()) || context.getLastChildOrder() || context.getHasAdded()){
            JdOrder jdOrder = JdOrder.builder().orderId(context.getOrderId()).orderStatus(OrderStatusEnum.ORDER_REFUNDING.getStatus()).build();
            jdOrderRepository.updateOrderStatusByOrderId(jdOrder);
        }
    }

    /**
     * 根据SKU配置的需提前预约时间、未来可约天数、每天可预约时间段计算出可约时间
     *
     * @param jdhSkuDtoList
     * @param isCare
     * @return
     */
    private AvaiableAppointmentTimeDTO calcAppointmentTime(List<JdhSkuDto> jdhSkuDtoList, Boolean isCare) {
        // 多SKU交集 最小可预约天数
        Integer maxScheduleDays = jdhSkuDtoList.stream().min(Comparator.comparing(JdhSkuDto::getMaxScheduleDays)).get().getMaxScheduleDays();
        if (maxScheduleDays == null || maxScheduleDays.intValue() == 0) {
            return null;
        }
        // 多SKU交集 最大需提前预约时间
        Integer advanceAppointTime = jdhSkuDtoList.stream().max(Comparator.comparing(JdhSkuDto::getAdvanceAppointTime, Comparator.nullsFirst(Comparator.naturalOrder()))).get().getAdvanceAppointTime();
        if (advanceAppointTime == null) {
            advanceAppointTime = 0;
        }
        // 多SKU交集 最大开始时段
        String beginTimeRange = jdhSkuDtoList.stream().max(Comparator.comparing(JdhSkuDto::getDayTimeFrameBegin)).get().getDayTimeFrameBegin();
        if (StringUtils.isBlank(beginTimeRange)) {
            beginTimeRange = "00:00";
        }
        // 多SKU交集 最小结算时段
        String endTimeRange = jdhSkuDtoList.stream().min(Comparator.comparing(JdhSkuDto::getDayTimeFrameEnd)).get().getDayTimeFrameEnd();
        if (StringUtils.isBlank(endTimeRange)) {
            endTimeRange = "24:00";
        }

        Date now = new Date();
        Calendar c = Calendar.getInstance();
        // 可预约开始时间
        c.setTime(now);
        // 提前大于0小时预约，不展示立即预约
        if (advanceAppointTime > 0) {
            if(isCare) {
                c.add(Calendar.HOUR_OF_DAY, advanceAppointTime);
                c.set(Calendar.SECOND, 0);
            }else {
                //非整点，去下一个整点时段
                if (c.get(Calendar.MINUTE) > 0) {
                    c.add(Calendar.HOUR_OF_DAY, advanceAppointTime + 1);
                } else {
                    c.add(Calendar.HOUR_OF_DAY, advanceAppointTime);
                }
                c.set(Calendar.MINUTE, 0);
                c.set(Calendar.SECOND, 0);
            }
        }
        Date avaiableTimeStart = c.getTime();
        // 可预约结束时间
        c.setTime(now);
        c.add(Calendar.DATE, maxScheduleDays - 1);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        Date avaiableTimeEnd = c.getTime();
        return new AvaiableAppointmentTimeDTO(avaiableTimeStart, avaiableTimeEnd, beginTimeRange, endTimeRange, advanceAppointTime,null,null);
    }

    /**
     * 组装buildOrderPromiseDto
     *
     * @param promise
     * @param orderDetail
     * @param vouMap
     * @return
     */
    private static OrderPromiseDto buildOrderPromiseDto(JdhPromise promise, JdOrder orderDetail, Map<Long, JdhVoucher> vouMap) {
        OrderPromiseDto promiseDto = new OrderPromiseDto();
        promiseDto.setPromiseId(promise.getPromiseId());
        promiseDto.setAppointmentId(promise.getAppointmentId());
        promiseDto.setServiceType(promise.getServiceType());
        promiseDto.setVerticalCode(promise.getVerticalCode());
        promiseDto.setVenderId(orderDetail.getVenderId());
        promiseDto.setOrderStatus(orderDetail.getOrderStatus());
        promiseDto.setPaymentTime(orderDetail.getPaymentTime());
        if (Objects.nonNull(promise.getStore())) {
            promiseDto.setStoreId(promise.getStore().getStoreId());
            promiseDto.setStoreName(promise.getStore().getStoreName());
            promiseDto.setStoreAddr(promise.getStore().getStoreAddr());
        }
        JdhVoucher voucher = vouMap.get(promise.getVoucherId());
        promiseDto.setOrderId(voucher.getExtend().getOrderId());
        if (Objects.nonNull(promise.getAppointmentTime())) {
            String day = promise.getAppointmentTime().formatAppointDate();
            promiseDto.setAppointmentDate(day);
            if (Objects.equals(DateTypeEnum.SCHEDULE_BY_DATE.getType(), promise.getAppointmentTime().getDateType())) {
                promiseDto.setTimeDesc(promise.getAppointmentTime().formatAppointDate());
            } else {
                String desc = promise.getAppointmentTime().formatAppointTimeDesc();
                promiseDto.setTimeDesc(desc);
            }
            promiseDto.setDateType(promise.getAppointmentTime().getDateType());
        }

        //用户信息
        if (Objects.nonNull(promise.findOnlyPatient())) {
            promiseDto.setUserName(Objects.nonNull(promise.findOnlyPatient().getUserName()) ?
                    promise.findOnlyPatient().getUserName().getName() : null);
            promiseDto.setUserPhone(Objects.nonNull(promise.findOnlyPatient().getPhoneNumber()) ?
                    promise.findOnlyPatient().getPhoneNumber().mask() : null);
            promiseDto.setUserCredentialNo(Objects.nonNull(promise.findOnlyPatient().getCredentialNum()) ?
                    promise.findOnlyPatient().getCredentialNum().mask() : null);
        }
        promiseDto.setAppointmentStatus(promise.getPromiseStatus());
        JdhPromiseStatusEnum statusEnum = JdhPromiseStatusEnum.convert(promise.getPromiseStatus());
        promiseDto.setAppointmentStatusName(Objects.nonNull(statusEnum) ? statusEnum.getDesc() : null);

        //将服务商品信息填入返回值
        List<PromiseService> services = promise.getServices();
        if (CollectionUtil.isNotEmpty(services)) {
            PromiseService basicService = null;
            if (services.size() == 1) {
                basicService = services.get(0);
            } else {
                Optional<PromiseService> optional = services.stream().filter(e -> Objects.equals(e.getTags(), ServiceTagEnum.BASIC.getType())).findFirst();
                basicService = optional.orElse(null);
            }
            if (Objects.nonNull(basicService)) {
                promiseDto.setSkuId(String.valueOf(basicService.getServiceId()));
                promiseDto.setSkuName(basicService.getServiceName());
            }
        }
        promiseDto.setVerticalCode(promise.getVerticalCode());
        return promiseDto;
    }

    /**
     * 根据orderId查询服务单，再根据服务单查询履约单
     *
     * @param orderId
     * @return
     */
    private List<Long> parseVoucherIds(Long orderId, String verticalCode) {
        List<JdhVoucher> vouchers = voucherRepository.listBySourceVoucherId(Arrays.asList(orderId.toString()), verticalCode);
        log.info("TradeApplicationImpl->parseVoucherIds 根据orderId查询 vouchers={}", JSON.toJSONString(vouchers));
        if (CollUtil.isEmpty(vouchers)) {
            List<JdOrderItem> items = jdOrderItemRepository.listByOrderId(orderId);
            log.info("TradeApplicationImpl->parseVoucherIds items={}", JSON.toJSONString(items));
            if (CollectionUtil.isEmpty(items)) {
                return Collections.emptyList();
            }
            List<Long> itemIds = items.stream().map(JdOrderItem::getOrderItemId).collect(Collectors.toList());
            List<String> sourceVoucherId = Lists.newArrayList();
            for (Long itemId : itemIds) {
                sourceVoucherId.add(String.valueOf(itemId));
            }
            vouchers = voucherRepository.listBySourceVoucherId(sourceVoucherId, verticalCode);
            log.info("TradeApplicationImpl->parseVoucherIds vouchers={}", JSON.toJSONString(vouchers));
        }

        if (CollectionUtil.isEmpty(vouchers)) {
            return Collections.emptyList();
        }
        return vouchers.stream().map(JdhVoucher::getVoucherId).collect(Collectors.toList());
    }

    /**
     * 查询订单详情
     *
     * @param orderId
     */
    @Override
    public JdOrderDTO queryOrderFullDetail(Long orderId) {
        OrderDetailParam orderDetailParam = new OrderDetailParam();
        orderDetailParam.setOrderId(String.valueOf(orderId));
        return getOrderDetail(orderDetailParam);
    }

    /**
     * @param orderId
     * @param serviceId
     * @return
     */
    @Override
    public JdOrderDTO queryOrderSettleDetail(Long orderId, String serviceId) {
        OrderDetailParam orderDetailParam = new OrderDetailParam();
        orderDetailParam.setOrderId(String.valueOf(orderId));
        orderDetailParam.setServiceId(serviceId);
        return getOrderSettleDetail(orderDetailParam);
    }

    /**
     * @param orderId
     * @param serviceId
     * @return
     */
    @Override
    public JdOrderDTO getSplitOrderSettleDetail(Long orderId, String serviceId,Long promiseId) {
        OrderDetailParam orderDetailParam = new OrderDetailParam();
        orderDetailParam.setParentId(orderId);
        orderDetailParam.setServiceId(serviceId);
        orderDetailParam.setPromiseId(promiseId);
        return getSplitOrderSettleDetail(orderDetailParam);
    }


    /**
     * 护士费用细项
     *
     * @param calcAngelSettleFeeParam
     * @return
     */
    @Override
    public List<ServiceFeeDetailDTO> calcNoFreeOrderServiceFee(CalcAngelSettleFeeParam calcAngelSettleFeeParam) {
        CalcOrderServiceFeeContext context = bulidCalcOrderServiceFeeContext(calcAngelSettleFeeParam);
        List<ServiceFeeDetail> serviceFeeDetailList = jdhOrderDomainService.calcNoFreeOrderServiceFee(context);
        return TradeSettleApplicationConverter.ins.convertToServiceFeeDetailDTOList(serviceFeeDetailList);
    }

    /**
     * @param orderId
     * @return
     */
    @Override
    public BigDecimal getOrderRefundAmount(Long orderId) {
        return jdOrderRefundApplication.getOrderRefundAmount(orderId);
    }

    /**
     * @param orderId
     * @return
     */
    @Override
    public List<JdOrderDTO> queryOrderIdList(Long orderId) {
        List<JdOrder> jdOrderList = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(orderId).build());
        if (CollUtil.isEmpty(jdOrderList)) {
            JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderId).build());
            jdOrderList = new ArrayList<>();
            jdOrderList.add(jdOrder);
        }
        List<JdOrderDTO> jdOrderDTOList = JdOrderConverter.INSTANCE.JdOrderToDTOList(jdOrderList);
        return jdOrderDTOList;
    }

    /**
     * @param calcAngelSettleFeeParam
     * @return
     */
    private CalcOrderServiceFeeContext bulidCalcOrderServiceFeeContext(CalcAngelSettleFeeParam calcAngelSettleFeeParam) {
        CalcOrderServiceFeeContext context = CalcOrderServiceFeeContext.builder().build();
        AddressUpdateParam addressInfo = calcAngelSettleFeeParam.getAddressUpdateParam();
        if (Objects.nonNull(addressInfo)) {
            AddressInfoValueObject addressInfoValueObject = new AddressInfoValueObject();
            addressInfoValueObject.setProvinceId(addressInfo.getProvinceId());
            addressInfoValueObject.setCityId(addressInfo.getCityId());
            addressInfoValueObject.setCountyId(addressInfo.getCountyId());
            context.setAddressInfo(addressInfoValueObject);
        }
        AppointmentTimeParam appointmentTimeDto = calcAngelSettleFeeParam.getAppointmentTimeParam();
        if (Objects.nonNull(appointmentTimeDto)) {
            OrderAppointmentTimeValueObject appointmentTimeValueObject = new OrderAppointmentTimeValueObject();
            appointmentTimeValueObject.setAppointmentStartTime(appointmentTimeDto.getAppointmentStartTime());
            appointmentTimeValueObject.setIsImmediately(appointmentTimeDto.getIsImmediately());
            context.setAppointmentTime(appointmentTimeValueObject);
        }
//        List<JdOrderItemDTO> jdOrderItemList = orderDetail.getJdOrderItemList();
//        Set<String> skuIds = new HashSet<>();
//        if(CollUtil.isNotEmpty(jdOrderItemList)){
//            jdOrderItemList.forEach(jdOrderItem -> {
//                skuIds.add(jdOrderItem.getSkuId().toString());
//            });
//            context.setSkuIds(skuIds);
//        }
        return context;
    }

    /**
     * 生成订单金额信息列表
     *
     * @param orderId
     * @return
     */
    private static List<JdOrderMoney> createOrderMoneyInfo(Long orderId) {
        OrderCalculationQueryServiceRpc orderCalculationQueryServiceRpc = SpringUtil.getBean(OrderCalculationQueryServiceRpc.class);
        String amountAndExpand = orderCalculationQueryServiceRpc.queryOrderSplitAmountAndExpand(orderId);
        List<JdOrderMoney> jdOrderMoneyList = new ArrayList<>();
        if (StringUtils.isBlank(amountAndExpand)) {
            return jdOrderMoneyList;
        }
        List<OrderAmount> orderAmountList = JSON.parseArray(amountAndExpand, OrderAmount.class);
        log.info("JdOrderFactory -> createOrderMoneyInfo, orderId={}, orderAmountList={}", orderId, JSON.toJSONString(orderAmountList));

        if (CollectionUtils.isEmpty(orderAmountList)) {
            log.info("JdOrderFactory -> createOrderMoneyInfo 获取订单金额明细失败, orderId={}", orderId);
            return jdOrderMoneyList;
        }
        for (OrderAmount orderAmount : orderAmountList) {
            if (null == orderAmount.getSkuId()) {
                log.info("JdOrderFactory -> createOrderMoneyInfo, skuid is null,  orderAmount={}", JSON.toJSONString(orderAmount));
                continue;
            }
            Map<Integer, List<OrderAmountExpand>> orderAmountExpandMap = orderAmount.getAmountExpands().stream().collect(Collectors.groupingBy(b -> b.getType()));
            for (Map.Entry<Integer, List<OrderAmountExpand>> orderAmountExpand : orderAmountExpandMap.entrySet()) {
                JdOrderMoney entity = new JdOrderMoney();
                List<OrderAmountExpand> orderAmountExpandList = orderAmountExpand.getValue();
                Integer amountType = orderAmountExpand.getKey();
                BigDecimal amount = BigDecimal.ZERO;
                for (OrderAmountExpand amountExpand : orderAmountExpandList) {
                    amount = amount.add(amountExpand.getAmount());
                }
                if (amount.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                entity.setOrderId(orderAmount.getOrderId());
                entity.setSkuId(orderAmount.getSkuId());
                entity.setMoneyType(amountType);
                entity.setAmount(amount);
                entity.setVersion(NumConstant.NUM_1);
                entity.setYn(YnStatusEnum.YES.getCode());
                entity.setCreateTime(new Date());
                entity.setUpdateTime(new Date());
                jdOrderMoneyList.add(entity);
            }
        }

        return jdOrderMoneyList;
    }

    private  String formatAppointmentTime(String appointmentStartTime, String appointmentEndTime) {
        LocalDateTime startTime = LocalDateTime.parse(appointmentStartTime, DateTimeFormatter.ofPattern(CommonConstant.YMDHM));
        LocalDateTime endTime = LocalDateTime.parse(appointmentEndTime, DateTimeFormatter.ofPattern(CommonConstant.YMDHM));
        String formattedStartTime = startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd&&&HH:mm"));
        String formattedEndTime = endTime.format(DateTimeFormatter.ofPattern(CommonConstant.HM));
        return formattedStartTime + "-" + formattedEndTime;
    }

    /**
     * 增强垂直业务身份
     * @param context
     */
    private void enhanceVerticalCode(CalcOrderServiceFeeContext context) {
        List<JdhSku> jdhSkus = jdhSkuRepository.queryMultiSku(context.getSkuIds().stream().map(s -> JdhSku.builder().skuId(Long.valueOf(s)).build()).filter(Objects::nonNull).collect(Collectors.toList()));
        Map<String, String> verticalCodeBySkuInfoMap = duccConfig.getVerticalCodeBySkuInfoMap();
        log.info("TradeApplicationImpl enhanceVerticalCode jdhSkus={}", JSON.toJSONString(jdhSkus));
        if (CollectionUtils.isNotEmpty(jdhSkus)) {
            String key = new StringBuilder(String.valueOf(jdhSkus.get(0).getChannelId())).append("*").append(jdhSkus.get(0).getServiceType()).toString();
            context.setVerticalCode(  verticalCodeBySkuInfoMap.get(key));
            Map<String, Map<String,String>> channelServiceTypeConvertMap = duccConfig.getChannelServiceTypeConvertMap();
            log.info("TradeApplicationImpl enhanceVerticalCode channelServiceTypeConvertMap={}", JSON.toJSONString(channelServiceTypeConvertMap));
            Map<String,String> newChannelIdType = channelServiceTypeConvertMap.get(key);
            log.info("TradeApplicationImpl enhanceVerticalCode newChannelIdType={}", JSON.toJSONString(newChannelIdType));
            context.setChannelId(Long.valueOf(newChannelIdType.get("channelId")));
            context.setServiceType(Integer.valueOf(newChannelIdType.get("serviceType")));

            List<JdhSku> mainSkuList = jdhSkus.stream().filter(s -> SkuRelTypeEnum.MAIN_ITEM.getType().equals(s.getSkuType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mainSkuList)){
                context.setMainSkuId(String.valueOf(mainSkuList.get(0).getSkuId()));
            }
        }
    }

    /**
     * 增强垂直业务身份
     * @param context
     */
    private void enhanceVerticalCode(CalcOrderServiceFeeContext context, List<JdhSkuDto> jdhSkus) {
        Map<String, String> verticalCodeBySkuInfoMap = duccConfig.getVerticalCodeBySkuInfoMap();
        if (CollectionUtils.isNotEmpty(jdhSkus)) {
            String key = new StringBuilder(String.valueOf(jdhSkus.get(0).getChannelId())).append("*").append(jdhSkus.get(0).getServiceType()).toString();
            context.setVerticalCode(  verticalCodeBySkuInfoMap.get(key));

            Map<String, Map<String,String>> channelServiceTypeConvertMap = duccConfig.getChannelServiceTypeConvertMap();
            Map<String,String> newChannelIdType = channelServiceTypeConvertMap.get(key);
            context.setChannelId(Long.valueOf(newChannelIdType.get("channelId")));
            context.setServiceType(Integer.valueOf(newChannelIdType.get("serviceType")));
            List<JdhSkuDto> mainSkuList = jdhSkus.stream().filter(s -> SkuRelTypeEnum.MAIN_ITEM.getType().equals(s.getSkuType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mainSkuList)) {
                context.setMainSkuId(String.valueOf(mainSkuList.get(0).getSkuId()));
            }
        }
    }

    /**
     * 兼容老版本排期列表
     *
     * @return
     */
    public List<XfylAppointDateDTO> getCompatibleGroupDTO(Map<String, Map<String, List<AppointmentTimeRangeDTO>>> displayDate, Boolean isCare) {
        List<XfylAppointDateDTO> result = new ArrayList<>();
        if (MapUtils.isNotEmpty(displayDate)) {
            displayDate.forEach((k1, v1) -> {
                XfylAppointDateDTO xfylAppointDateDTO = new XfylAppointDateDTO();
                result.add(xfylAppointDateDTO);
                xfylAppointDateDTO.setAppointDateDesc(k1);
                xfylAppointDateDTO.setStatusLabel("可约");
                xfylAppointDateDTO.setStatus(1);
                xfylAppointDateDTO.setDateType(2);
                List<XfylAppointDateTimeGroupDTO> appointDateTimeGroupDTOList = new ArrayList<>();
                xfylAppointDateDTO.setAppointDateTimeGroupDTOList(appointDateTimeGroupDTOList);
                v1.forEach((k2, v2) -> {
                    XfylAppointDateTimeGroupDTO xfylAppointDateTimeGroupDTO = new XfylAppointDateTimeGroupDTO();
                    xfylAppointDateTimeGroupDTO.setGroupName(k2);
                    List<XfylAppointDateTimeDTO> dateTimeDTOList = new ArrayList<>();
                    xfylAppointDateTimeGroupDTO.setDateTimeDTOList(dateTimeDTOList);
                    v2.forEach(item -> {
                        XfylAppointDateTimeDTO xfylAppointDateTimeDTO = new XfylAppointDateTimeDTO();
                        xfylAppointDateTimeDTO.setAppointmentEndTime(item.getEndTimeStr());
                        xfylAppointDateTimeDTO.setStatusLabel(item.getIsAvailable() ? "可约" : "不可约");
                        xfylAppointDateTimeDTO.setAppointmentStartTime(item.getBeginTimeStr());
                        xfylAppointDateTimeDTO.setTimeDesc(item.getTips(isCare));
                        xfylAppointDateTimeDTO.setStatus(item.getIsAvailable() ? 1 : 2);
                        xfylAppointDateTimeDTO.setIsImmediately(item.getIsImmediately());
                        xfylAppointDateTimeDTO.setTimePeriodFeeDesc(item.getTimePeriodFeeDesc());
                        dateTimeDTOList.add(xfylAppointDateTimeDTO);
                    });
                    appointDateTimeGroupDTOList.add(xfylAppointDateTimeGroupDTO);
                });
            });
        }
        return result;
    }
    @Override
    public Boolean xfylOrderRefund2Knight(RefundOrderKnightParam param) {
        log.info("TradeApplicationImpl xfylOrderRefund2Knight RefundOrderParam:{} ", param);
        JdOrder query = new JdOrder();
        query.setParentId(param.getOrderId());
        List<JdOrder> chlidrens = jdOrderRepository.findOrderListByParentId(query);
        if (CollectionUtils.isEmpty(chlidrens)){
            RefundOrderParam refundOrderParam=JSON.parseObject(JSON.toJSONString(param),RefundOrderParam.class);
            xfylOrderRefund(refundOrderParam);
        }else {
            chlidrens.stream().forEach(jdOrder -> {
                RefundOrderParam refundOrderParam=new RefundOrderParam();
                refundOrderParam.setOrderId(jdOrder.getOrderId());
                refundOrderParam.setPromiseId(param.getPromiseId());
                refundOrderParam.setRefundType(Integer.valueOf(CommonConstant.ONE_STR));//整单退
                refundOrderParam.setOperator(param.getOperator());
                refundOrderParam.setRefundReason(param.getRefundReason());
                refundOrderParam.setRefundReasonCode(param.getRefundReasonCode());
                refundOrderParam.setRefundSource(CommonConstant.THREE_STR);
                xfylOrderRefund(refundOrderParam);
            });
        }
        return true;
    }

    /**
     * 查询自定义订单列表
     * @param param
     * @return
     */
    @Override
    public List<CustomOrderInfoDTO> queryCustomOrderList(QueryCustomOrderListParam param) {
        log.info("TradeApplicationImpl queryCustomOrderList param={}", JSON.toJSONString(param));
        // appoint
        List<CustomOrderParam> appointCustomOrderParamList = new ArrayList<>();
        // o2o
        List<CustomOrderParam> o2oCustomOrderParamList = new ArrayList<>();
        param.getCustomOrderParamList().forEach(customOrder->{
            // 订单业务标识
            Map<String, String> sendPayMap = StringUtils.isBlank(customOrder.getSendPayMap()) ? null : JSON.parseObject(customOrder.getSendPayMap(), new TypeReference< Map<String, String>>(){});
            String orderBizMark = verificationOrderType(Integer.valueOf(customOrder.getOrderType()), customOrder.getSendPay(), sendPayMap);
            if (CustomOrderStrategy.XFYL_SELF_PHYSICAL_ORDER.equals(orderBizMark)){
                appointCustomOrderParamList.add(customOrder);
                return;
            }
            o2oCustomOrderParamList.add(customOrder);
        });

        // appoint
        CompletableFuture<List<CustomOrderInfoDTO>> appointListFuture = CompletableFuture.supplyAsync(() -> {
            return completableAppointCustomOrder(appointCustomOrderParamList, param.getQueryCache(), param.getFreshCache());
        },executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_HAND_POOL));

        // o2o
        CompletableFuture<List<CustomOrderInfoDTO>> o2oListFuture = CompletableFuture.supplyAsync(() -> {
            return completableO20CustomOrder(o2oCustomOrderParamList, param.getQueryCache(), param.getFreshCache());
        },executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_HAND_POOL));

        // 返回结果
        List<CustomOrderInfoDTO> result = new ArrayList<>();

        List<CustomOrderInfoDTO> appointList = appointListFuture.join();
        log.info("TradeApplicationImpl queryCustomOrderList appointList={}", JSON.toJSONString(appointList));
        List<CustomOrderInfoDTO> o2oList = o2oListFuture.join();
        log.info("TradeApplicationImpl queryCustomOrderList o2oList={}", JSON.toJSONString(o2oList));
        if (CollectionUtils.isNotEmpty(appointList)){
            result.addAll(appointList);
        }
        if (CollectionUtils.isNotEmpty(o2oList)){
            result.addAll(o2oList);
        }
        log.info("TradeApplicationImpl queryCustomOrderList result={}", JSON.toJSONString(result));
        return result;
    }

    private List<CustomOrderInfoDTO> completableAppointCustomOrder(List<CustomOrderParam> appointCustomOrderParamList, Boolean queryCache, Boolean freshCache) {
        log.info("TradeApplicationImpl completableAppointCustomOrder appointCustomOrderParamList={}", JSON.toJSONString(appointCustomOrderParamList));
        if (CollectionUtils.isEmpty(appointCustomOrderParamList)){
            return Lists.newArrayList();
        }
        List<Long> orderIdList = appointCustomOrderParamList.stream().map(CustomOrderParam::getOrderId).collect(Collectors.toList());
        QueryAppointmentInfoParam param = new QueryAppointmentInfoParam();
        param.setOrderIdList(orderIdList);
        List<AppointmentInfoDTO> appointmentList = selfAppointRecordExportServiceRpc.queryAppointmentInfoByCondition(param);
        log.info("TradeApplicationImpl completableAppointCustomOrder appointmentList={}", JSON.toJSONString(appointmentList));
        if (CollectionUtils.isEmpty(appointmentList)){
            return Lists.newArrayList();
        }
        List<AppointmentInfoDetail> appointmentDetailList = JSON.parseArray(JSON.toJSONString(appointmentList), AppointmentInfoDetail.class);

        Map<Long, List<AppointmentInfoDetail>> appointmentDetailMap = appointmentDetailList.stream().collect(Collectors.groupingBy(AppointmentInfoDetail::getOrderId));

        CustomOrderHandlerContext context = CustomOrderHandlerContext.builder().orderIdMappingAppointmentInfoDetailMap(appointmentDetailMap).build();

        return completableAppointCustomOrderInfoData(context, appointCustomOrderParamList);
    }

    private List<CustomOrderInfoDTO> completableAppointCustomOrderInfoData(CustomOrderHandlerContext context, List<CustomOrderParam> customOrderParamList) {

        Map<Long, List<AppointmentInfoDetail>> orderIdMappingAppointmentInfoDetailMap = context.getOrderIdMappingAppointmentInfoDetailMap();
        // 异步处理
        List<CompletableFuture<CustomOrderInfoDTO>> futureList = Collections.synchronizedList(Lists.newArrayList());
        customOrderParamList.forEach(customOrder->{
            CompletableFuture<CustomOrderInfoDTO> future = CompletableFuture.supplyAsync(()->{
                try {
                    if (CollectionUtils.isEmpty(orderIdMappingAppointmentInfoDetailMap.get(customOrder.getOrderId()))){
                        return null;
                    }
                    CustomOrderHandlerContext handlerContext = JSON.parseObject(JSON.toJSONString(context), CustomOrderHandlerContext.class);
                    handlerContext.setOrderId(customOrder.getOrderId());

                    // 订单业务标识
                    Map<String, String> sendPayMap = StringUtils.isBlank(customOrder.getSendPayMap()) ? null : JSON.parseObject(customOrder.getSendPayMap(), new TypeReference< Map<String, String>>(){});
                    String orderBizMark = verificationOrderType(Integer.valueOf(customOrder.getOrderType()), customOrder.getSendPay(), sendPayMap);
                    if (StringUtils.isBlank(orderBizMark)){
                        log.info("TradeApplicationImpl completableAppointCustomOrderInfoData customOrder={}, orderBizMark={}", JSON.toJSONString(customOrder), orderBizMark);
                        return null;
                    }

                    // 自定义订单策略处理器
                    CustomOrderStrategy customOrderStrategy = CustomOrderApplicationContext.STRATEGY_MAP.get(orderBizMark);
                    return customOrderStrategy.handle(handlerContext);
                } catch (Exception e) {
                    log.error("TradeApplicationImpl completableAppointCustomOrderInfoData supplyAsync error e:", e);
                }
                return null;
            }, executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_HAND_POOL)).exceptionally(throwable -> {
                log.error("TradeApplicationImpl completableAppointCustomOrderInfoData completableFuture error:"+throwable.getMessage(),throwable);
                return null;
            });
            futureList.add(future);
        });

        List<CustomOrderInfoDTO> dataList = new ArrayList<>();
        futureList.forEach(future->{
            CustomOrderInfoDTO data = future.join();
            if (Objects.nonNull(data)){
                dataList.add(data);
            }
        });
        log.info("TradeApplicationImpl completableAppointCustomOrderInfoData dataList={} ", JSON.toJSONString(dataList));
        return dataList;
    }

    private List<CustomOrderInfoDTO> completableO20CustomOrder(List<CustomOrderParam> o2oCustomOrderParamList, Boolean queryCache, Boolean freshCache) {
        log.info("TradeApplicationImpl completableO20CustomOrder o2oCustomOrderParamList={}", JSON.toJSONString(o2oCustomOrderParamList));
        if (CollectionUtils.isEmpty(o2oCustomOrderParamList)){
            return Lists.newArrayList();
        }
        List<Long> orderIdList = o2oCustomOrderParamList.stream().map(CustomOrderParam::getOrderId).collect(Collectors.toList());

        // 进入订列异步刷新订详楼层，满足100ms需求
        CompletableFuture.runAsync(() -> asyncOrderDetailFloor(orderIdList), executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_DETAIL_HAND_POOL));

        Boolean jdAppOrderListFloorCacheSwitch = duccConfig.getJdAppOrderListFloorCacheSwitch();

        if (Boolean.TRUE.equals(queryCache) && Boolean.TRUE.equals(jdAppOrderListFloorCacheSwitch)) {
            log.info("TradeApplicationImpl completableO20CustomOrder get Cache before");
            List<String> cacheValues = jimClient.mGet(orderIdList.stream().map(s -> RedisKeyEnum.getRedisKey(RedisKeyEnum.JD_APP_ORDER_LIST_PROCESS_INFO_FLOOR, s.toString())).toArray(String[]::new));
            log.info("TradeApplicationImpl completableO20CustomOrder get Cache after");
            if (CollUtil.isNotEmpty(cacheValues)) {
                List<CustomOrderInfoDTO> cacheObjList = new ArrayList<>();
                List<Long> cacheOrderIdList = new ArrayList<>();
                for (String cacheVal : cacheValues) {
                    if (StringUtils.isBlank(cacheVal)) {
                        continue;
                    }
                    log.info("TradeApplicationImpl completableO20CustomOrder parseObject before");
                    CustomOrderInfoDTO customOrderInfoDTO = JSON.parseObject(cacheVal, CustomOrderInfoDTO.class);
                    log.info("TradeApplicationImpl completableO20CustomOrder parseObject after");
                    cacheObjList.add(customOrderInfoDTO);
                    cacheOrderIdList.add(customOrderInfoDTO.getOrderId());
                }
                List<Long> noCacheOrderIdList = new ArrayList<>(orderIdList);
                noCacheOrderIdList.removeAll(cacheOrderIdList);
                if (CollUtil.isNotEmpty(noCacheOrderIdList)) {
                    List<CustomOrderInfoDTO> ret = completableO20CustomOrder(o2oCustomOrderParamList, noCacheOrderIdList);
                    if (CollUtil.isNotEmpty(ret)) {
                        for (CustomOrderInfoDTO customOrderInfoDTO : ret) {
                            String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.JD_APP_ORDER_LIST_PROCESS_INFO_FLOOR, customOrderInfoDTO.getOrderId().toString());
                            jimClient.setEx(cacheKey, JSON.toJSONString(customOrderInfoDTO), RedisKeyEnum.JD_APP_ORDER_LIST_PROCESS_INFO_FLOOR.getExpireTime(), RedisKeyEnum.JD_APP_ORDER_LIST_PROCESS_INFO_FLOOR.getExpireTimeUnit());
                            log.info("TradeApplicationImpl queryCustomOrderListFloor queryNotExist set Cache orderId={}", customOrderInfoDTO.getOrderId());
                        }
                        cacheObjList.addAll(ret);
                    }
                }
                log.info("TradeApplicationImpl completableO20CustomOrder get Cache return");
                return cacheObjList;
            }
        }
        List<CustomOrderInfoDTO> ret = completableO20CustomOrder(o2oCustomOrderParamList, orderIdList);
        if (CollUtil.isNotEmpty(ret) && Boolean.TRUE.equals(jdAppOrderListFloorCacheSwitch)) {
            for (CustomOrderInfoDTO customOrderInfoDTO : ret) {
                String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.JD_APP_ORDER_LIST_PROCESS_INFO_FLOOR, customOrderInfoDTO.getOrderId().toString());
                jimClient.setEx(cacheKey, JSON.toJSONString(customOrderInfoDTO), RedisKeyEnum.JD_APP_ORDER_LIST_PROCESS_INFO_FLOOR.getExpireTime(), RedisKeyEnum.JD_APP_ORDER_LIST_PROCESS_INFO_FLOOR.getExpireTimeUnit());
                log.info("TradeApplicationImpl queryCustomOrderListFloor set Cache orderId={}", customOrderInfoDTO.getOrderId());
            }
        }
        return ret;
    }

    private List<CustomOrderInfoDTO> completableO20CustomOrder(List<CustomOrderParam> o2oCustomOrderParamList, List<Long> orderIdList) {
        // 查询订单
        CompletableFuture<List<JdOrder>> orderListFuture = CompletableFuture.supplyAsync(() -> {
            return jdOrderRepository.findOrdersByList(orderIdList);
        },executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_HAND_POOL));

        // 查询订单明细
        CompletableFuture<List<JdOrderItem>> orderItemListFuture = CompletableFuture.supplyAsync(() -> {
            return jdOrderItemRepository.listByOrderIdList(orderIdList);
        },executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_HAND_POOL));

        List<JdOrder> orderList = orderListFuture.join();
        List<JdOrderItem> orderItemList = orderItemListFuture.join();
        log.info("completableO20CustomOrder queryOrder orderIdList={}, orderList={}", JSON.toJSONString(orderIdList), JSON.toJSONString(orderList));
        if (CollectionUtils.isEmpty(orderList)){
            return Lists.newArrayList();
        }
        Map<Long, List<JdOrderItem>> orderIdMappingOrderItemMap = orderItemList.stream().collect(Collectors.groupingBy(JdOrderItem::getOrderId));
        log.info("completableO20CustomOrder build orderIdMappingOrderItemMap={}", JSON.toJSONString(orderIdMappingOrderItemMap));

        // orderId与sourceVoucherId映射关系 或 orderItemId与sourceVoucherId映射关系
        Map<Long, String> orderIdMappingSourceVoucherIdMap = new HashMap<>();
        orderList.forEach(order->{
            // LOC履约服务的POP订单
            if (OrderTypeEnum.COMMON_LOC_TYPE.getType().equals(order.getOrderType())){
                List<JdOrderItem> orderItems = orderIdMappingOrderItemMap.get(order.getOrderId());
                if (CollectionUtils.isEmpty(orderItems)){
                    return;
                }
                orderItems.forEach(orderItem->{
                    orderIdMappingSourceVoucherIdMap.put(orderItem.getOrderItemId(), String.valueOf(orderItem.getOrderItemId()));
                });
                return;
            }
            if (ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getVerticalCode().equalsIgnoreCase(order.getVerticalCode())) {
                orderIdMappingSourceVoucherIdMap.put(order.getOrderId(), order.getOrderId().toString());
                return;
            }
            // 判断是否为拆单场景
            String sourceVoucherId = Objects.nonNull(order.getParentId()) && order.getParentId() > 0 ? order.getParentId().toString() : order.getOrderId().toString();
            orderIdMappingSourceVoucherIdMap.put(order.getOrderId(),sourceVoucherId);
        });
        log.info("completableO20CustomOrder build orderIdMappingSourceVoucherIdMap={}", JSON.toJSONString(orderIdMappingSourceVoucherIdMap));

        // 查询履约单
        PromiseRepQuery promiseRep = new PromiseRepQuery();
        promiseRep.setSourceVoucherIdList(orderIdMappingSourceVoucherIdMap.entrySet().stream().map(Map.Entry::getValue).collect(Collectors.toList()));
        promiseRep.setQueryPatients(true);
        promiseRep.setQueryPromiseExtend(false);
        promiseRep.setQueryPromiseService(false);
        List<JdhPromise> promiseList = promiseRepository.findList(promiseRep);
        log.info("completableO20CustomOrder queryPromise promiseRep={}, promiseList={}", JSON.toJSONString(promiseRep), JSON.toJSONString(promiseList));
        // sourceVoucherId与履约单映射关系
        Map<String, List<JdhPromise>> sourceVoucherIdMappingPromiseMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(promiseList)){
            sourceVoucherIdMappingPromiseMap = promiseList.stream().collect(Collectors.groupingBy(JdhPromise::getSourceVoucherId));
        }
        log.info("completableO20CustomOrder build sourceVoucherIdMappingPromiseMap={}", JSON.toJSONString(sourceVoucherIdMappingPromiseMap));

        // promiseId与检测单映射关系
        Map<Long, List<MedicalPromiseDTO>> promiseIdMappingMedicalPromiseMap = new HashMap<>();
        // promiseId与服务者工单映射关系
        Map<Long, List<AngelWork>> promiseIdMappingAngelWorkMap = new HashMap<>();
        // promiseId与履约单历史映射关系
        Map<Long, List<JdhPromiseHistory>> promiseIdMappingPromiseHistoryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(promiseList)){
            // 履约单id
            List<Long> promiseIdList = promiseList.stream().map(JdhPromise::getPromiseId).collect(Collectors.toList());
            // 查询检查单
            CompletableFuture<List<MedicalPromiseDTO>> medicalPromiseListFuture = CompletableFuture.supplyAsync(() -> {
                MedicalPromiseListRequest req = new MedicalPromiseListRequest();
                req.setPromiseIdList(promiseIdList);
                req.setItemDetail(Boolean.TRUE);
                List<MedicalPromiseDTO> result = medicalPromiseApplication.queryMedicalPromiseList(req);
                log.info("completableO20CustomOrder queryMedicalPromise req={}, result={}", JSON.toJSONString(req), JSON.toJSONString(result));
                return result;
            },executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_HAND_POOL));

            // 查询服务者工单
            CompletableFuture<List<AngelWork>> angelWorkListFuture = CompletableFuture.supplyAsync(() -> {
                AngelWorkDBQuery req = new AngelWorkDBQuery();
                req.setPromiseIds(promiseIdList);
                req.setNotInWorkStatusList(Arrays.asList(AngelWorkStatusEnum.CANCEL.getType()));
                List<AngelWork> result = angelWorkRepository.findList(req);
                log.info("completableO20CustomOrder queryAngelWork req={}, result={}", JSON.toJSONString(req), JSON.toJSONString(result));
                return result;
            },executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_HAND_POOL));

            // 查询履约单历史
            CompletableFuture<List<JdhPromiseHistory>> promiseHistoryListFuture = CompletableFuture.supplyAsync(() -> {
                PromiseHistoryRepQuery req = PromiseHistoryRepQuery.builder()
                        .promiseIdList(promiseIdList)
                        .build();
                List<JdhPromiseHistory> result = promiseHistoryRepository.findList(req);
                log.info("completableO20CustomOrder queryPromiseHistory req={}, result={}", JSON.toJSONString(req), JSON.toJSONString(result));
                return result;
            },executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_HAND_POOL));

            // 检查单
            List<MedicalPromiseDTO> medicalPromiseList = medicalPromiseListFuture.join();
            if (CollectionUtils.isNotEmpty(medicalPromiseList)){
                promiseIdMappingMedicalPromiseMap =  medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromiseId));
            }
            log.info("completableO20CustomOrder build promiseIdMappingMedicalPromiseMap={}", JSON.toJSONString(promiseIdMappingMedicalPromiseMap));

            // 服务者工单
            List<AngelWork> angelWorkList = angelWorkListFuture.join();
            if (CollectionUtils.isNotEmpty(angelWorkList)){
                promiseIdMappingAngelWorkMap =  angelWorkList.stream().collect(Collectors.groupingBy(AngelWork::getPromiseId));
            }
            log.info("completableO20CustomOrder build promiseIdMappingAngelWorkMap={}", JSON.toJSONString(promiseIdMappingAngelWorkMap));

            // 履约单历史
            List<JdhPromiseHistory> promiseHistoryList = promiseHistoryListFuture.join();
            if (CollectionUtils.isNotEmpty(promiseHistoryList)){
                promiseIdMappingPromiseHistoryMap =  promiseHistoryList.stream().collect(Collectors.groupingBy(JdhPromiseHistory::getPromiseId));
            }
            log.info("completableO20CustomOrder build promiseIdMappingPromiseHistoryMap={}", JSON.toJSONString(promiseIdMappingPromiseHistoryMap));
        }

        CustomOrderHandlerContext context = CustomOrderHandlerContext.builder()
                .orderIdMappingOrderItemMap(orderIdMappingOrderItemMap)
                .orderIdMappingSourceVoucherIdMap(orderIdMappingSourceVoucherIdMap)
                .sourceVoucherIdMappingPromiseMap(sourceVoucherIdMappingPromiseMap)
                .promiseIdMappingMedicalPromiseMap(promiseIdMappingMedicalPromiseMap)
                .promiseIdMappingAngelWorkMap(promiseIdMappingAngelWorkMap)
                .promiseIdMappingPromiseHistoryMap(promiseIdMappingPromiseHistoryMap)
                .build();

        log.info("completableO20CustomOrder build context={}", JSON.toJSONString(context));
        return completableCustomOrderInfoData(orderList, context, o2oCustomOrderParamList);
    }

    private List<CustomOrderInfoDTO> completableCustomOrderInfoData(List<JdOrder> orderList, CustomOrderHandlerContext context, List<CustomOrderParam> customOrderParamList) {

        Map<Long, JdOrder> orderMap = orderList.stream().collect(Collectors.toMap(JdOrder::getOrderId, Function.identity()
                , (key1, key2) -> key2));

        // 异步处理
        List<CompletableFuture<CustomOrderInfoDTO>> futureList = Collections.synchronizedList(Lists.newArrayList());
        customOrderParamList.forEach(customOrder->{
            CompletableFuture<CustomOrderInfoDTO> future = CompletableFuture.supplyAsync(()->{
                try {
                    CustomOrderHandlerContext handlerContext = JSON.parseObject(JSON.toJSONString(context), CustomOrderHandlerContext.class);
                    JdOrder order = orderMap.get(customOrder.getOrderId());
                    if (Objects.isNull(order)){
                        return null;
                    }
                    handlerContext.setOrderStatusId(customOrder.getOrderStatusId());
                    handlerContext.setOrder(order);

                    Map<String, String> sendPayMap = new HashMap<>();
                    // 如果入参sendpaymap为空从订单表查询对象获取
                    if (StringUtils.isBlank(customOrder.getSendPayMap())) {
                        sendPayMap = order.getSendPayMap();
                    } else {
                        sendPayMap = StringUtils.isBlank(customOrder.getSendPayMap()) ? null : JSON.parseObject(customOrder.getSendPayMap(), new TypeReference< Map<String, String>>(){});
                    }

                    // 订单业务标识
                    String orderBizMark = verificationOrderType(Integer.valueOf(customOrder.getOrderType()), customOrder.getSendPay(), sendPayMap);
                    if (StringUtils.isBlank(orderBizMark)){
                        log.info("TradeApplicationImpl completableCustomOrderInfoData customOrder={}, orderBizMark={}", JSON.toJSONString(customOrder), orderBizMark);
                        return null;
                    }

                    // 自定义订单策略处理器
                    CustomOrderStrategy customOrderStrategy = CustomOrderApplicationContext.STRATEGY_MAP.get(orderBizMark);
                    return customOrderStrategy.handle(handlerContext);
                } catch (Exception e) {
                    log.error("TradeApplicationImpl completableCustomOrderInfoData supplyAsync error e:", e);
                }
                return null;
            }, executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_HAND_POOL)).exceptionally(throwable -> {
                log.error("TradeApplicationImpl completableCustomOrderInfoData completableFuture error:"+throwable.getMessage(),throwable);
                return null;
            });
            futureList.add(future);
        });

        List<CustomOrderInfoDTO> dataList = new ArrayList<>();
        futureList.forEach(future->{
            CustomOrderInfoDTO data = future.join();
            if (Objects.nonNull(data)){
                dataList.add(data);
            }
        });
        log.info("TradeApplicationImpl handleCustomOrderInfoData dataList={} ", JSON.toJSONString(dataList));
        return dataList;
    }

    /**
     * 校验订单类型
     * @param orderType
     * @param sendPay
     * @return
     */
    public static String verificationOrderType(Integer orderType, String sendPay, Map<String, String> sendPayMap) {
        try {
            if (StringUtils.isBlank(sendPay) && CollUtil.isEmpty(sendPayMap)){
                return "";
            }
            if (OrderTypeEnum.XFYL_ORDER_TYPE.getType().equals(orderType) && ((StringUtils.isNotBlank(sendPay) && sendPay.length() >= 294
                    && String.valueOf(sendPay.charAt(293)).equals("6")) || (CollUtil.isNotEmpty(sendPayMap) && sendPayMap.containsKey(SendpayValueEnum.SEND_PAY_294_6.getSendPayStr()) && sendPayMap.get(SendpayValueEnum.SEND_PAY_294_6.getSendPayStr()).equals(SendpayValueEnum.SEND_PAY_294_6.getSendPayValue())))){
                // 消费医疗到家订单
                return CustomOrderStrategy.XFYL_HOME_ORDER;
            } else if (OrderTypeEnum.COMMON_LOC_TYPE.getType().equals(orderType)){
                // 消费医疗LOC订单
                return CustomOrderStrategy.XFYL_LOC_ORDER;
            } else if (OrderTypeEnum.XFYL_VTP_VIRTUAL_ORDER_TYPE.getType().equals(orderType) && ((StringUtils.isNotBlank(sendPay) && sendPay.length() >= 354
                    && String.valueOf(sendPay.charAt(351)).equals("0") && String.valueOf(sendPay.charAt(352)).equals("9")
                    && String.valueOf(sendPay.charAt(353)).equals("9")) || (CollUtil.isNotEmpty(sendPayMap) && sendPayMap.containsKey(SendpayValueEnum.SEND_PAY_352_0.getSendPayStr()) && sendPayMap.get(SendpayValueEnum.SEND_PAY_352_0.getSendPayStr()).equals(SendpayValueEnum.SEND_PAY_352_0.getSendPayValue()) && sendPayMap.containsKey(SendpayValueEnum.SEND_PAY_353_9.getSendPayStr()) && sendPayMap.get(SendpayValueEnum.SEND_PAY_353_9.getSendPayStr()).equals(SendpayValueEnum.SEND_PAY_353_9.getSendPayValue()) && sendPayMap.containsKey(SendpayValueEnum.SEND_PAY_354_9.getSendPayStr()) && sendPayMap.get(SendpayValueEnum.SEND_PAY_354_9.getSendPayStr()).equals(SendpayValueEnum.SEND_PAY_354_9.getSendPayValue())))){
                // 消费医疗虚拟订单
                return CustomOrderStrategy.XFYL_VTP_VIRTUAL_ORDER;
            } else if (OrderTypeEnum.XFYL_ORDER_TYPE.getType().equals(orderType) && ((sendPay.length() >= 294
                    && String.valueOf(sendPay.charAt(293)).equals("4")) || (CollUtil.isNotEmpty(sendPayMap) && sendPayMap.containsKey(SendpayValueEnum.SEND_PAY_294_4.getSendPayStr()) && sendPayMap.get(SendpayValueEnum.SEND_PAY_294_4.getSendPayStr()).equals(SendpayValueEnum.SEND_PAY_294_4.getSendPayValue())))){
                // 消费医疗自营体检订单
                return CustomOrderStrategy.XFYL_SELF_PHYSICAL_ORDER;
            } else if (OrderTypeEnum.DEFAULT_ORDER_TYPE.getType().equals(orderType) && CollUtil.isNotEmpty(sendPayMap) && sendPayMap.containsKey(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()) && sendPayMap.get(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()).equals(SendpayValueEnum.SEND_PAY_1254_1.getSendPayValue())){
                // 消费医疗自营体检订单
                return CustomOrderStrategy.TRANSPORT_TEST_ORDER;
            }
        } catch (Exception e) {
            log.error("TradeApplicationImpl verificationOrderType error orderType={}, sendPay={}, e:", orderType, sendPay, e);
        }
        return "";
    }

    /**
     * 收单
     * @param request
     * @return
     */
    @Override
    public Integer receiveOrder(ReceiveOrderRequest request) {
        log.info("TradeApplicationImpl receiveOrder 收单 ReceiveOrderRequest:{}", JSONObject.toJSONString(request));
        JdOrder jdOrder = null;
        try {

            jdOrder = jdOrderRepository.find(new JdOrderIdentifier(request.getJdOrderNo()));
            if (Objects.nonNull(jdOrder)) {
                JdOrderSaveParam jdOrderSaveParam = new JdOrderSaveParam();
                jdOrderSaveParam.setOrderId(String.valueOf(request.getJdOrderNo()));
                jdOrderSaveParam.setUserPin(request.getUserPin());
                jdOrderSaveParam.setPartnerSource(PartnerSourceEnum.JDH_XFYL.getCode());
                JdOrder saveOrder = jdOrderApplication.saveJdOrder(jdOrderSaveParam);
                // 如果订单已经退款,或者申请退款中 不再创建服务单
                if (!jdOrder.isRefund()) {
                    syncCreateVoucher(saveOrder);
                }
                eventCoordinator.publish(EventFactory.newDefaultEvent(saveOrder, TradeEventTypeEnum.RECEIVE_ORDER, null));
                return ProduceStatusEnum.SUCCESS.getCode();
            }else{
                JdOrderSaveParam jdOrderSaveParam = new JdOrderSaveParam();
                jdOrderSaveParam.setOrderId(String.valueOf(request.getJdOrderNo()));
                jdOrderSaveParam.setUserPin(request.getUserPin());
                jdOrderSaveParam.setPartnerSource(PartnerSourceEnum.JDH_XFYL.getCode());
                jdOrder = jdOrderApplication.saveJdOrder(jdOrderSaveParam);
                log.info("TradeApplicationImpl receiveOrder 收单-订单落库结束 ReceiveOrderRequest:{}, jdOrder={}", JSONObject.toJSONString(request), JSONObject.toJSONString(jdOrder));
                // 同步产卡
                Boolean flag = syncCreateVoucher(jdOrder);
                eventCoordinator.publish(EventFactory.newDefaultEvent(jdOrder, TradeEventTypeEnum.RECEIVE_ORDER, null));
                log.info("TradeApplicationImpl receiveOrder 收单-产卡完成 ReceiveOrderRequest:{}, jdOrder={}， flag={}", JSONObject.toJSONString(request), JSONObject.toJSONString(jdOrder), flag);
            }
         } catch (BusinessException e) {
            log.error("TradeApplicationImpl receiveOrder 收单-订单落库及产卡异常: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("TradeApplicationImpl receiveOrder 收单-订单落库及产卡异常 ReceiveOrderRequest:{}, jdOrder={}", JSONObject.toJSONString(request), JSONObject.toJSONString(jdOrder));
            return ProduceStatusEnum.P_EXCEPTION.getCode();
        }
        return ProduceStatusEnum.SUCCESS.getCode();
    }


    /**
     * 订单拉完成
     * @param orderId
     * @return
     */
    @Override
    public Boolean reviseOrderFinishState(Long orderId) {
        if(Objects.nonNull(orderId)){
            JdOrder orderDetail = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderId).build());
            if(Objects.nonNull(orderDetail) && OrderTypeEnum.XFYL_VTP_VIRTUAL_ORDER_TYPE.getType().equals(orderDetail.getOrderType())){
                // VTP订单单独的RPC接口
                return vtpOrderInfoRpc.reviseOrderFinishState(orderId);
            }
        }
        return orderInfoRpc.reviseOrderFinishState(orderId);
    }

    /**
     * 意向护士列表
     * @param param
     * @return
     */
    @Override
    public List<ServiceHistoryAngelDTO> queryServiceHistoryAngel(QueryServiceHistoryAngelParam param) {
        log.info("TradeApplicationImpl queryServiceHistoryAngel param={}", JSON.toJSONString(param));
        // 获取地址信息
        AddressDetailBO addressDetail = getAddressDetail(param.getUserPin(), param.getAddressId());
        log.info("TradeApplicationImpl queryServiceHistoryAngel addressDetail={}", JSON.toJSONString(addressDetail));
        if (Objects.isNull(addressDetail)){
            return Lists.newArrayList();
        }

        // 获取主商品
        JdhSkuDto mainProductSku = getMainProductSku(param.getServiceIds());
        log.info("TradeApplicationImpl queryServiceHistoryAngel mainProductSku={}", JSON.toJSONString(mainProductSku));
        if (Objects.isNull(mainProductSku)){
            return Lists.newArrayList();
        }

        // 查询用户服务过的历史护士
        QueryLastAngelRequest queryLastAngelRequest = new QueryLastAngelRequest();
        BeanUtils.copyProperties(param,queryLastAngelRequest);
        queryLastAngelRequest.setServiceType(String.valueOf(mainProductSku.getServiceType()));
        queryLastAngelRequest.setBusinessMode(ServiceTypeNewEnum.getByType(mainProductSku.getServiceType()).getBusinessModeEnum().getCode());
        queryLastAngelRequest.setUserAddress(addressDetail.getFullAddress());
        log.info("TradeApplicationImpl queryServiceHistoryAngel queryLastAngelRequest={}", JSON.toJSONString(queryLastAngelRequest));
        List<LastCompleteAngelDto> lastCompleteAngelList = angelQueryApplication.listLastAngel(queryLastAngelRequest);
        log.info("queryServiceHistoryAngel queryLastAngelRequest={},lastCompleteAngelList={}", JSON.toJSONString(queryLastAngelRequest), JSON.toJSONString(lastCompleteAngelList));
        if (CollectionUtils.isEmpty(lastCompleteAngelList)){
            return Lists.newArrayList();
        }

        // 返回结果
        List<ServiceHistoryAngelDTO> serviceHistoryAngelDTOList = new ArrayList<>();
        // 护士默认头像
        JSONObject headImgConfigObj = JSON.parseObject(duccConfig.getNurseDefaultHeadImgConfig());
        lastCompleteAngelList.forEach(a->{
            ServiceHistoryAngelDTO serviceHistoryAngelDTO = new ServiceHistoryAngelDTO();
            serviceHistoryAngelDTO.setAngelId(a.getAngel().getAngelId());
            serviceHistoryAngelDTO.setHeadImg(headImgConfigObj.getString(String.valueOf(GenderEnum.WOMEN.getType())));
            if (GenderEnum.MAN.getType().equals(a.getAngel().getGender())){
                serviceHistoryAngelDTO.setHeadImg(headImgConfigObj.getString(String.valueOf(GenderEnum.MAN.getType())));
            }
            serviceHistoryAngelDTO.setLastServiceTime(a.getLastServiceTime());
            serviceHistoryAngelDTO.setLastServiceName(a.getLastServiceName());
            serviceHistoryAngelDTO.setAngelName(UserName.mask(a.getAngel().getAngelName()));
            serviceHistoryAngelDTOList.add(serviceHistoryAngelDTO);
        });
        return serviceHistoryAngelDTOList;
    }

    private AddressDetailBO getAddressDetail(String userPin, Long addressId){
        List<AddressDetailBO> addressList = jdhAddressRpc.queryAddressList(userPin);
        if (CollectionUtils.isEmpty(addressList)){
            return null;
        }
        addressList = addressList.stream().filter(a->a.getAddressId().equals(addressId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(addressList)){
            return null;
        }
        return addressList.get(0);
    }


    private JdhSkuDto getMainProductSku(List<Long> serviceIds) {
        try {
            JdhSkuListRequest skuListRequest = new JdhSkuListRequest();
            skuListRequest.setSkuIdList(new HashSet<>(serviceIds));
            Map<Long, JdhSkuDto> skuMap = productApplication.queryJdhSkuInfoList(skuListRequest);
            log.info("TradeApplicationImpl getMainProductSku skuListRequest={},skuMap={}", JSON.toJSONString(skuListRequest), JSON.toJSONString(skuMap));
            List<JdhSkuDto> skuDtoList = skuMap.values().stream().filter(s ->Arrays.asList(SkuRelTypeEnum.MAIN_ITEM.getType(),SkuRelTypeEnum.UPGRADE_ITEM.getType()).contains(s.getSkuType()))
                    .collect(Collectors.toList());
            return skuDtoList.get(0);
        } catch (Exception e) {
            log.error("TradeApplicationImpl getMainProductSku error e", e);
        }
        return null;
    }

    /**
     * 查询意向服务者
     * @param param
     * @return
     */
    @Override
    public IntendedAngelDTO queryIntendedAngel(QueryIntendedAngelParam param) {
        log.info("TradeApplicationImpl queryIntendedAngel param={}", JSON.toJSONString(param));
        // 获取地址信息
        AddressDetailBO addressDetail = null;
        if(StringUtils.isNotBlank(param.getFullAddress())){
            addressDetail = AddressDetailBO.builder().fullAddress(param.getFullAddress()).build();
        } else {
            addressDetail = getAddressDetail(param.getUserPin(), param.getAddressId());
        }
        log.info("TradeApplicationImpl queryIntendedAngel addressDetail={}", JSON.toJSONString(addressDetail));
        if (Objects.isNull(addressDetail)){
            return null;
        }

        // 获取主商品
        JdhSkuDto mainProductSku = getMainProductSku(param.getServiceIds());
        log.info("TradeApplicationImpl queryIntendedAngel mainProductSku={}", JSON.toJSONString(mainProductSku));
        if (Objects.isNull(mainProductSku)){
            return null;
        }

        // 查询意向服务者
        QueryIntendedAngelRequest queryIntendedAngelRequest = new QueryIntendedAngelRequest();
        BeanUtils.copyProperties(param, queryIntendedAngelRequest);
        queryIntendedAngelRequest.setServiceType(String.valueOf(mainProductSku.getServiceType()));
        queryIntendedAngelRequest.setBusinessMode(ServiceTypeNewEnum.getByType(mainProductSku.getServiceType()).getBusinessModeEnum().getCode());
        queryIntendedAngelRequest.setUserAddress(addressDetail.getFullAddress());
        log.info("TradeApplicationImpl queryIntendedAngel queryIntendedAngelRequest={}", JSON.toJSONString(queryIntendedAngelRequest));
        LastCompleteAngelDto lastCompleteAngelDto = angelQueryApplication.getIntendedAngel(queryIntendedAngelRequest);
        log.info("queryIntendedAngel queryIntendedAngelRequest={},lastCompleteAngelDto={}", JSON.toJSONString(queryIntendedAngelRequest)
                , JSON.toJSONString(lastCompleteAngelDto));
        if (Objects.isNull(lastCompleteAngelDto)){
            return null;
        }

        // 返回结果
        IntendedAngelDTO intendedAngelDTO = new IntendedAngelDTO();
        JdhAngelDto angel = lastCompleteAngelDto.getAngel();
        intendedAngelDTO.setAngelId(angel.getAngelId());
        // 护士默认头像
        JSONObject headImgConfigObj = JSON.parseObject(duccConfig.getNurseDefaultHeadImgConfig());
        intendedAngelDTO.setHeadImg(headImgConfigObj.getString(String.valueOf(GenderEnum.WOMEN.getType())));
        if (GenderEnum.MAN.getType().equals(angel.getGender())){
            intendedAngelDTO.setHeadImg(headImgConfigObj.getString(String.valueOf(GenderEnum.MAN.getType())));
        }
        intendedAngelDTO.setLastServiceTime(lastCompleteAngelDto.getLastServiceTime());
        intendedAngelDTO.setLastServiceName(lastCompleteAngelDto.getLastServiceName());
        intendedAngelDTO.setAngelName(UserName.mask(angel.getAngelName()));
        return intendedAngelDTO;
    }

    /**
     * 主站订单详情楼层
     *
     * @param param
     * @return
     */
    @Override
    public CustomOrderDetailFloorDTO queryCustomOrderDetailFloor(CustomOrderDetailParam param) {
        if (param == null || param.getOrderId() == null || param.getOrderType() == null || CollUtil.isEmpty(param.getSendPayMap())) {
            return null;
        }

        CompletableFuture<Boolean> accessOrderType = CompletableFuture.supplyAsync(() -> OrderTypeEnum.DEFAULT_ORDER_TYPE.getType().equals(param.getOrderType()),executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_DETAIL_HAND_POOL));

        CompletableFuture<Boolean> accessSendPayMap = CompletableFuture.supplyAsync(() -> CollUtil.isNotEmpty(param.getSendPayMap()) && param.getSendPayMap().containsKey(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()) && SendpayValueEnum.SEND_PAY_1254_1.getSendPayValue().equalsIgnoreCase(param.getSendPayMap().get(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr())),executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_DETAIL_HAND_POOL));

        Boolean jdAppOrderDetailFloorCacheSwitch = duccConfig.getJdAppOrderDetailFloorCacheSwitch();

        String orderIdStr = param.getOrderId().toString();
        CompletableFuture<CustomOrderDetailFloorDTO> getCacheObj = CompletableFuture.supplyAsync(() -> {
            if (Boolean.TRUE.equals(param.getQueryCache()) && Boolean.TRUE.equals(jdAppOrderDetailFloorCacheSwitch)) {
                String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.JD_APP_ORDER_DETAIL_PROCESS_INFO_FLOOR, orderIdStr);
                log.info("TradeApplicationImpl queryCustomOrderDetailFloor get Cache before");
                String cacheValue = jimClient.get(cacheKey);
                log.info("TradeApplicationImpl queryCustomOrderDetailFloor get Cache after");
                if (StringUtils.isNotBlank(cacheValue)) {
                    log.info("TradeApplicationImpl queryCustomOrderDetailFloor parseObject before");
                    CustomOrderDetailFloorDTO ret = JSON.parseObject(cacheValue, CustomOrderDetailFloorDTO.class);
                    log.info("TradeApplicationImpl queryCustomOrderDetailFloor parseObject after");
                    return ret;
                }
            }
            return null;
        }, executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_DETAIL_HAND_POOL));

        CompletableFuture.allOf(accessOrderType, accessSendPayMap, getCacheObj);
        try {
            if (accessOrderType.get() && accessSendPayMap.get()) {
                log.info("TradeApplicationImpl queryCustomOrderDetailFloor access");
                if (getCacheObj.get() != null) {
                    log.info("TradeApplicationImpl queryCustomOrderDetailFloor return Cache");
                    return getCacheObj.get();
                } else {
                    CustomOrderDetailFloorDTO customOrderDetailFloorDTO = queryCustomOrderDetailFloorFromDB(param);
                    if (Boolean.TRUE.equals(jdAppOrderDetailFloorCacheSwitch)) {
                        String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.JD_APP_ORDER_DETAIL_PROCESS_INFO_FLOOR, orderIdStr);
                        jimClient.setEx(cacheKey, JSON.toJSONString(customOrderDetailFloorDTO), RedisKeyEnum.JD_APP_ORDER_DETAIL_PROCESS_INFO_FLOOR.getExpireTime(), RedisKeyEnum.JD_APP_ORDER_DETAIL_PROCESS_INFO_FLOOR.getExpireTimeUnit());
                        log.info("TradeApplicationImpl queryCustomOrderDetailFloor set Cache orderId={}", orderIdStr);
                    }
                    return customOrderDetailFloorDTO;
                }
            }
        } catch (Exception e) {
            log.error("TradeApplicationImpl queryCustomOrderDetailFloor exception", e);
            return null;
        }
        return null;
    }

    /**
     * 数据库查询楼层信息
     * @return
     */
    private CustomOrderDetailFloorDTO queryCustomOrderDetailFloorFromDB(CustomOrderDetailParam param) {
        // 查询订单
        CompletableFuture<JdOrder> orderFuture = CompletableFuture.supplyAsync(() -> {
            JdOrder jdOrder = new JdOrder();
            jdOrder.setOrderId(param.getOrderId());
            jdOrder.setUserPin(param.getPin());
            return jdOrderRepository.findOrderDetail(jdOrder);
        },executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_DETAIL_HAND_POOL));

        // 查询服务单
        CompletableFuture<List<VoucherDto>> voucherFuture = CompletableFuture.supplyAsync(() -> {
            VoucherPageRequest request = VoucherPageRequest.builder()
                    .sourceVoucherId(String.valueOf(param.getOrderId())).build();
            return voucherApplication.queryVoucherList(request);
        },executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_DETAIL_HAND_POOL));

        // 查询履约单
        CompletableFuture<List<JdhPromise>> promiseFuture = CompletableFuture.supplyAsync(() -> {
            PromiseRepQuery promiseRep = new PromiseRepQuery();
            promiseRep.setSourceVoucherId(param.getOrderId().toString());
            promiseRep.setQueryPatients(false);
            promiseRep.setQueryPromiseExtend(false);
            promiseRep.setQueryPromiseService(false);
            return promiseRepository.findList(promiseRep);
        },executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_DETAIL_HAND_POOL));

        AtomicReference<JSONObject> config = new AtomicReference<>();
        CompletableFuture<List<CustomOrderDetailProgressBarDTO>> duccConfigFuture = CompletableFuture.supplyAsync(() -> {
            JSONObject customOrderInfoConfig = JSON.parseObject(duccConfig.getCustomOrderInfoConfig());
            // 服务流程配置
            JSONObject processTrackObj = customOrderInfoConfig.getJSONObject("processTrack");
            config.set(processTrackObj.getJSONObject("XFYLTransPortTestOrder"));
            return JSON.parseArray(JSON.toJSONString(config.get().getJSONArray("detailTopNodeList")), CustomOrderDetailProgressBarDTO.class);
        },executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_DETAIL_HAND_POOL));

        JdOrder order = orderFuture.join();
        List<VoucherDto> voucherList = voucherFuture.join();
        List<JdhPromise> promiseList = promiseFuture.join();
        List<CustomOrderDetailProgressBarDTO> customOrderDetailProgressBarDTOList = duccConfigFuture.join();

        CustomOrderDetailFloorDTO customOrderDetailFloorDTO = new CustomOrderDetailFloorDTO();
        // 订单不存在兜底为都完成状态
        if (order == null || CollUtil.isEmpty(voucherList)) {
            customOrderDetailFloorDTO.setServiceProgressBar(customOrderDetailProgressBarDTOList.stream().peek(s -> s.setIsDone(true)).collect(Collectors.toList()));
            log.info("TradeApplicationImpl queryCustomOrderDetailFloor order={} voucherList={}", JSON.toJSONString(order), JSON.toJSONString(voucherList));
            return customOrderDetailFloorDTO;
        }
        CompletableFuture<Integer> totalVoucherFuture = CompletableFuture.supplyAsync(() -> voucherList.stream().map(VoucherDto::getPromiseNum).filter(Objects::nonNull).reduce(0, Integer::sum),executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_DETAIL_HAND_POOL));

        // 查询服务单
        CompletableFuture<Integer> medicalCountFuture = CompletableFuture.supplyAsync(() -> medicalPromiseRepository.count(MedicalPromiseListQuery.builder().voucherId(voucherList.get(0).getVoucherId()).build()),executorPoolFactory.get(ThreadPoolConfigEnum.CUSTOM_ORDER_DETAIL_HAND_POOL));

        int usedNum = medicalCountFuture.join();
        int totalVoucher = totalVoucherFuture.join();

        List<CustomOrderDetailServiceInfoBo> customOrderDetailServiceInfoBoList;
        if (totalVoucher > 1) {
            customOrderDetailServiceInfoBoList = JSON.parseArray(config.get().getString("detailMulti"), CustomOrderDetailServiceInfoBo.class);
        } else {
            customOrderDetailServiceInfoBoList = JSON.parseArray(config.get().getString("detailSingle"), CustomOrderDetailServiceInfoBo.class);
        }
        if (CollUtil.isEmpty(customOrderDetailServiceInfoBoList)) {
            log.info("TradeApplicationImpl queryCustomOrderDetailFloor customOrderDetailServiceInfoBoList is empty");
            return customOrderDetailFloorDTO;
        }
        Map<String, Object> urlParams = new HashMap<>();
        List<Integer> promiseStatusList = new ArrayList<>();
        List<Integer> medPromiseStatusList = new ArrayList<>();
        String shipMsg = null;
        if (CollUtil.isNotEmpty(promiseList)) {
            List<JdhPromise> promiseCreateDsc = promiseList.stream().sorted(Comparator.comparing(JdhPromise::getCreateTime).reversed()).collect(Collectors.toList());
            JdhPromise lastPromise = promiseCreateDsc.get(0);

            promiseStatusList.add(lastPromise.getPromiseStatus());
            // 查询检查单
            MedicalPromiseListRequest req = new MedicalPromiseListRequest();
            req.setPromiseId(lastPromise.getPromiseId());
            List<MedicalPromiseDTO> result = medicalPromiseApplication.queryMedicalPromiseList(req);
            if (CollUtil.isNotEmpty(result)) {
                medPromiseStatusList.addAll(result.stream().map(MedicalPromiseDTO::getStatus).collect(Collectors.toList()));
            }
            if (totalVoucher > 1){
                // new JdhPromise() 避免空指针
                urlParams.put(PromiseAggregateEnum.PROMISE.getCode(), new JdhPromise());
            }else{
                if (CollUtil.isNotEmpty(promiseList)) {
                    urlParams.put(PromiseAggregateEnum.PROMISE.getCode(), promiseList.get(0));
                } else {
                    urlParams.put(PromiseAggregateEnum.PROMISE.getCode(), new JdhPromise());
                }
            }
            shipMsg = buildShipMsg(lastPromise, result);
        }
        int canUseNum = totalVoucher - usedNum;
        canUseNum = Math.max(canUseNum, 0);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderStatus", order.getOrderStatus());
        paramMap.put("totalServiceNum", totalVoucher);
        paramMap.put("canUseNum", canUseNum);
        paramMap.put("usedNum", usedNum);
        paramMap.put("promiseStatusList", promiseStatusList);
        paramMap.put("medPromiseStatusList", medPromiseStatusList);
        CustomOrderDetailServiceInfoDTO customOrderDetailServiceInfoDTO = null;
        customOrderDetailFloorDTO.setServiceProgressBar(customOrderDetailProgressBarDTOList.stream().peek(s -> s.setIsDone(false)).collect(Collectors.toList()));
        int selectNodeType = 0;
        List<Integer> isDoneNodeTpyeList = new ArrayList<>();
        urlParams.put(TradeAggregateEnum.ORDER.getCode(), order);
        urlParams.put(PromiseAggregateEnum.VOUCHER.getCode(), voucherList.get(0));
        for (CustomOrderDetailServiceInfoBo detailServiceInfoBo : customOrderDetailServiceInfoBoList) {
            log.info("TradeApplicationImpl queryCustomOrderDetailFloor detailServiceInfoBo={} paramMap={}", JSON.toJSONString(detailServiceInfoBo), JSON.toJSONString(paramMap));
            if ((Boolean) AviatorEvaluator.compile(detailServiceInfoBo.getStatusExpression(), Boolean.TRUE).execute(paramMap)) {
                customOrderDetailServiceInfoDTO = new CustomOrderDetailServiceInfoDTO();
                createCustomOrderDetailServiceInfoDTO(customOrderDetailServiceInfoDTO, detailServiceInfoBo, usedNum, totalVoucher, urlParams, shipMsg);
                Integer selectedNodeType = detailServiceInfoBo.getSelectNodeType();
                if (selectedNodeType != null) {
                    selectNodeType = selectedNodeType;
                }
                List<Integer> doneNodeTypeList = detailServiceInfoBo.getIsDoneNodeTypeList();
                if (CollUtil.isNotEmpty(doneNodeTypeList)) {
                    isDoneNodeTpyeList.addAll(doneNodeTypeList);
                }
                break;
            }
        }
        int finalSelectNodeType = selectNodeType;
        customOrderDetailFloorDTO.getServiceProgressBar().forEach(
                s -> {
                    if (s.getServiceNodeType().equals(finalSelectNodeType)) {
                        s.setSelected(true);
                    }
                    if (CollUtil.isNotEmpty(isDoneNodeTpyeList) && isDoneNodeTpyeList.contains(s.getServiceNodeType())) {
                        s.setIsDone(true);
                    }
                }
        );
        if (customOrderDetailServiceInfoDTO != null) {
            customOrderDetailFloorDTO.setServiceFloorInfo(customOrderDetailServiceInfoDTO);
        }
        return customOrderDetailFloorDTO;
    }

    /**
     * 物流信息
     * @param lastPromise
     * @return
     */
    private String buildShipMsg(JdhPromise lastPromise, List<MedicalPromiseDTO> result) {
        // 仅一个运力场景才展示具体运力信息
        if (CollUtil.isEmpty(result)) {
            return null;
        }
        List<Integer> medPromiseStatusList = result.stream().filter(s -> s.getPromiseId().equals(lastPromise.getPromiseId())).map(MedicalPromiseDTO::getStatus).collect(Collectors.toList());
        List<Integer> show = Lists.newArrayList(MedicalPromiseStatusEnum.COLLECTED.getStatus(), MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus());
        show.retainAll(medPromiseStatusList);
        log.info("TradeApplicationImpl buildShipMsg show={}", JSON.toJSONString(show));
        if (CollUtil.isNotEmpty(show)) {
            GetDetailByPromiseIdQuery getDetailByPromiseIdQuery = new GetDetailByPromiseIdQuery();
            getDetailByPromiseIdQuery.setPromiseId(lastPromise.getPromiseId());
            AngelShipDto angelShipDto = angelWorkApplication.getDetailByPromiseId(getDetailByPromiseIdQuery);
            if (angelShipDto != null && StringUtils.isNotBlank(angelShipDto.getLogisticsMessage())) {
                return angelShipDto.getLogisticsMessage();
            }
        }
        return null;
    }

    /**
     * 创建服务楼层dto对象
     */
    private void createCustomOrderDetailServiceInfoDTO(CustomOrderDetailServiceInfoDTO customOrderDetailServiceInfoDTO, CustomOrderDetailServiceInfoBo detailServiceInfoBo, Integer usedNum, Integer totalVoucher, Map<String, Object> urlParams, String shipMsg) {
        // 进度楼层
        customOrderDetailServiceInfoDTO.setProcessFloorTitle(StringUtils.isNotBlank(detailServiceInfoBo.getProcessFloorTitle()) ? String.format(detailServiceInfoBo.getProcessFloorTitle(), usedNum, totalVoucher) : null);
        customOrderDetailServiceInfoDTO.setProcessInfoIcon(StringUtils.isNotBlank(detailServiceInfoBo.getProcessInfoIcon()) ? detailServiceInfoBo.getProcessInfoIcon(): null);
        String processInfo = StringUtils.isNotBlank(shipMsg) ? shipMsg :detailServiceInfoBo.getProcessInfo();
        customOrderDetailServiceInfoDTO.setProcessInfo(StringUtils.isNotBlank(processInfo) ? String.format(processInfo, totalVoucher) : "");
        customOrderDetailServiceInfoDTO.setProcessButtonName(StringUtils.isNotBlank(detailServiceInfoBo.getProcessButtonName()) ? detailServiceInfoBo.getProcessButtonName() : null);
        customOrderDetailServiceInfoDTO.setProcessButtonUrl(StringUtils.isNotBlank(detailServiceInfoBo.getProcessButtonUrl()) ? UrlUtil.removeValueNullParams(EntityUtil.fillUrlByBean(detailServiceInfoBo.getProcessButtonUrl() , urlParams)) : null);
        customOrderDetailServiceInfoDTO.setProcessButtonStyleType(detailServiceInfoBo.getProcessButtonStyleType());
        customOrderDetailServiceInfoDTO.setProcessFloorUrl(StringUtils.isNotBlank(detailServiceInfoBo.getProcessFloorUrl()) ? UrlUtil.removeValueNullParams(EntityUtil.fillUrlByBean(detailServiceInfoBo.getProcessFloorUrl() , urlParams)) : null);
        customOrderDetailServiceInfoDTO.setProcessButtonSort(detailServiceInfoBo.getProcessButtonSort());
        // 提示楼层
        customOrderDetailServiceInfoDTO.setTipsIcon(StringUtils.isNotBlank(detailServiceInfoBo.getTipsIcon()) ? detailServiceInfoBo.getTipsIcon() : null);
        customOrderDetailServiceInfoDTO.setTipsInfo(StringUtils.isNotBlank(detailServiceInfoBo.getTipsInfo()) ? detailServiceInfoBo.getTipsInfo() : null);
        customOrderDetailServiceInfoDTO.setTipsButtonName(StringUtils.isNotBlank(detailServiceInfoBo.getTipsButtonName()) ? detailServiceInfoBo.getTipsButtonName() : null);
        customOrderDetailServiceInfoDTO.setTipsButtonUrl(StringUtils.isNotBlank(detailServiceInfoBo.getTipsButtonUrl()) ? UrlUtil.removeValueNullParams(EntityUtil.fillUrlByBean(detailServiceInfoBo.getTipsButtonUrl() , urlParams)) : null);
        customOrderDetailServiceInfoDTO.setTipsFloorUrl(StringUtils.isNotBlank(detailServiceInfoBo.getTipsFloorUrl()) ? UrlUtil.removeValueNullParams(EntityUtil.fillUrlByBean(detailServiceInfoBo.getTipsFloorUrl() , urlParams)) : null);
        customOrderDetailServiceInfoDTO.setProcessButtonOpType(detailServiceInfoBo.getProcessButtonOpType());
        customOrderDetailServiceInfoDTO.setTipsButtonOpType(detailServiceInfoBo.getTipsButtonOpType());
        customOrderDetailServiceInfoDTO.setTipsFloorOpType(detailServiceInfoBo.getTipsFloorOpType());
        customOrderDetailServiceInfoDTO.setTipsButtonSort(detailServiceInfoBo.getTipsButtonSort());
    }

    /**
     * 同步产卡
     * @param jdOrder
     * @return
     */
    private Boolean syncCreateVoucher(JdOrder jdOrder) {

        List<JdOrderItem> jdOrderItemList = jdOrderItemRepository.listByOrderId(jdOrder.getOrderId());
        List<OrderCompleteVoucherContext> voucherContextList = new ArrayList<>();
        for (JdOrderItem jdOrderItem : jdOrderItemList) {
            Integer skuNum = jdOrderItem.getSkuNum();
            for (int i = 0; i < skuNum; i++) {
                OrderCompleteVoucherContext voucherContext = JdOrderFactory.createVoucherContext(jdOrderItem, jdOrder);
                voucherContext.setSourceVoucherId(String.valueOf(jdOrder.getOrderId()));
                voucherContextList.add(voucherContext);
            }
        }
        //批量创建创建服务单
        List<CreateVoucherCmd> createVoucherCmds =
                TradeOrderConverter.INSTANCE.convertToCreateVoucherCmdList(voucherContextList);
        log.info("TradeApplicationImpl syncCreateVoucher 产卡信息 jdOrder={}， createVoucherCmds={}", JSONObject.toJSONString(jdOrder), JSONObject.toJSONString(createVoucherCmds));
        return voucherApplication.batchCreateVoucher(createVoucherCmds);
    }

    /**
     * 异步刷新订详楼层信息
     */
    private void asyncOrderDetailFloor(List<Long> orderIdList) {
        log.info("TradeApplicationImpl asyncOrderDetailFloor 异步执行异步刷新订详楼层信息 orderList={}", orderIdList);
        for (Long orderId : orderIdList) {
            JdOrder orderEntity = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(orderId).build());
            if (orderEntity == null) {
                return;
            }
            Map<String, String> sendPayMap = orderEntity.getSendPayMap();
            if (CollUtil.isEmpty(sendPayMap)) {
                OrderInfoQueryContext context = new OrderInfoQueryContext();
                context.setOrderId(String.valueOf(orderEntity.getOrderId()));
                context.setUserPin(orderEntity.getUserPin());
                JdOrder jdOrder = orderInfoRpc.queryOrderInfo(context);
                if (jdOrder != null) {
                    sendPayMap = jdOrder.getSendPayMap();
                }
            }
            CustomOrderDetailParam customOrderDetailParam = new CustomOrderDetailParam();
            customOrderDetailParam.setPin(orderEntity.getUserPin());
            customOrderDetailParam.setOrderId(orderId);
            customOrderDetailParam.setOrderType(orderEntity.getOrderType());
            customOrderDetailParam.setSendPayMap(sendPayMap);
            customOrderDetailParam.setSendPay(orderEntity.getSendPay());
            customOrderDetailParam.setOrderStatusId(orderEntity.getOrderStatus());
            customOrderDetailParam.setFreshCache(true);
            this.queryCustomOrderDetailFloor(customOrderDetailParam);
            log.info("TradeApplicationImpl asyncOrderDetailFloor 异步执行异步刷新订详楼层信息,已刷新 orderId={}", orderId);
        }
        log.info("TradeApplicationImpl asyncOrderDetailFloor 异步执行异步刷新订详楼层信息 end=");
    }
}
