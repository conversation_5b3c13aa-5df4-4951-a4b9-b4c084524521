package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @date 2025-07-16 14:00
 */

@Getter
@AllArgsConstructor
public enum MedicalPromiseSubStatusEnum {


    /**
     * 样本检查中
     */
    SAMPLE_CHECK(301,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), "样本检查中"),
    /**
     * 样本检查通过
     */
    SAMPLE_CHECK_PASS(302,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本检查通过"),
    /**
     * 样本检查-样本上报异常-重采样-确认中
     */
    SAMPLE_CHECK_REFUSE_RE_COLLECT_CONNECT(351,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-重采样-确认中"),
    /**
     * 样本检查中-样本上报异常-重采样-用户同意
     */
    SAMPLE_CHECK_REFUSE_RE_COLLECT_AGREE(352,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-重采样-用户同意"),
    /**
     * 样本检查中-样本上报异常-重采样-用户不同意
     */
    SAMPLE_CHECK_REFUSE_RE_COLLECT_REFUSE(353,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-重采样-用户不同意"),
    /**
     * 样本检查中-样本上报异常-让步检测-确认中
     */
    SAMPLE_CHECK_REFUSE_CHECK_CONNECT(354,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-让步检测-确认中"),
    /**
     * 样本检查中-样本上报异常-让步检测-用户同意
     */
    SAMPLE_CHECK_REFUSE_CHECK_AGREE(355,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-让步检测-用户同意"),
    /**
     * 样本检查中-样本上报异常-让步检测-用户不同意
     */
    SAMPLE_CHECK_REFUSE_CHECK_REFUSE(356,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-让步检测-用户不同意"),
    /**
     * 样本处理中
     */
    SAMPLE_DEAL(401,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本处理中"),

    /**
     * 样本处理中-异常-重采样-确认中
     */
    SAMPLE_DEAL_ERROR_RE_COLLECT_CONNECT(451,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-重采样-确认中"),
    /**
     * 样本处理中-异常-重采样-用户同意
     */
    SAMPLE_DEAL_ERROR_RE_COLLECT_AGREE(452,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-重采样-用户同意"),
    /**
     * 样本处理中-异常-重采样-用户不同意
     */
    SAMPLE_DEAL_ERROR_RE_COLLECT_REFUSE(453,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-重采样-用户不同意"),
    /**
     * 样本上机检测
     */
    SAMPLE_TEST(501,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"已上机检测"),
    /**
     * 样本上机检测完成
     */
    SAMPLE_TEST_FINISH(502,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"检测完成"),
    /**
     * 样本上机检测-异常-重检测
     */
    SAMPLE_TEST_ERROR_RE_TEST(551,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-重检测"),
    /**
     * 报告审核
     */
    REPORT_CHECK(701,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"报告审核中"),
    /**
     * 报告审核-通过
     */
    REPORT_CHECK_PASS(702,MedicalPromiseStatusEnum.COMPLETED.getStatus(),"报告审核成功"),
    /**
     * 报告审核-拒绝
     */
    REPORT_CHECK_ERROR(751,MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"报告审核失败")



    ;
    /**
     * 子状态值
     */
    private final Integer subStatus;

    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 描述该枚举子状态的详细信息。
     */
    private final String desc;

}
