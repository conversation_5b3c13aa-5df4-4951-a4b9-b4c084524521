package com.jdh.o2oservice.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * @Project : 履约单细分状态
 * <AUTHOR> maoxianglin1
 * @create 2025/7/14 下午6:20
 */
@AllArgsConstructor
@Getter
public enum MedicalPromiseSubdivisionStatusEnum {

    /**
     * 样本送检中
     */
    MATERIAL_INFO_UNDER_INSPECTION(1, MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(), Lists.newArrayList(), "样本送检中"),


    /**
     * 样本检查中
     */
    MATERIAL_INFO_INSPECTING(2, MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), Lists.newArrayList(301, 302), "样本检查中"),


    /**
     * 样本检查异常
     */
    MATERIAL_INFO_INSPECTING_EXCEPTION(3, MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), Lists.newArrayList(351, 352, 353, 354, 355, 356), "样本检查异常"),


    /**
     * 样本处理中
     */
    MATERIAL_INFO_PROCESSING(4, MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), Lists.newArrayList(401), "样本处理中"),


    /**
     * 上机检测中
     */
    MATERIAL_INFO_MACHINE_INSPECTION(5, MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), Lists.newArrayList(501, 502), "上机检测中"),


    /**
     * 结果审核中
     */
    MATERIAL_INFO_RESULT_REVIEW(6, MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), Lists.newArrayList(701), "结果审核中"),


    /**
     * 结果审核异常
     */
    MATERIAL_INFO_RESULT_REVIEW_EXCEPTION(7, MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), Lists.newArrayList(751), "结果审核异常"),


    /**
     * 已完成
     */
    MATERIAL_INFO_INSPECTION_COMPLETED(8, MedicalPromiseStatusEnum.COMPLETED.getStatus(), Lists.newArrayList(702), "检测完成"),

    ;


    /**
     * 状态code
     */
    private final Integer statusCode;

    /**
     * 检测单状态（履约节点状态）
     */
    private final Integer medicalPromiseStatus;

    /**
     * 细分节点状态
     */
    private final List<Integer> subdivisionStatusList;

    /**
     * 检测单状态描述
     */
    private final String statusDesc;

    /**
     * 找到特定的履约单细分状态
     *
     * @param medicalPromiseStatus 检测单状态
     * @param subdivisionStatus    细分节点状态
     * @return
     */
    public static MedicalPromiseSubdivisionStatusEnum getMedicalPromiseSubdivisionStatusEnumByCondition(Integer medicalPromiseStatus, Integer subdivisionStatus) {
        for (MedicalPromiseSubdivisionStatusEnum value : values()) {
            if (value.getMedicalPromiseStatus().equals(medicalPromiseStatus) && value.getSubdivisionStatusList().contains(subdivisionStatus)) {
                return value;
            }
        }
        return null;
    }
}
