spring:
  ## jmq
  # https://joyspace.jd.com/pages/W1Rw2L8996bu5FsPgUta
  # dev为测试站，可以本地直接链接 http://test.taishan.jd.com/jmq/application
  # 集群接入地址：https://joyspace.jd.com/pages/OCNOfPvDOzYzOzim8HBd
  jmq:
    enabled: true
    producers:
      reachStoreProducer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
      reachStoreMq2Producer:
        password: C4AE77FB
        app: jdho2oservice
        address: jmq-cluster.jd.local:80
        enabled: true
      angelSettleProducer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
      pdfToJpgProvider:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
    consumers:
      jdhReachStoreConsumer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
      jdhReachStoreMq2Consumer:
        password: C4AE77FB
        app: jdho2oservice
        address: jmq-cluster.jd.local:80
        enabled: true
      jdhNethpSyncMqConsumer:
        password: C4AE77FB
        app: jdho2oservice
        address: jmq-cluster.jd.local:80
        enabled: true
      jdhRefundMqConsumer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
      dtsConsumer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: false
      locCodeConsumer:
        password: C4AE77FB
        app: jdho2oservice
        address: jmq-cluster.jd.local:80
        enabled: true
      nethpRxConsumer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
  topics:
    jdhReachStoreConsumer:
      locCodeSendTopic: loc_code_sync
      xfylLocCodeSendForwardYfbTopic: xfyl_loc_code_sync_forward_yfb
      providerCallback: provider_callback_topic
      nethpTriageEvent: NethpTriageEvent
      xfylNethpTriageEventForwardYfbTopic: xfyl_NethpTriageEvent_forward_yfb
      nethpTriageDiagRecycleEvent: triage_diag_recycle_event
      xfylNethpTriageDiagRecycleEventForwardYfbTopic: xfyl_triage_diag_recycle_event_forward_yfb
      refundResult: physicalexammq_orbCancelResult
      delayMessageTopic: delay_task_topic
      o2oServiceReport: o2o_service_report
      o2oServiceOrderStatus: o2o_service_order_status
      jdOrderbinlakeTopic: jd_order_binlake
      jdOrderbinlakeVtpTransferTopic: jd_order_binlake_vtp_transfer
      jdhPromiseBinlakeTopic: jdh_promise_binlake
      jdhMedicalPromiseBinlakeTopic: jdh_medical_promise_binlake
      jdhAngelWorkBinlakeTopic: jdh_angel_work_binlake
      jdhAngelTaskBinlakeTopic: jdh_angel_task_binlake
      jdhAngelShipBinlakeTopic: jdh_angel_ship_binlake
      withdrawChangeEventEventTopic: withdrawChangeEvent
      withdrawChangeEventEventForwardYfbTopic: withdrawChangeEvent_yfb
      cancelPayTopic: physicalexammq_ODC_CANCEL_v2
      reachNoticeTopic: o2o_reach_notice
      jdGmsProductCategoryPropertyTopic: gms_product_category_property
      jdGmsProductCategoryPropertyTopicYF: xfyl_gms_product_category_property_yfb
      examinationSkuBinLakeTopic: examination_man_sku_binlake
      vtpafs: VTP_AFS_Result
      qcAlarmTopic: o2o_qc_alarm_topic
      yunDingPrivacyNotificationTopic: yunding-privacy-notification
      callRecordingRetrievalTopic: xfyl_call_recording_retrieval
      callBindReleaseTopic: xfyl_call_bind_release
    jdhReachStoreMq2Consumer:
      nethpDoctorAuditEventTopic: DoctorAuditEvent
      xfylNethpDoctorAuditEventForwardYfbTopic: xfyl_DoctorAuditEvent_forward_yfb
      nethpDoctorChangeEventTopic: DoctorInfoChangeEvent
      xfylNethpDoctorChangeEventForwardYfbTopic: xfyl_DoctorInfoChangeEvent_forward_yfb
      vtprefundResult: svp_refund_message
    order:
      pop-middleware-no-split: physicalexamination_0_276_v2
      pop-middleware-split: physicalexamination_0_275_v2
      self-middleware-no-split: physicalexammq_0_276_v2
      self-middleware-split: physicalexammq_0_275_v2
      middleware-pop-complete: physicalexammq_ODC_COMPLETE_v2
    event:
      consumer.topic: o2o_service_event_topic
      medicalPromiseTopic: jdh_o2o_service_promise_msg
      forward.topic: promise_event_forward_topic
      o2oCoreEventTopic: o2o_core_event_topic
    reach:
      task.topic: o2o_service_reach_task_topic
      jdApp.push.topic: msg_common_topic
      file.submit.topic: sound_submit_topic
    settlement:
      ebsTopic: healthcare_examin_order_ebs
    delay:
      event.topic: o2o_delay_task_topic
    dts:
      order: xfyl_appointment_order_info_binlake_o2o
      order-sku: examination_order_sku_info_binlake_o2o
      appointment: xfyl_appointment_info_binlake_o2o
      ship: xfyl_order_ship_info_binlake_o2o
    net:
      nethpRx: NETHP_RX_CREATE
    report:
      deal: xfyl_pdf_to_jpg
    jd:
      jdLogisticsTopic: bd_waybill_state_toc_xfyl