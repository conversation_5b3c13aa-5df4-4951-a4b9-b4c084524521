package com.jdh.o2oservice.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.WaveEntry;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.promise.service.PromiseTransformApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportResultApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderFullApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.report.cmd.MedicalReportIndicatorFlushToEsCmd;
import com.jdh.o2oservice.export.trade.cmd.JdOrderFullSaveCmd;
import com.jdh.o2oservice.export.trade.dto.JdOrderFullDTO;
import com.jdh.o2oservice.listener.util.DBFieldChangeEventUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 21:36
 * @Desc :
 */
@Slf4j
@Service("jdhReportIndicatorListener")
public class JdhReportIndicatorListener implements MessageListener {

    /**
     * ducc
     */
    @Resource
    private MedicalReportResultApplication medicalReportResultApplication;

    /**
     * 序列化器
     */
    private static final MessageDeserialize<Message> DESERIALIZE = new JMQMessageDeserialize();

    /**
     * onMessage
     *
     * @param messages 消息
     * @throws Exception Exception
     */
    @Override
    @JmqListener(id = "jdhReachStoreConsumer", topics = {"${topics.jdhReachStoreConsumer.jdhReportIndicatorBinlakeTopic}"})
    public void onMessage(List<Message> messages) throws Exception {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        //将从JMQ消费者客户端获取的Binlake消费数据进行解析
        List<EntryMessage> entryMessages = DESERIALIZE.deserialize(messages);
        for (EntryMessage entryMessage : entryMessages) {
            if (null == entryMessage.getRowChange()) {
                continue;
            }
            if (!entryMessage.getEntryType().equals(WaveEntry.EntryType.ROWDATA)) {
                continue;
            }
            List<WaveEntry.RowData> rowDatas = entryMessage.getRowChange().getRowDatasList();
            for (WaveEntry.RowData rowData : rowDatas) {
                try {
                    MedicalReportIndicatorFlushToEsCmd cmd = new MedicalReportIndicatorFlushToEsCmd();
                    for (WaveEntry.Column column : rowData.getAfterColumnsList()) {
                        if ("id".equalsIgnoreCase(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                            cmd.setId(Long.parseLong(column.getValue()));
                        }
                    }
                    medicalReportResultApplication.flushReportIndicatorsToEs(cmd);
                } catch (Exception e) {
                    log.error("JdhReportIndicatorListener -> onMessage , error", e);
                    throw e;
                }
            }
        }
    }
}

