package com.jdh.o2oservice.listener;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.WaveEntry;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.trade.service.JdOrderFullApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.OrderTypeEnum;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.trade.cmd.JdOrderFullSaveCmd;
import com.jdh.o2oservice.export.trade.dto.JdOrderFullDTO;
import com.jdh.o2oservice.export.trade.query.JdOrderIdParam;
import com.jdh.o2oservice.listener.util.DBFieldChangeEventUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 14:33
 * @Desc : 消费订单表（jd_order）binlake
 */
@Slf4j
@Service("jdOrderListener")
public class JdOrderListener  implements MessageListener {

    /**
     * jdOrderFullApplication
     */
    @Autowired
    private JdOrderFullApplication jdOrderFullApplication;

    /**
     * verticalBusinessRepository
     */
    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * ducc
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * dbFieldChangeEventUtil
     */
    @Resource
    DBFieldChangeEventUtil dbFieldChangeEventUtil;

    /**
     * 序列化器
     */
    private static final MessageDeserialize<Message> DESERIALIZE = new JMQMessageDeserialize();

    /**
     * 订单类型列表
     */
    private static final List<String> ORDER_TYPE_LIST = Arrays.asList(OrderTypeEnum.DEFAULT_ORDER_TYPE.getType().toString(),OrderTypeEnum.XFYL_ORDER_TYPE.getType().toString(),OrderTypeEnum.XFYL_VTP_VIRTUAL_ORDER_TYPE.getType().toString());

    /**
     * onMessage
     *
     * @param messages 消息
     * @throws Exception Exception
     */
    @Override
    @JmqListener(id= "jdhReachStoreConsumer", topics = {"${topics.jdhReachStoreConsumer.jdOrderbinlakeTopic}"})
    public void onMessage(List<Message> messages) throws Exception {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        //将从JMQ消费者客户端获取的Binlake消费数据进行解析
        List<EntryMessage> entryMessages = DESERIALIZE.deserialize(messages);
        String dbConfig = duccConfig.getDbFieldChangeEventConfig();
        for(EntryMessage entryMessage : entryMessages){
            if (null == entryMessage.getRowChange()) {
                continue;
            }
            if (!entryMessage.getEntryType().equals(WaveEntry.EntryType.ROWDATA)) {
                continue;
            }
            String tableName = entryMessage.getHeader().getTableName();
            List<WaveEntry.RowData> rowDatas = entryMessage.getRowChange().getRowDatasList();
            for (WaveEntry.RowData rowData : rowDatas) {
                try {
                    //组装参数
                    JdOrderFullSaveCmd cmd = convert2JdOrderFullSaveCmd(rowData);
                    log.info("JdOrderListener -> onMessage , cmd={}", JSON.toJSONString(cmd));
                    if(OrderStatusEnum.ORDER_WAIT_PAY.getStatus().toString().equals(cmd.getOrderStatus())
                            || OrderStatusEnum.ORDER_CANCEL.getStatus().toString().equals(cmd.getOrderStatus())
                            || OrderStatusEnum.ORDER_SPLIT.getStatus().toString().equals(cmd.getOrderStatus())){
                        log.info("未付款 和 已取消订单 父订单 不处理 cmd={}", JSON.toJSONString(cmd));
                        continue;
                    }

                    JdhVerticalBusiness verticalBusiness = verticalBusinessRepository.find(cmd.getVerticalCode());
                    log.info("JdOrderListener -> onMessage , verticalBusiness={}", JSON.toJSONString(verticalBusiness));
                    if(Objects.nonNull(verticalBusiness) &&
                            (BusinessModeEnum.SELF_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_CARE.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.SELF_TEST_TRANSPORT.getCode().equals(verticalBusiness.getBusinessModeCode()))){
                        //非200号订单跳过
//                        if(!OrderTypeEnum.XFYL_ORDER_TYPE.getType().toString().equals(cmd.getOrderType())){
//                            log.info("非200号订单不处理 cmd:{}", JSON.toJSONString(cmd));
//                            continue;
//                        }
                        //非200号、4号 订单跳过
                        if(!ORDER_TYPE_LIST.contains(cmd.getOrderType())){
                            continue;
                        }

                        // 表字段变更事件
                        dbFieldChangeEventUtil.dbFieldChange(dbConfig, tableName, rowData);

                        List<JdOrderFullDTO> jdOrderFullDTOList = jdOrderFullApplication.queryByOrderId(new JdOrderIdParam(cmd.getOrderId()));
                        log.info("JdOrderListener -> onMessage jdOrderFullDTOList:{}",JSON.toJSONString(jdOrderFullDTOList));
                        if(CollectionUtils.isEmpty(jdOrderFullDTOList)){
                            log.info("JdOrderListener -> onMessage , jdOrderFullDTOList isEmpty");
                            throw new RuntimeException("查不到数据，重试");
                        }
                        for(JdOrderFullDTO dto: jdOrderFullDTOList){
                            //如果是逻辑删除
                            if(StringUtils.isNotBlank(cmd.getYn()) && cmd.getYn().equals("0")){
                                jdOrderFullApplication.deleteById(Collections.singletonList(dto.getId()));
                                continue;
                            }
                            jdOrderFullApplication.reloadFullOrderInfo(Long.parseLong(dto.getPromiseId()));
                        }
                    }



                }catch (Exception e){
                    log.error("JdOrderListener -> onMessage error",e);
                    throw e;
                }
            }
        }
    }



    /**
     * 组装参数
     * @param rowData
     * @return
     */
    private JdOrderFullSaveCmd convert2JdOrderFullSaveCmd(WaveEntry.RowData rowData) {
        JdOrderFullSaveCmd resultDTO = new JdOrderFullSaveCmd();
        for (WaveEntry.Column column : rowData.getAfterColumnsList()) {
            if ("order_id".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                resultDTO.setOrderId(column.getValue());
            }else if ("yn".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                resultDTO.setYn(column.getValue());
            }else if ("order_type".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                resultDTO.setOrderType(column.getValue());
            }else if ("order_status".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                resultDTO.setOrderStatus(column.getValue());
            }else if ("service_type".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                resultDTO.setServiceType(column.getValue());
            }else if ("vertical_code".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                resultDTO.setVerticalCode(column.getValue());
            }
        }
        return resultDTO;
    }
}
