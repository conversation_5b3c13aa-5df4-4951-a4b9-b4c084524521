package com.jdh.o2oservice.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.listener.factory.HandlerFactory;
import com.jdh.o2oservice.listener.handler.AbstractHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName:DefaultMqListener
 * @Description: jmq监听类
 * @Author: yaoqinghai
 * @Date: 2023/12/22 17:35
 * @Vserion: 1.0
 **/
@Slf4j
@Component
public class DefaultMqListener {

    /**
     * handler工厂，根据topic获取相应的handler
     */
    @Autowired
    private HandlerFactory handlerFactory;

    @JmqListener(id = "jdhReachStoreConsumer",
            topics = {"${topics.order.self-middleware-no-split}",
                    "${topics.order.self-middleware-split}",
                    "${topics.order.pop-middleware-no-split}",
                    "${topics.order.pop-middleware-split}",
                    "${topics.jd.jdLogisticsTopic}",
                    "${topics.afs.afsTopic}"})
    public <T> void onMessage(List<Message> list) {
        if (CollectionUtil.isEmpty(list)) {
            log.error("[DefaultMqListener.onMessage],听到的消息没有内容!");
            return;
        }
        try {
            dealMqMessage(list);
        } catch (Exception e) {
            log.error("[DefaultMqListener->onMessage],jmq exception!", e);
            throw new SystemException(SystemErrorCode.JMQ_SEND_ERROR);
        }
    }

    /**
     * 提测和上线需要使用JMQ4,配置jdhReachStoreConsumer
     * @param list
     * @param <T>
     */
    @JmqListener(id = "jdhReachStoreConsumer",
            topics = {"${topics.order.middleware-pop-complete}"})
    public <T> void orderComplete(List<Message> list) {
        if (CollectionUtil.isEmpty(list)) {
            log.error("[DefaultMqListener.orderComplete],听到的消息没有内容!");
            return;
        }
        try {
            dealMqMessage(list);
        } catch (Exception e) {
            log.error("[DefaultMqListener->orderComplete],jmq exception!", e);
            throw new SystemException(SystemErrorCode.JMQ_SEND_ERROR);
        }
    }

    @JmqListener(id = "jdhReachStoreMq2Consumer",
            topics = {"${topics.order.self-middleware-no-split}",
                    "${topics.order.self-middleware-split}"})
    public <T> void onMessageMq2(List<Message> list) {
        if (CollectionUtil.isEmpty(list)) {
            log.error("[DefaultMqListener.onMessageMq2],听到的消息没有内容!");
            return;
        }
        try {
            dealMqMessage(list);
        } catch (Exception e) {
            log.error("[DefaultMqListener->onMessageMq2],jmq exception!", e);
            throw new SystemException(SystemErrorCode.JMQ_SEND_ERROR);
        }
    }


    @JmqListener(id = "locCodeConsumer",
            topics = {"${topics.jdhReachStoreConsumer.locCodeSendTopic}"})
    public <T> void locCodeListen(List<Message> list) {
        if (CollectionUtil.isEmpty(list)) {
            log.error("[DefaultMqListener.onMessageMq2],听到的消息没有内容!");
            return;
        }
        try {
            dealMqMessage(list);
        } catch (Exception e) {
            log.error("[DefaultMqListener->onMessageMq2],jmq exception!", e);
            throw new SystemException(SystemErrorCode.JMQ_SEND_ERROR);
        }
    }

    @JmqListener(id = "jdhReachStoreConsumer",
            topics = {"${topics.delay.event.topic}"})
    public <T> void onDelayMessage(List<Message> list) {
        if (CollectionUtil.isEmpty(list)) {
            log.error("[DefaultMqListener.onMessage],听到的消息没有内容!");
            return;
        }
        try {
            dealMqMessage(list);
        } catch (Exception e) {
            log.error("[DefaultMqListener->onMessage],jmq exception!", e);
            throw new SystemException(SystemErrorCode.JMQ_SEND_ERROR);
        }
    }

    /**
     * 质控消息处理
     * @param list
     * @param <T>
     */
    @JmqListener(id = "jdhReachStoreConsumer",
            topics = {"${topics.jdhReachStoreConsumer.qcAlarmTopic}"})
    public <T> void onMessageQcAlarm(List<Message> list) {
        if (CollectionUtil.isEmpty(list)) {
            log.error("[DefaultMqListener.onMessageQcAlarm],听到的消息没有内容!");
            return;
        }
        try {
            dealMqMessage(list);
        } catch (Exception e) {
            log.error("[DefaultMqListener->onMessageQcAlarm],jmq exception!", e);
            throw new SystemException(SystemErrorCode.JMQ_SEND_ERROR);
        }
    }

    /**
     * 处理订单消息
     *
     * @param list
     * @param <T>
     * @throws Exception
     */
    private <T> void dealMqMessage(List<Message> list) throws Exception {
        String className = null;
        for (Message message : list) {
            try {
                String msgText = message.getText();
                if (StringUtils.isBlank(msgText)) {
                    continue;
                }

                String topic = message.getTopic();
                if (handlerFactory == null) {
                    log.error("handlerFactory不存在，需要注入工厂，请检查主题是否配置了handler，topic:{}", topic);
                    throw new Exception("handlerFactory不存在，需要注入工厂，请检查主题是否配置了handler");
                }
                // 从handler工厂获取handler
                AbstractHandler<T> handler = handlerFactory.getHandler(message.getTopic());
                className = handler.getClass().getSimpleName();

                // 从消息中直接取字段过滤
                if (handler.preFilterOfDiscardMessage(message)) {
                    log.info("{}->onMessage|第一次过滤掉当前消息", className);
                    continue;
                }

                // 解析消息对象
                T t = handler.analysisMessage(message);
                // 根据消息对象的内容进行过滤
                if (handler.filterOfDiscardMessage(t)) {
                    log.info("{}->onMessage|过滤掉当前消息, businessId={}", className, message.getBusinessId());
                    continue;
                }
                log.info("dealMqMessage msgText={}", msgText);
                // 检查转投预发环境
                if (handler.transferToYf(message, t)) {
                    log.info("{}->onMessage|转投到预发, topic:{}, businessId={}", className, topic, message.getBusinessId());
                    continue;
                }
                // 不需要转投的，通过Handler处理
                handler.dealMessage(t);
            } catch (Exception e) {
                log.error("{}->onMessage|error,messageAttributes:{}, messageText:{}",
                        className, JSON.toJSONString(message.getAttributes()), message.getText(), e);
                throw e;
            }
        }
    }

    public static void main(String[] args) {
        String expression = "(seq.some(cart.theSkus, lambda(sku) -> (sku.cidFirst == 27156 || sku.cid == 31463) end) != nil)";
        String expression1 = "(seq.some(childOrders, lambda(s) -> (( s.orderType == 0) &&  (string.substring(s.sendPay,1253,1254) == '1')) end) != nil)";
        String expression2 = "(seq.some(cart.theSkus, lambda(sku) -> (sku.cidFirst == 27156 || sku.cid == 31463) end) != nil) || ((orderType == 0) &&  (seq.get(sendPayDict,'1253') != nil)";
        String expression3 = "((orderType == '200' || orderType == '75' || orderType == '22') && (order.splitType == 2 || order.splitType == 3) && (string.substring(order.sendPay,293,294) != '0') ) || (orderType == '224') || (orderType == '0' && (seq.some(cart.theSkus, lambda(sku) -> (sku.cid == 27445 || sku.cid == 27446 || sku.cid == 27447 || sku.cid == 27448 || sku.cid == 27449 || sku.cid == 27450) end) != nil))";

    }
}
