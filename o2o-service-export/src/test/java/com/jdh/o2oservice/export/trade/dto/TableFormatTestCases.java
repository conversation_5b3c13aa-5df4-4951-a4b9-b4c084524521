package com.jdh.o2oservice.export.trade.dto;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 表格形式测试用例 - 30个非标准时间测试用例的输入输出对照表
 */
public class TableFormatTestCases {

    private static final SimpleDateFormat fmt = new SimpleDateFormat("HH:mm");

    public static void main(String[] args) {
        System.out.println("=== 30个非标准时间测试用例输入输出对照表 ===\n");

        printTableHeader();

        for (int i = 1; i <= 30; i++) {
            executeAndPrintTestCase(i);
        }

        printTableFooter();
    }

    private static void printTableHeader() {
        System.out.println("┌────┬────────┬─────────────┬──────────┬──────┬──────┬─────────────────────────────────────────────────────────────────────────────────────┐");
        System.out.println("│用例│当前时间│  可预约范围  │每日时间  │提前  │立即+ │                                预期输出时间段                                        │");
        System.out.println("├────┼────────┼─────────────┼──────────┼──────┼──────┼─────────────────────────────────────────────────────────────────────────────────────┤");
    }

    private static void printTableFooter() {
        System.out.println("└────┴────────┴─────────────┴──────────┴──────┴──────┴─────────────────────────────────────────────────────────────────────────────────────┘");
        System.out.println("\n说明：");
        System.out.println("- 提前：advanceAppointTime (null=立即预约, 数字=提前分钟数)");
        System.out.println("- 立即+：immediatelyAddTime (立即预约增加的分钟数)");
        System.out.println("- 时间段格式：开始-结束(时长min,类型)");
        System.out.println("- 类型：立即=立即预约, 预约=常规预约");
    }

    private static void executeAndPrintTestCase(int caseNum) {
        AvaiableAppointmentTimeDTO dto = createTestCase(caseNum);
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();

        // 打印表格行
        System.out.printf("│%2d  │", caseNum);
        System.out.printf("%s│", fmt.format(dto.getAvaiableTimeBegin()));
        System.out.printf("%s-%s│",
            fmt.format(dto.getAvaiableTimeBegin()),
            fmt.format(dto.getAvaiableTimeEnd()));
        System.out.printf("%s-%s│",
            dto.getBeginTimeRange(),
            dto.getEndTimeRange());
        System.out.printf("%4s  │",
            dto.getAdvanceAppointTime() == null ? "null" : dto.getAdvanceAppointTime().toString());
        System.out.printf("%4s  │",
            dto.getImmediatelyAddTime().toString());

        // 输出时间段
        if (result.isEmpty()) {
            System.out.printf("%-84s│%n", "无时间段");
        } else {
            StringBuilder output = new StringBuilder();
            for (int i = 0; i < result.size(); i++) {
                AppointmentTimeRangeDTO slot = result.get(i);
                long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);

                if (i > 0) output.append(" ");
                output.append(String.format("%s-%s(%dmin,%s)",
                    fmt.format(slot.getBeginTime()),
                    fmt.format(slot.getEndTime()),
                    duration,
                    slot.getIsImmediately() ? "立即" : "预约"
                ));

                // 每行最多显示84个字符，超出则换行
                if (output.length() > 80 && i < result.size() - 1) {
                    System.out.printf("%-84s│%n", output.toString());
                    System.out.print("│    │        │             │          │      │      │");
                    output = new StringBuilder();
                }
            }
            System.out.printf("%-84s│%n", output.toString());
        }
    }

    private static AvaiableAppointmentTimeDTO createTestCase(int caseNum) {
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        Calendar now = Calendar.getInstance();
        Calendar end = Calendar.getInstance();

        switch (caseNum) {
            case 1: // 10:17立即预约
                now.set(2024, 0, 15, 10, 17, 0);
                end.set(2024, 0, 15, 14, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 2: // 10:23提前30分钟
                now.set(2024, 0, 15, 10, 23, 0);
                end.set(2024, 0, 15, 14, 0, 0);
                dto.setAdvanceAppointTime(30);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 3: // 10:37提前60分钟
                now.set(2024, 0, 15, 10, 37, 0);
                end.set(2024, 0, 15, 15, 0, 0);
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(90);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 4: // 10:43提前120分钟
                now.set(2024, 0, 15, 10, 43, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(120);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 5: // 10:07兼容性测试
                now.set(2024, 0, 15, 10, 7, 0);
                end.set(2024, 0, 15, 13, 0, 0);
                dto.setAdvanceAppointTime(0);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 6: // 11:13立即预约
                now.set(2024, 0, 15, 11, 13, 0);
                end.set(2024, 0, 15, 15, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 7: // 17:47接近下班
                now.set(2024, 0, 15, 17, 47, 0);
                end.set(2024, 0, 16, 10, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 8: // 16:13跨天提前预约
                now.set(2024, 0, 15, 16, 13, 0);
                end.set(2024, 0, 16, 12, 0, 0);
                dto.setAdvanceAppointTime(90);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 9: // 8:27早上开始
                now.set(2024, 0, 15, 8, 27, 0);
                end.set(2024, 0, 15, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(90);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 10: // 12:19中午时段
                now.set(2024, 0, 15, 12, 19, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(45);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 11: // 14:33下午时段
                now.set(2024, 0, 15, 14, 33, 0);
                end.set(2024, 0, 15, 17, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 12: // 15:41短时间范围
                now.set(2024, 0, 15, 15, 41, 0);
                end.set(2024, 0, 15, 17, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 13: // 10:29限制每日时间
                now.set(2024, 0, 15, 10, 29, 0);
                end.set(2024, 0, 15, 15, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("09:00");
                dto.setEndTimeRange("15:00");
                break;

            case 14: // 9:41窄时间窗口
                now.set(2024, 0, 15, 9, 41, 0);
                end.set(2024, 0, 15, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(15);
                dto.setBeginTimeRange("09:30");
                dto.setEndTimeRange("12:30");
                break;

            case 15: // 9:13大提前时间
                now.set(2024, 0, 15, 9, 13, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(240);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 16: // 20:17夜间服务
                now.set(2024, 0, 15, 20, 17, 0);
                end.set(2024, 0, 16, 2, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("18:00");
                dto.setEndTimeRange("23:59");
                break;

            case 17: // 10:21全天服务
                now.set(2024, 0, 15, 10, 21, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("00:00");
                dto.setEndTimeRange("23:59");
                break;

            case 18: // 10:39周末场景
                now.set(2024, 0, 20, 10, 39, 0);
                end.set(2024, 0, 20, 16, 0, 0);
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("09:00");
                dto.setEndTimeRange("17:00");
                break;

            case 19: // 16:47多天范围
                now.set(2024, 0, 15, 16, 47, 0);
                end.set(2024, 0, 16, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(90);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 20: // 7:51边界时间
                now.set(2024, 0, 15, 7, 51, 0);
                end.set(2024, 0, 15, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 21: // 10:09提前15分钟
                now.set(2024, 0, 15, 10, 9, 0);
                end.set(2024, 0, 15, 14, 0, 0);
                dto.setAdvanceAppointTime(15);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 22: // 10:31提前180分钟
                now.set(2024, 0, 15, 10, 31, 0);
                end.set(2024, 0, 15, 17, 0, 0);
                dto.setAdvanceAppointTime(180);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 23: // 10:49立即预约大时长
                now.set(2024, 0, 15, 10, 49, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(150);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 24: // 10:03午休时间
                now.set(2024, 0, 15, 10, 3, 0);
                end.set(2024, 0, 15, 14, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("12:00");
                break;

            case 25: // 13:57下午班
                now.set(2024, 0, 15, 13, 57, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("13:00");
                dto.setEndTimeRange("20:00");
                break;

            case 26: // 10:17非标准时间
                now.set(2024, 0, 15, 10, 17, 0);
                end.set(2024, 0, 15, 14, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(37);
                dto.setBeginTimeRange("08:30");
                dto.setEndTimeRange("17:30");
                break;

            case 27: // 22:13跨月测试
                now.set(2024, 0, 31, 22, 13, 0);
                end.set(2024, 1, 1, 10, 0, 0);
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 28: // 10:27零立即增加时间
                now.set(2024, 0, 15, 10, 27, 0);
                end.set(2024, 0, 15, 14, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(0);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 29: // 17:51极短预约窗口
                now.set(2024, 0, 15, 17, 51, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(10);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;

            case 30: // 23:37复杂跨天场景
                now.set(2024, 0, 15, 23, 37, 0);
                end.set(2024, 0, 16, 12, 0, 0);
                dto.setAdvanceAppointTime(30);
                dto.setImmediatelyAddTime(90);
                dto.setBeginTimeRange("06:00");
                dto.setEndTimeRange("23:59");
                break;
        }

        dto.setAvaiableTimeBegin(now.getTime());
        dto.setAvaiableTimeEnd(end.getTime());
        dto.setTimeWindow(30); // 固定30分钟

        return dto;
    }