package com.jdh.o2oservice.export.trade.dto;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 简约排版测试用例 - 固定timeWindow=30，输出30个用例
 */
public class CompactTestCases {
    
    private static final SimpleDateFormat fmt = new SimpleDateFormat("MM-dd HH:mm");
    
    public static void main(String[] args) {
        System.out.println("=== calculateAvailableTimeSlots 简约测试用例 (timeWindow=30) ===\n");
        
        for (int i = 1; i <= 30; i++) {
            executeTestCase(i);
        }
        
        System.out.println("=== 30个测试用例完成 ===");
    }
    
    private static void executeTestCase(int caseNum) {
        AvaiableAppointmentTimeDTO dto = createTestCase(caseNum);
        
        System.out.printf("【用例%02d】", caseNum);
        printInputs(dto);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.print(" → 输出: ");
        if (result.isEmpty()) {
            System.out.println("无时段");
        } else {
            System.out.printf("%d个时段 ", result.size());
            for (int i = 0; i < result.size(); i++) {
                AppointmentTimeRangeDTO slot = result.get(i);
                long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
                System.out.printf("[%s-%s %dmin %s] ", 
                    fmt.format(slot.getBeginTime()),
                    fmt.format(slot.getEndTime()).substring(6),
                    duration,
                    slot.getIsImmediately() ? "立即" : "预约"
                );
                if ((i + 1) % 3 == 0 && i < result.size() - 1) System.out.print("\n" + " ".repeat(50));
            }
        }
        System.out.println("\n");
    }
    
    private static void printInputs(AvaiableAppointmentTimeDTO dto) {
        System.out.printf(" 当前:%s 范围:%s~%s 每日:%s~%s 提前:%s 立即+:%s",
            fmt.format(new Date()),
            fmt.format(dto.getAvaiableTimeBegin()),
            fmt.format(dto.getAvaiableTimeEnd()),
            dto.getBeginTimeRange(),
            dto.getEndTimeRange(),
            dto.getAdvanceAppointTime(),
            dto.getImmediatelyAddTime()
        );
    }
    
    private static AvaiableAppointmentTimeDTO createTestCase(int caseNum) {
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        Calendar now = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        
        switch (caseNum) {
            case 1: // 基础立即预约
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 14, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 2: // 提前30分钟
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 14, 0, 0);
                dto.setAdvanceAppointTime(30);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 3: // 提前60分钟
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 15, 0, 0);
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(90);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 4: // 提前120分钟
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(120);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 5: // 兼容性测试 advanceAppointTime=0
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 13, 0, 0);
                dto.setAdvanceAppointTime(0);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 6: // 非整点开始时间
                now.set(2024, 0, 15, 10, 23, 0);
                end.set(2024, 0, 15, 13, 23, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 7: // 接近下班时间
                now.set(2024, 0, 15, 17, 30, 0);
                end.set(2024, 0, 16, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 8: // 跨天提前预约
                now.set(2024, 0, 15, 17, 0, 0);
                end.set(2024, 0, 16, 12, 0, 0);
                dto.setAdvanceAppointTime(90);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 9: // 早上开始
                now.set(2024, 0, 15, 8, 0, 0);
                end.set(2024, 0, 15, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(90);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 10: // 中午时段
                now.set(2024, 0, 15, 12, 0, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(45);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 11: // 下午时段
                now.set(2024, 0, 15, 14, 0, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 12: // 短时间范围
                now.set(2024, 0, 15, 15, 0, 0);
                end.set(2024, 0, 15, 16, 30, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 13: // 限制每日时间范围
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("09:00");
                dto.setEndTimeRange("15:00");
                break;
                
            case 14: // 窄时间窗口
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 13, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(15);
                dto.setBeginTimeRange("09:30");
                dto.setEndTimeRange("12:30");
                break;
                
            case 15: // 大提前时间
                now.set(2024, 0, 15, 9, 0, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(240);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 16: // 夜间服务
                now.set(2024, 0, 15, 20, 0, 0);
                end.set(2024, 0, 16, 8, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("18:00");
                dto.setEndTimeRange("23:59");
                break;
                
            case 17: // 全天服务
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("00:00");
                dto.setEndTimeRange("23:59");
                break;
                
            case 18: // 周末场景
                now.set(2024, 0, 20, 10, 0, 0); // 周六
                end.set(2024, 0, 20, 16, 0, 0);
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("09:00");
                dto.setEndTimeRange("17:00");
                break;
                
            case 19: // 多天范围
                now.set(2024, 0, 15, 16, 0, 0);
                end.set(2024, 0, 17, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(90);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 20: // 边界时间测试
                now.set(2024, 0, 15, 7, 59, 0);
                end.set(2024, 0, 15, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 21: // 提前15分钟
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 14, 0, 0);
                dto.setAdvanceAppointTime(15);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 22: // 提前180分钟
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(180);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 23: // 立即预约大时长
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(150);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 24: // 午休时间
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("12:00");
                break;
                
            case 25: // 下午班
                now.set(2024, 0, 15, 13, 0, 0);
                end.set(2024, 0, 15, 20, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("13:00");
                dto.setEndTimeRange("20:00");
                break;
                
            case 26: // 非标准时间
                now.set(2024, 0, 15, 10, 17, 0);
                end.set(2024, 0, 15, 14, 47, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(37);
                dto.setBeginTimeRange("08:30");
                dto.setEndTimeRange("17:30");
                break;
                
            case 27: // 跨月测试
                now.set(2024, 0, 31, 22, 0, 0);
                end.set(2024, 1, 1, 10, 0, 0);
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 28: // 零立即增加时间
                now.set(2024, 0, 15, 10, 0, 0);
                end.set(2024, 0, 15, 14, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(0);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 29: // 极短预约窗口
                now.set(2024, 0, 15, 17, 45, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(10);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 30: // 复杂跨天场景
                now.set(2024, 0, 15, 23, 30, 0);
                end.set(2024, 0, 17, 2, 0, 0);
                dto.setAdvanceAppointTime(30);
                dto.setImmediatelyAddTime(90);
                dto.setBeginTimeRange("06:00");
                dto.setEndTimeRange("23:59");
                break;
        }
        
        dto.setAvaiableTimeBegin(now.getTime());
        dto.setAvaiableTimeEnd(end.getTime());
        dto.setTimeWindow(30); // 固定30分钟
        
        return dto;
    }
}
