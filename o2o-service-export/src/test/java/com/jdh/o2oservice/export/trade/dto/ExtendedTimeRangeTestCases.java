package com.jdh.o2oservice.export.trade.dto;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 扩展时间范围测试用例 - 30个测试用例，时间跨度大于3天
 * 应用修正逻辑：结束时间以整点或整半点结束，不超过当天endTimeRange
 */
public class ExtendedTimeRangeTestCases {
    
    private static final SimpleDateFormat fullFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
    
    public static void main(String[] args) {
        System.out.println("=== 扩展时间范围测试用例 (跨度>3天) ===\n");
        
        printTableHeader();
        
        for (int i = 1; i <= 30; i++) {
            executeAndPrintTestCase(i);
        }
        
        printTableFooter();
    }
    
    private static void printTableHeader() {
        System.out.println("┌────┬─────────────────────┬─────────────────────┬─────────────────────┬─────────────┬──────┬──────┬─────────────────────────────────────────────────────────────────────────────────────┐");
        System.out.println("│用例│      当前时间       │      开始时间       │      结束时间       │ 每日时间范围│提前  │类型  │                                可约时段列表                                         │");
        System.out.println("│    │  yyyy-MM-dd HH:mm:ss│  yyyy-MM-dd HH:mm:ss│  yyyy-MM-dd HH:mm:ss│  HH:mm-HH:mm│(小时)│      │                                                                                      │");
        System.out.println("├────┼─────────────────────┼─────────────────────┼─────────────────────┼─────────────┼──────┼──────┼─────────────────────────────────────────────────────────────────────────────────────┤");
    }
    
    private static void printTableFooter() {
        System.out.println("└────┴─────────────────────┴─────────────────────┴─────────────────────┴─────────────┴──────┴──────┴─────────────────────────────────────────────────────────────────────────────────────┘");
        System.out.println("\n说明：");
        System.out.println("- 所有测试用例时间跨度都大于3天");
        System.out.println("- 结束时间已修正为整点或整半点，不超过当天endTimeRange");
        System.out.println("- 提前：null=立即预约, 1/2/3=提前1/2/3小时预约");
        System.out.println("- 类型：检测(60分钟增加), 护理(120分钟增加)");
        System.out.println("- ⚠标记表示结束时间未对齐到整点或半点");
    }
    
    private static void executeAndPrintTestCase(int caseNum) {
        TestCaseData testData = createTestCase(caseNum);
        AvaiableAppointmentTimeDTO dto = testData.dto;
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        // 计算时间跨度
        long timeSpanDays = (dto.getAvaiableTimeEnd().getTime() - dto.getAvaiableTimeBegin().getTime()) / (1000 * 60 * 60 * 24);
        
        // 打印表格行
        System.out.printf("│%2d  │", caseNum);
        System.out.printf("%s│", fullFormat.format(testData.currentTime));
        System.out.printf("%s│", fullFormat.format(dto.getAvaiableTimeBegin()));
        System.out.printf("%s│", fullFormat.format(dto.getAvaiableTimeEnd()));
        System.out.printf("%s-%s│", 
            dto.getBeginTimeRange(),
            dto.getEndTimeRange());
        System.out.printf("%4s  │", testData.advanceHours);
        System.out.printf("%4s  │", testData.serviceType);
        
        // 输出时间段（显示前6个和总数）
        if (result.isEmpty()) {
            System.out.printf("%-84s│%n", "无时间段");
        } else {
            StringBuilder output = new StringBuilder();
            output.append(String.format("共%d个时段(跨%d天) ", result.size(), timeSpanDays));
            
            // 显示前6个时间段
            for (int i = 0; i < Math.min(6, result.size()); i++) {
                AppointmentTimeRangeDTO slot = result.get(i);
                long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
                
                // 检查结束时间是否为整点或半点
                Calendar endCal = Calendar.getInstance();
                endCal.setTime(slot.getEndTime());
                int endMinute = endCal.get(Calendar.MINUTE);
                String alignMark = (endMinute == 0 || endMinute == 30) ? "" : "⚠";
                
                if (i > 0) output.append(" ");
                output.append(String.format("%s-%s(%dmin,%s)%s",
                    timeFormat.format(slot.getBeginTime()),
                    timeFormat.format(slot.getEndTime()),
                    duration,
                    slot.getIsImmediately() ? "立即" : "预约",
                    alignMark
                ));
                
                // 每行最多显示70个字符，超出则换行
                if (output.length() > 65 && i < Math.min(6, result.size()) - 1) {
                    System.out.printf("%-84s│%n", output.toString());
                    System.out.print("│    │                     │                     │                     │             │      │      │");
                    output = new StringBuilder();
                }
            }
            
            if (result.size() > 6) {
                output.append(" ...");
            }
            
            System.out.printf("%-84s│%n", output.toString());
        }
    }
    
    private static class TestCaseData {
        Date currentTime;
        AvaiableAppointmentTimeDTO dto;
        String advanceHours;
        String serviceType;
    }
    
    private static TestCaseData createTestCase(int caseNum) {
        TestCaseData testData = new TestCaseData();
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        Calendar now = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        
        switch (caseNum) {
            case 1: // 检测服务立即预约 - 4天跨度
                now.set(2024, 0, 15, 10, 17, 0);
                end.set(2024, 0, 19, 16, 0, 0); // 4天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;
                
            case 2: // 护理服务立即预约 - 5天跨度
                now.set(2024, 0, 15, 10, 23, 0);
                end.set(2024, 0, 20, 15, 0, 0); // 5天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;
                
            case 3: // 检测服务提前1小时 - 4天跨度
                now.set(2024, 0, 15, 10, 37, 0);
                end.set(2024, 0, 19, 15, 0, 0); // 4天跨度
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "1";
                testData.serviceType = "检测";
                break;
                
            case 4: // 护理服务提前1小时 - 6天跨度
                now.set(2024, 0, 15, 10, 43, 0);
                end.set(2024, 0, 21, 16, 0, 0); // 6天跨度
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "1";
                testData.serviceType = "护理";
                break;
                
            case 5: // 检测服务提前2小时 - 7天跨度
                now.set(2024, 0, 15, 10, 7, 0);
                end.set(2024, 0, 22, 16, 0, 0); // 7天跨度
                dto.setAdvanceAppointTime(120);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "2";
                testData.serviceType = "检测";
                break;

            case 6: // 护理服务提前2小时 - 5天跨度
                now.set(2024, 0, 15, 11, 13, 0);
                end.set(2024, 0, 20, 17, 0, 0); // 5天跨度
                dto.setAdvanceAppointTime(120);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "2";
                testData.serviceType = "护理";
                break;

            case 7: // 检测服务提前3小时 - 8天跨度
                now.set(2024, 0, 15, 9, 47, 0);
                end.set(2024, 0, 23, 18, 0, 0); // 8天跨度
                dto.setAdvanceAppointTime(180);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "3";
                testData.serviceType = "检测";
                break;

            case 8: // 护理服务提前3小时 - 6天跨度
                now.set(2024, 0, 15, 10, 13, 0);
                end.set(2024, 0, 21, 18, 0, 0); // 6天跨度
                dto.setAdvanceAppointTime(180);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "3";
                testData.serviceType = "护理";
                break;

            case 9: // 检测服务跨周末 - 9天跨度
                now.set(2024, 0, 15, 17, 27, 0); // 周一
                end.set(2024, 0, 24, 12, 0, 0); // 下周三，9天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 10: // 护理服务跨周末 - 10天跨度
                now.set(2024, 0, 15, 17, 19, 0); // 周一
                end.set(2024, 0, 25, 12, 0, 0); // 下周四，10天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 11: // 检测服务早上时段 - 4天跨度
                now.set(2024, 0, 15, 8, 33, 0);
                end.set(2024, 0, 19, 12, 0, 0); // 4天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 12: // 护理服务早上时段 - 5天跨度
                now.set(2024, 0, 15, 8, 41, 0);
                end.set(2024, 0, 20, 13, 0, 0); // 5天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 13: // 检测服务中午时段 - 6天跨度
                now.set(2024, 0, 15, 12, 29, 0);
                end.set(2024, 0, 21, 16, 0, 0); // 6天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 14: // 护理服务中午时段 - 7天跨度
                now.set(2024, 0, 15, 12, 11, 0);
                end.set(2024, 0, 22, 17, 0, 0); // 7天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 15: // 检测服务下午时段 - 4天跨度
                now.set(2024, 0, 15, 14, 13, 0);
                end.set(2024, 0, 19, 17, 0, 0); // 4天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 16: // 护理服务下午时段 - 8天跨度
                now.set(2024, 0, 15, 14, 57, 0);
                end.set(2024, 0, 23, 18, 0, 0); // 8天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 17: // 检测服务限制时间范围 - 5天跨度
                now.set(2024, 0, 15, 10, 21, 0);
                end.set(2024, 0, 20, 15, 0, 0); // 5天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("09:00");
                dto.setEndTimeRange("15:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 18: // 护理服务限制时间范围 - 6天跨度
                now.set(2024, 0, 15, 10, 39, 0);
                end.set(2024, 0, 21, 16, 0, 0); // 6天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("09:00");
                dto.setEndTimeRange("15:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 19: // 检测服务夜间时段 - 4天跨度
                now.set(2024, 0, 15, 20, 47, 0);
                end.set(2024, 0, 19, 2, 0, 0); // 4天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("18:00");
                dto.setEndTimeRange("23:59");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 20: // 护理服务夜间时段 - 7天跨度
                now.set(2024, 0, 15, 21, 13, 0);
                end.set(2024, 0, 22, 3, 0, 0); // 7天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("18:00");
                dto.setEndTimeRange("23:59");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 21: // 检测服务全天时段 - 5天跨度
                now.set(2024, 0, 15, 15, 51, 0);
                end.set(2024, 0, 20, 20, 0, 0); // 5天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("00:00");
                dto.setEndTimeRange("23:59");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 22: // 护理服务全天时段 - 6天跨度
                now.set(2024, 0, 15, 16, 27, 0);
                end.set(2024, 0, 21, 21, 0, 0); // 6天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("00:00");
                dto.setEndTimeRange("23:59");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 23: // 检测服务短时间窗口 - 4天跨度
                now.set(2024, 0, 15, 16, 41, 0);
                end.set(2024, 0, 19, 18, 0, 0); // 4天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 24: // 护理服务短时间窗口 - 7天跨度
                now.set(2024, 0, 15, 15, 3, 0);
                end.set(2024, 0, 22, 17, 0, 0); // 7天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 25: // 检测服务边界时间 - 8天跨度
                now.set(2024, 0, 15, 7, 57, 0);
                end.set(2024, 0, 23, 12, 0, 0); // 8天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 26: // 护理服务边界时间 - 9天跨度
                now.set(2024, 0, 15, 7, 31, 0);
                end.set(2024, 0, 24, 13, 0, 0); // 9天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 27: // 检测服务多天范围 - 10天跨度
                now.set(2024, 0, 15, 16, 17, 0);
                end.set(2024, 0, 25, 10, 0, 0); // 10天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 28: // 护理服务多天范围 - 11天跨度
                now.set(2024, 0, 15, 15, 47, 0);
                end.set(2024, 0, 26, 12, 0, 0); // 11天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 29: // 检测服务极短窗口 - 4天跨度
                now.set(2024, 0, 15, 17, 51, 0);
                end.set(2024, 0, 19, 18, 0, 0); // 4天跨度
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 30: // 护理服务跨月场景 - 12天跨度
                now.set(2024, 0, 25, 23, 37, 0);
                end.set(2024, 1, 6, 12, 0, 0); // 跨月12天跨度
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("06:00");
                dto.setEndTimeRange("23:59");
                testData.advanceHours = "1";
                testData.serviceType = "护理";
                break;
        }

        dto.setAvaiableTimeBegin(now.getTime());
        dto.setAvaiableTimeEnd(end.getTime());
        dto.setTimeWindow(30); // 固定30分钟

        testData.currentTime = now.getTime();
        testData.dto = dto;

        return testData;
    }
}
