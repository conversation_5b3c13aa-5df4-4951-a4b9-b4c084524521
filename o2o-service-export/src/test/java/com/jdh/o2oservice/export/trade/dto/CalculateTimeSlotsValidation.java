package com.jdh.o2oservice.export.trade.dto;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * calculateAvailableTimeSlots 方法验证
 * 通过具体的测试数据验证方法的正确性
 */
public class CalculateTimeSlotsValidation {
    
    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
    
    public static void main(String[] args) {
        System.out.println("=== calculateAvailableTimeSlots 方法验证 ===\n");
        
        // 验证测试1：基础立即预约功能
        validateBasicImmediateAppointment();
        
        // 验证测试2：时间窗口对齐
        validateTimeWindowAlignment();
        
        // 验证测试3：跨天处理
        validateCrossDayHandling();
        
        // 验证测试4：边界条件
        validateBoundaryConditions();
        
        System.out.println("=== 验证完成 ===");
    }
    
    /**
     * 验证测试1：基础立即预约功能
     */
    private static void validateBasicImmediateAppointment() {
        System.out.println("【验证测试1】基础立即预约功能");
        System.out.println("测试条件：");
        System.out.println("- 开始时间：2024-01-15 10:30:00");
        System.out.println("- 每日时间：09:00-18:00");
        System.out.println("- advanceAppointTime：null（支持立即预约）");
        System.out.println("- immediatelyAddTime：60分钟");
        System.out.println("- timeWindow：30分钟");
        System.out.println();
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        Calendar startCal = Calendar.getInstance();
        startCal.set(2024, Calendar.JANUARY, 15, 10, 30, 0);
        startCal.set(Calendar.MILLISECOND, 0);
        
        Calendar endCal = (Calendar) startCal.clone();
        endCal.add(Calendar.HOUR_OF_DAY, 4);
        
        dto.setAvaiableTimeBegin(startCal.getTime());
        dto.setAvaiableTimeEnd(endCal.getTime());
        dto.setBeginTimeRange("09:00");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(null);
        dto.setImmediatelyAddTime(60);
        dto.setTimeWindow(30);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("预期结果：");
        System.out.println("1. 第一个时间段应该是立即预约");
        System.out.println("2. 立即预约时长应该是90分钟（60+30）");
        System.out.println("3. 后续时间段应该是30分钟间隔的常规预约");
        System.out.println();
        
        System.out.println("实际结果：");
        if (!result.isEmpty()) {
            AppointmentTimeRangeDTO first = result.get(0);
            boolean isImmediate = first.getIsImmediately();
            long duration = (first.getEndTime().getTime() - first.getBeginTime().getTime()) / (1000 * 60);
            
            System.out.println("✓ 第一个时间段是立即预约：" + isImmediate);
            System.out.println("✓ 立即预约时长：" + duration + "分钟");
            System.out.println("✓ 开始时间：" + dateTimeFormat.format(first.getBeginTime()));
            System.out.println("✓ 结束时间：" + dateTimeFormat.format(first.getEndTime()));
            
            // 检查后续时间段
            if (result.size() > 1) {
                AppointmentTimeRangeDTO second = result.get(1);
                boolean secondIsImmediate = second.getIsImmediately();
                long secondDuration = (second.getEndTime().getTime() - second.getBeginTime().getTime()) / (1000 * 60);
                
                System.out.println("✓ 第二个时间段是常规预约：" + !secondIsImmediate);
                System.out.println("✓ 常规预约时长：" + secondDuration + "分钟");
            }
        }
        
        System.out.println("总计生成：" + result.size() + " 个时间段");
        System.out.println("验证结果：" + (validateImmediateAppointmentLogic(result) ? "✅ 通过" : "❌ 失败"));
        System.out.println();
    }
    
    /**
     * 验证测试2：时间窗口对齐
     */
    private static void validateTimeWindowAlignment() {
        System.out.println("【验证测试2】时间窗口对齐");
        System.out.println("测试条件：");
        System.out.println("- 开始时间：2024-01-15 10:37:00（非整点）");
        System.out.println("- timeWindow：15分钟");
        System.out.println("- 不支持立即预约");
        System.out.println();
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        Calendar startCal = Calendar.getInstance();
        startCal.set(2024, Calendar.JANUARY, 15, 10, 37, 0); // 非15分钟对齐时间
        startCal.set(Calendar.MILLISECOND, 0);
        
        Calendar endCal = (Calendar) startCal.clone();
        endCal.add(Calendar.HOUR_OF_DAY, 2);
        
        dto.setAvaiableTimeBegin(startCal.getTime());
        dto.setAvaiableTimeEnd(endCal.getTime());
        dto.setBeginTimeRange("09:00");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(60); // 不支持立即预约
        dto.setTimeWindow(15);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("预期结果：");
        System.out.println("1. 时间段应该对齐到15分钟边界（如10:45, 11:00, 11:15...）");
        System.out.println("2. 每个时间段时长15分钟");
        System.out.println();
        
        System.out.println("实际结果：");
        for (int i = 0; i < Math.min(5, result.size()); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            Calendar cal = Calendar.getInstance();
            cal.setTime(slot.getBeginTime());
            int minute = cal.get(Calendar.MINUTE);
            
            System.out.println((i + 1) + ". " + timeFormat.format(slot.getBeginTime()) + 
                             "-" + timeFormat.format(slot.getEndTime()) + 
                             " (分钟对齐：" + (minute % 15 == 0 ? "✓" : "✗") + ")");
        }
        
        System.out.println("验证结果：" + (validateTimeAlignment(result, 15) ? "✅ 通过" : "❌ 失败"));
        System.out.println();
    }
    
    /**
     * 验证测试3：跨天处理
     */
    private static void validateCrossDayHandling() {
        System.out.println("【验证测试3】跨天处理");
        System.out.println("测试条件：");
        System.out.println("- 开始时间：2024-01-15 17:30:00");
        System.out.println("- 结束时间：2024-01-17 12:00:00（跨2天）");
        System.out.println("- 每日时间：08:00-18:00");
        System.out.println();
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        Calendar startCal = Calendar.getInstance();
        startCal.set(2024, Calendar.JANUARY, 15, 17, 30, 0);
        startCal.set(Calendar.MILLISECOND, 0);
        
        Calendar endCal = Calendar.getInstance();
        endCal.set(2024, Calendar.JANUARY, 17, 12, 0, 0);
        endCal.set(Calendar.MILLISECOND, 0);
        
        dto.setAvaiableTimeBegin(startCal.getTime());
        dto.setAvaiableTimeEnd(endCal.getTime());
        dto.setBeginTimeRange("08:00");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(60);
        dto.setTimeWindow(60);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("预期结果：");
        System.out.println("1. 应该包含3天的时间段");
        System.out.println("2. 每天的时间段应该在08:00-18:00范围内");
        System.out.println("3. 不应该有夜间时间段");
        System.out.println();
        
        System.out.println("实际结果：");
        Calendar currentDay = Calendar.getInstance();
        currentDay.setTime(startCal.getTime());
        int dayCount = 1;
        int currentDaySlots = 0;
        
        for (AppointmentTimeRangeDTO slot : result) {
            Calendar slotCal = Calendar.getInstance();
            slotCal.setTime(slot.getBeginTime());
            
            if (slotCal.get(Calendar.DAY_OF_YEAR) != currentDay.get(Calendar.DAY_OF_YEAR)) {
                System.out.println("第" + dayCount + "天：" + currentDaySlots + " 个时间段");
                dayCount++;
                currentDaySlots = 0;
                currentDay.setTime(slot.getBeginTime());
            }
            currentDaySlots++;
        }
        System.out.println("第" + dayCount + "天：" + currentDaySlots + " 个时间段");
        
        System.out.println("验证结果：" + (validateCrossDayLogic(result) ? "✅ 通过" : "❌ 失败"));
        System.out.println();
    }
    
    /**
     * 验证测试4：边界条件
     */
    private static void validateBoundaryConditions() {
        System.out.println("【验证测试4】边界条件");
        
        // 测试无效参数
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        dto.setBeginTimeRange("invalid");
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("无效参数测试：" + (result.isEmpty() ? "✅ 通过" : "❌ 失败"));
        
        // 测试null参数
        dto = new AvaiableAppointmentTimeDTO();
        dto.setAvaiableTimeBegin(null);
        result = dto.calculateAvailableTimeSlots();
        
        System.out.println("null参数测试：" + (result.isEmpty() ? "✅ 通过" : "❌ 失败"));
        System.out.println();
    }
    
    // 辅助验证方法
    private static boolean validateImmediateAppointmentLogic(List<AppointmentTimeRangeDTO> result) {
        if (result.isEmpty()) return false;
        
        AppointmentTimeRangeDTO first = result.get(0);
        if (!first.getIsImmediately()) return false;
        
        long duration = (first.getEndTime().getTime() - first.getBeginTime().getTime()) / (1000 * 60);
        return duration == 90; // 60 + 30
    }
    
    private static boolean validateTimeAlignment(List<AppointmentTimeRangeDTO> result, int windowMinutes) {
        for (AppointmentTimeRangeDTO slot : result) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(slot.getBeginTime());
            if (cal.get(Calendar.MINUTE) % windowMinutes != 0) {
                return false;
            }
        }
        return true;
    }
    
    private static boolean validateCrossDayLogic(List<AppointmentTimeRangeDTO> result) {
        for (AppointmentTimeRangeDTO slot : result) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(slot.getBeginTime());
            int hour = cal.get(Calendar.HOUR_OF_DAY);
            
            // 检查是否在每日时间范围内（8:00-18:00）
            if (hour < 8 || hour >= 18) {
                return false;
            }
        }
        return true;
    }
}
