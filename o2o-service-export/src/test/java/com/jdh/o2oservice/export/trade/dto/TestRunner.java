package com.jdh.o2oservice.export.trade.dto;

/**
 * 测试运行器 - 用于快速验证 calculateAvailableTimeSlots 方法
 * 可以直接运行main方法查看测试结果
 */
public class TestRunner {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("AvaiableAppointmentTimeDTO 方法验证测试");
        System.out.println("========================================\n");
        
        AvaiableAppointmentTimeDTOTestData testData = new AvaiableAppointmentTimeDTOTestData();
        
        try {
            // 运行所有测试场景
            testData.runAllTestScenarios();
            
        } catch (Exception e) {
            System.err.println("测试执行出错：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
