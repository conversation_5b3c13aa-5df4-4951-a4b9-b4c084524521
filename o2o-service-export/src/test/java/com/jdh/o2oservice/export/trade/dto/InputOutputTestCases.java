package com.jdh.o2oservice.export.trade.dto;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * calculateAvailableTimeSlots 方法输入输出测试用例
 * 仅显示输入参数和输出结果，用于逻辑验证
 */
public class InputOutputTestCases {
    
    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    public static void main(String[] args) {
        System.out.println("========================================================");
        System.out.println("calculateAvailableTimeSlots 输入输出测试用例");
        System.out.println("========================================================\n");
        
        testCase1();
        testCase2();
        testCase3();
        testCase4();
        testCase5();
        testCase6();
        
        System.out.println("========================================================");
        System.out.println("测试用例执行完成");
        System.out.println("========================================================");
    }
    
    /**
     * 测试用例1：立即预约 - advanceAppointTime = null
     */
    private static void testCase1() {
        System.out.println("【测试用例1】立即预约 - advanceAppointTime = null");
        System.out.println("-".repeat(60));
        
        // 模拟当前时间：2024-01-15 10:30:00
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 10, 30, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.add(Calendar.HOUR_OF_DAY, 4);
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("08:00");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(null);
        dto.setImmediatelyAddTime(60);
        dto.setTimeWindow(30);
        
        System.out.println("📥 输入参数：");
        System.out.println("  当前时间: " + dateTimeFormat.format(currentTime.getTime()));
        System.out.println("  avaiableTimeBegin: " + dateTimeFormat.format(dto.getAvaiableTimeBegin()));
        System.out.println("  avaiableTimeEnd: " + dateTimeFormat.format(dto.getAvaiableTimeEnd()));
        System.out.println("  beginTimeRange: " + dto.getBeginTimeRange());
        System.out.println("  endTimeRange: " + dto.getEndTimeRange());
        System.out.println("  advanceAppointTime: " + dto.getAdvanceAppointTime());
        System.out.println("  immediatelyAddTime: " + dto.getImmediatelyAddTime());
        System.out.println("  timeWindow: " + dto.getTimeWindow());
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("\n📤 输出结果：");
        System.out.println("  总时间段数: " + result.size());
        for (int i = 0; i < Math.min(8, result.size()); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s - %s | 时长:%d分钟 | 立即预约:%s%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()),
                dateTimeFormat.format(slot.getEndTime()),
                duration,
                slot.getIsImmediately()
            );
        }
        System.out.println();
    }
    
    /**
     * 测试用例2：提前预约 - advanceAppointTime = 120
     */
    private static void testCase2() {
        System.out.println("【测试用例2】提前预约 - advanceAppointTime = 120");
        System.out.println("-".repeat(60));
        
        // 模拟当前时间：2024-01-15 14:00:00
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 14, 0, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.add(Calendar.DAY_OF_MONTH, 1);
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("09:00");
        dto.setEndTimeRange("17:00");
        dto.setAdvanceAppointTime(120);
        dto.setImmediatelyAddTime(90);
        dto.setTimeWindow(45);
        
        System.out.println("📥 输入参数：");
        System.out.println("  当前时间: " + dateTimeFormat.format(currentTime.getTime()));
        System.out.println("  avaiableTimeBegin: " + dateTimeFormat.format(dto.getAvaiableTimeBegin()));
        System.out.println("  avaiableTimeEnd: " + dateTimeFormat.format(dto.getAvaiableTimeEnd()));
        System.out.println("  beginTimeRange: " + dto.getBeginTimeRange());
        System.out.println("  endTimeRange: " + dto.getEndTimeRange());
        System.out.println("  advanceAppointTime: " + dto.getAdvanceAppointTime());
        System.out.println("  immediatelyAddTime: " + dto.getImmediatelyAddTime());
        System.out.println("  timeWindow: " + dto.getTimeWindow());
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("\n📤 输出结果：");
        System.out.println("  总时间段数: " + result.size());
        for (int i = 0; i < Math.min(6, result.size()); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s - %s | 时长:%d分钟 | 立即预约:%s%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()),
                dateTimeFormat.format(slot.getEndTime()),
                duration,
                slot.getIsImmediately()
            );
        }
        System.out.println();
    }
    
    /**
     * 测试用例3：兼容性测试 - advanceAppointTime = 0
     */
    private static void testCase3() {
        System.out.println("【测试用例3】兼容性测试 - advanceAppointTime = 0");
        System.out.println("-".repeat(60));
        
        // 模拟当前时间：2024-01-15 16:15:00
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 16, 15, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.add(Calendar.HOUR_OF_DAY, 6);
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("08:00");
        dto.setEndTimeRange("22:00");
        dto.setAdvanceAppointTime(0);
        dto.setImmediatelyAddTime(45);
        dto.setTimeWindow(15);
        
        System.out.println("📥 输入参数：");
        System.out.println("  当前时间: " + dateTimeFormat.format(currentTime.getTime()));
        System.out.println("  avaiableTimeBegin: " + dateTimeFormat.format(dto.getAvaiableTimeBegin()));
        System.out.println("  avaiableTimeEnd: " + dateTimeFormat.format(dto.getAvaiableTimeEnd()));
        System.out.println("  beginTimeRange: " + dto.getBeginTimeRange());
        System.out.println("  endTimeRange: " + dto.getEndTimeRange());
        System.out.println("  advanceAppointTime: " + dto.getAdvanceAppointTime());
        System.out.println("  immediatelyAddTime: " + dto.getImmediatelyAddTime());
        System.out.println("  timeWindow: " + dto.getTimeWindow());
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("\n📤 输出结果：");
        System.out.println("  总时间段数: " + result.size());
        for (int i = 0; i < Math.min(10, result.size()); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s - %s | 时长:%d分钟 | 立即预约:%s%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()),
                dateTimeFormat.format(slot.getEndTime()),
                duration,
                slot.getIsImmediately()
            );
        }
        System.out.println();
    }
    
    /**
     * 测试用例4：跨天场景 - advanceAppointTime = 30
     */
    private static void testCase4() {
        System.out.println("【测试用例4】跨天场景 - advanceAppointTime = 30");
        System.out.println("-".repeat(60));
        
        // 模拟当前时间：2024-01-15 17:45:00 (接近下班)
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 17, 45, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.add(Calendar.DAY_OF_MONTH, 1);
        endTime.set(Calendar.HOUR_OF_DAY, 12);
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("08:30");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(30);
        dto.setImmediatelyAddTime(60);
        dto.setTimeWindow(30);
        
        System.out.println("📥 输入参数：");
        System.out.println("  当前时间: " + dateTimeFormat.format(currentTime.getTime()));
        System.out.println("  avaiableTimeBegin: " + dateTimeFormat.format(dto.getAvaiableTimeBegin()));
        System.out.println("  avaiableTimeEnd: " + dateTimeFormat.format(dto.getAvaiableTimeEnd()));
        System.out.println("  beginTimeRange: " + dto.getBeginTimeRange());
        System.out.println("  endTimeRange: " + dto.getEndTimeRange());
        System.out.println("  advanceAppointTime: " + dto.getAdvanceAppointTime());
        System.out.println("  immediatelyAddTime: " + dto.getImmediatelyAddTime());
        System.out.println("  timeWindow: " + dto.getTimeWindow());
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("\n📤 输出结果：");
        System.out.println("  总时间段数: " + result.size());
        for (int i = 0; i < Math.min(8, result.size()); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s - %s | 时长:%d分钟 | 立即预约:%s%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()),
                dateTimeFormat.format(slot.getEndTime()),
                duration,
                slot.getIsImmediately()
            );
        }
        System.out.println();
    }
    
    /**
     * 测试用例5：时间窗口对齐 - advanceAppointTime = null, 非整点开始
     */
    private static void testCase5() {
        System.out.println("【测试用例5】时间窗口对齐 - advanceAppointTime = null, 非整点开始");
        System.out.println("-".repeat(60));
        
        // 模拟当前时间：2024-01-15 11:23:00 (非整点)
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 11, 23, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.add(Calendar.HOUR_OF_DAY, 3);
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("10:00");
        dto.setEndTimeRange("16:00");
        dto.setAdvanceAppointTime(null);
        dto.setImmediatelyAddTime(30);
        dto.setTimeWindow(20);
        
        System.out.println("📥 输入参数：");
        System.out.println("  当前时间: " + dateTimeFormat.format(currentTime.getTime()));
        System.out.println("  avaiableTimeBegin: " + dateTimeFormat.format(dto.getAvaiableTimeBegin()));
        System.out.println("  avaiableTimeEnd: " + dateTimeFormat.format(dto.getAvaiableTimeEnd()));
        System.out.println("  beginTimeRange: " + dto.getBeginTimeRange());
        System.out.println("  endTimeRange: " + dto.getEndTimeRange());
        System.out.println("  advanceAppointTime: " + dto.getAdvanceAppointTime());
        System.out.println("  immediatelyAddTime: " + dto.getImmediatelyAddTime());
        System.out.println("  timeWindow: " + dto.getTimeWindow());
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("\n📤 输出结果：");
        System.out.println("  总时间段数: " + result.size());
        for (int i = 0; i < Math.min(8, result.size()); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s - %s | 时长:%d分钟 | 立即预约:%s%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()),
                dateTimeFormat.format(slot.getEndTime()),
                duration,
                slot.getIsImmediately()
            );
        }
        System.out.println();
    }
    
    /**
     * 测试用例6：大提前时间 - advanceAppointTime = 240
     */
    private static void testCase6() {
        System.out.println("【测试用例6】大提前时间 - advanceAppointTime = 240");
        System.out.println("-".repeat(60));
        
        // 模拟当前时间：2024-01-15 09:00:00
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 9, 0, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.add(Calendar.DAY_OF_MONTH, 2);
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("08:00");
        dto.setEndTimeRange("20:00");
        dto.setAdvanceAppointTime(240); // 4小时提前
        dto.setImmediatelyAddTime(120);
        dto.setTimeWindow(60);
        
        System.out.println("📥 输入参数：");
        System.out.println("  当前时间: " + dateTimeFormat.format(currentTime.getTime()));
        System.out.println("  avaiableTimeBegin: " + dateTimeFormat.format(dto.getAvaiableTimeBegin()));
        System.out.println("  avaiableTimeEnd: " + dateTimeFormat.format(dto.getAvaiableTimeEnd()));
        System.out.println("  beginTimeRange: " + dto.getBeginTimeRange());
        System.out.println("  endTimeRange: " + dto.getEndTimeRange());
        System.out.println("  advanceAppointTime: " + dto.getAdvanceAppointTime());
        System.out.println("  immediatelyAddTime: " + dto.getImmediatelyAddTime());
        System.out.println("  timeWindow: " + dto.getTimeWindow());
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("\n📤 输出结果：");
        System.out.println("  总时间段数: " + result.size());
        for (int i = 0; i < Math.min(6, result.size()); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s - %s | 时长:%d分钟 | 立即预约:%s%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()),
                dateTimeFormat.format(slot.getEndTime()),
                duration,
                slot.getIsImmediately()
            );
        }
        System.out.println();
    }
}
