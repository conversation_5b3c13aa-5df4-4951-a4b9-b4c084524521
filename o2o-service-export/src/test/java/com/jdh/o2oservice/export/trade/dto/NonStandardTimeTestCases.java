package com.jdh.o2oservice.export.trade.dto;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 非整点非半点时间测试用例 - 固定timeWindow=30，输出30个用例
 */
public class NonStandardTimeTestCases {
    
    private static final SimpleDateFormat fmt = new SimpleDateFormat("MM-dd HH:mm");
    
    public static void main(String[] args) {
        System.out.println("=== 非整点非半点时间测试用例 (timeWindow=30) ===\n");
        
        for (int i = 1; i <= 30; i++) {
            executeTestCase(i);
        }
        
        System.out.println("=== 30个非标准时间测试用例完成 ===");
    }
    
    private static void executeTestCase(int caseNum) {
        AvaiableAppointmentTimeDTO dto = createTestCase(caseNum);
        
        System.out.printf("【用例%02d】", caseNum);
        printInputs(dto);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.print(" → 输出: ");
        if (result.isEmpty()) {
            System.out.println("无时段");
        } else {
            System.out.printf("%d个时段 ", result.size());
            for (int i = 0; i < result.size(); i++) {
                AppointmentTimeRangeDTO slot = result.get(i);
                long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
                System.out.printf("[%s-%s %dmin %s] ", 
                    fmt.format(slot.getBeginTime()),
                    fmt.format(slot.getEndTime()).substring(6),
                    duration,
                    slot.getIsImmediately() ? "立即" : "预约"
                );
                if ((i + 1) % 3 == 0 && i < result.size() - 1) System.out.print("\n" + " ".repeat(50));
            }
        }
        System.out.println("\n");
    }
    
    private static void printInputs(AvaiableAppointmentTimeDTO dto) {
        System.out.printf(" 当前:%s 范围:%s~%s 每日:%s~%s 提前:%s 立即+:%s",
            fmt.format(new Date()),
            fmt.format(dto.getAvaiableTimeBegin()),
            fmt.format(dto.getAvaiableTimeEnd()),
            dto.getBeginTimeRange(),
            dto.getEndTimeRange(),
            dto.getAdvanceAppointTime(),
            dto.getImmediatelyAddTime()
        );
    }
    
    private static AvaiableAppointmentTimeDTO createTestCase(int caseNum) {
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        Calendar now = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        
        switch (caseNum) {
            case 1: // 10:17开始立即预约
                now.set(2024, 0, 15, 10, 17, 0);
                end.set(2024, 0, 15, 14, 17, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 2: // 10:23提前30分钟
                now.set(2024, 0, 15, 10, 23, 0);
                end.set(2024, 0, 15, 14, 23, 0);
                dto.setAdvanceAppointTime(30);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 3: // 10:37提前60分钟
                now.set(2024, 0, 15, 10, 37, 0);
                end.set(2024, 0, 15, 15, 37, 0);
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(90);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 4: // 10:43提前120分钟
                now.set(2024, 0, 15, 10, 43, 0);
                end.set(2024, 0, 15, 16, 43, 0);
                dto.setAdvanceAppointTime(120);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 5: // 10:07兼容性测试
                now.set(2024, 0, 15, 10, 7, 0);
                end.set(2024, 0, 15, 13, 7, 0);
                dto.setAdvanceAppointTime(0);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 6: // 10:53立即预约
                now.set(2024, 0, 15, 10, 53, 0);
                end.set(2024, 0, 15, 13, 53, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 7: // 17:47接近下班
                now.set(2024, 0, 15, 17, 47, 0);
                end.set(2024, 0, 16, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 8: // 17:13跨天提前预约
                now.set(2024, 0, 15, 17, 13, 0);
                end.set(2024, 0, 16, 12, 0, 0);
                dto.setAdvanceAppointTime(90);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 9: // 8:27早上开始
                now.set(2024, 0, 15, 8, 27, 0);
                end.set(2024, 0, 15, 12, 27, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(90);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 10: // 12:19中午时段
                now.set(2024, 0, 15, 12, 19, 0);
                end.set(2024, 0, 15, 16, 19, 0);
                dto.setAdvanceAppointTime(45);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 11: // 14:33下午时段
                now.set(2024, 0, 15, 14, 33, 0);
                end.set(2024, 0, 15, 18, 33, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 12: // 15:41短时间范围
                now.set(2024, 0, 15, 15, 41, 0);
                end.set(2024, 0, 15, 17, 11, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 13: // 10:29限制每日时间
                now.set(2024, 0, 15, 10, 29, 0);
                end.set(2024, 0, 15, 16, 29, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("09:00");
                dto.setEndTimeRange("15:00");
                break;
                
            case 14: // 10:11窄时间窗口
                now.set(2024, 0, 15, 10, 11, 0);
                end.set(2024, 0, 15, 13, 11, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(15);
                dto.setBeginTimeRange("09:30");
                dto.setEndTimeRange("12:30");
                break;
                
            case 15: // 9:13大提前时间
                now.set(2024, 0, 15, 9, 13, 0);
                end.set(2024, 0, 15, 18, 13, 0);
                dto.setAdvanceAppointTime(240);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 16: // 20:17夜间服务
                now.set(2024, 0, 15, 20, 17, 0);
                end.set(2024, 0, 16, 8, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("18:00");
                dto.setEndTimeRange("23:59");
                break;
                
            case 17: // 10:21全天服务
                now.set(2024, 0, 15, 10, 21, 0);
                end.set(2024, 0, 15, 18, 21, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("00:00");
                dto.setEndTimeRange("23:59");
                break;
                
            case 18: // 10:39周末场景
                now.set(2024, 0, 20, 10, 39, 0); // 周六
                end.set(2024, 0, 20, 16, 39, 0);
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("09:00");
                dto.setEndTimeRange("17:00");
                break;
                
            case 19: // 16:47多天范围
                now.set(2024, 0, 15, 16, 47, 0);
                end.set(2024, 0, 17, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(90);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 20: // 7:51边界时间
                now.set(2024, 0, 15, 7, 51, 0);
                end.set(2024, 0, 15, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 21: // 10:09提前15分钟
                now.set(2024, 0, 15, 10, 9, 0);
                end.set(2024, 0, 15, 14, 9, 0);
                dto.setAdvanceAppointTime(15);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 22: // 10:31提前180分钟
                now.set(2024, 0, 15, 10, 31, 0);
                end.set(2024, 0, 15, 18, 31, 0);
                dto.setAdvanceAppointTime(180);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 23: // 10:49立即预约大时长
                now.set(2024, 0, 15, 10, 49, 0);
                end.set(2024, 0, 15, 16, 49, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(150);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 24: // 10:03午休时间
                now.set(2024, 0, 15, 10, 3, 0);
                end.set(2024, 0, 15, 16, 3, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("12:00");
                break;
                
            case 25: // 13:57下午班
                now.set(2024, 0, 15, 13, 57, 0);
                end.set(2024, 0, 15, 20, 57, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(45);
                dto.setBeginTimeRange("13:00");
                dto.setEndTimeRange("20:00");
                break;
                
            case 26: // 10:17非标准时间
                now.set(2024, 0, 15, 10, 17, 0);
                end.set(2024, 0, 15, 14, 47, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(37);
                dto.setBeginTimeRange("08:30");
                dto.setEndTimeRange("17:30");
                break;
                
            case 27: // 22:13跨月测试
                now.set(2024, 0, 31, 22, 13, 0);
                end.set(2024, 1, 1, 10, 0, 0);
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(30);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 28: // 10:27零立即增加时间
                now.set(2024, 0, 15, 10, 27, 0);
                end.set(2024, 0, 15, 14, 27, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(0);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 29: // 17:51极短预约窗口
                now.set(2024, 0, 15, 17, 51, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(10);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                break;
                
            case 30: // 23:37复杂跨天场景
                now.set(2024, 0, 15, 23, 37, 0);
                end.set(2024, 0, 17, 2, 0, 0);
                dto.setAdvanceAppointTime(30);
                dto.setImmediatelyAddTime(90);
                dto.setBeginTimeRange("06:00");
                dto.setEndTimeRange("23:59");
                break;
        }
        
        dto.setAvaiableTimeBegin(now.getTime());
        dto.setAvaiableTimeEnd(end.getTime());
        dto.setTimeWindow(30); // 固定30分钟
        
        return dto;
    }
}
