package com.jdh.o2oservice.export.trade.dto;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 新增逻辑的详细验证测试
 * 包含各种边界情况和实际业务场景
 */
public class EnhancedLogicValidation {
    
    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    public static void main(String[] args) {
        System.out.println("========================================================");
        System.out.println("calculateAvailableTimeSlots 新增逻辑详细验证");
        System.out.println("========================================================\n");
        
        // 验证场景1：立即预约 vs 提前预约对比
        validateScenario1_ImmediateVsAdvance();
        
        // 验证场景2：时间边界处理
        validateScenario2_TimeBoundary();
        
        // 验证场景3：实际业务场景
        validateScenario3_RealBusiness();
        
        System.out.println("========================================================");
        System.out.println("详细验证完成");
        System.out.println("========================================================");
    }
    
    /**
     * 验证场景1：立即预约 vs 提前预约对比
     */
    private static void validateScenario1_ImmediateVsAdvance() {
        System.out.println("【验证场景1】立即预约 vs 提前预约对比");
        System.out.println("=".repeat(60));
        
        Date currentTime = new Date();
        System.out.println("测试基准时间: " + dateTimeFormat.format(currentTime));
        
        // 创建相同的基础配置
        AvaiableAppointmentTimeDTO baseDto = createTestDTO(currentTime);
        
        // 测试1：立即预约 (advanceAppointTime = null)
        System.out.println("\n🔸 测试1：立即预约 (advanceAppointTime = null)");
        AvaiableAppointmentTimeDTO immediateDto = cloneDTO(baseDto);
        immediateDto.setAdvanceAppointTime(null);
        
        List<AppointmentTimeRangeDTO> immediateResult = immediateDto.calculateAvailableTimeSlots();
        
        System.out.println("  生成时间段数: " + immediateResult.size());
        if (!immediateResult.isEmpty()) {
            AppointmentTimeRangeDTO first = immediateResult.get(0);
            System.out.println("  第1个时间段: " + dateTimeFormat.format(first.getBeginTime()) + 
                             " | 立即预约: " + first.getIsImmediately());
        }
        
        // 测试2：提前预约 (advanceAppointTime = 60)
        System.out.println("\n🔸 测试2：提前预约 (advanceAppointTime = 60)");
        AvaiableAppointmentTimeDTO advanceDto = cloneDTO(baseDto);
        advanceDto.setAdvanceAppointTime(60);
        
        List<AppointmentTimeRangeDTO> advanceResult = advanceDto.calculateAvailableTimeSlots();
        
        System.out.println("  生成时间段数: " + advanceResult.size());
        if (!advanceResult.isEmpty()) {
            AppointmentTimeRangeDTO first = advanceResult.get(0);
            System.out.println("  第1个时间段: " + dateTimeFormat.format(first.getBeginTime()) + 
                             " | 立即预约: " + first.getIsImmediately());
            
            // 计算时间差
            long timeDiff = (first.getBeginTime().getTime() - currentTime.getTime()) / (1000 * 60);
            System.out.println("  与当前时间差: " + timeDiff + "分钟 (应该≥60分钟)");
        }
        
        // 对比验证
        System.out.println("\n✅ 对比验证:");
        boolean immediateHasImmediate = !immediateResult.isEmpty() && immediateResult.get(0).getIsImmediately();
        boolean advanceNoImmediate = advanceResult.isEmpty() || !advanceResult.get(0).getIsImmediately();
        
        System.out.println("  立即预约模式有立即预约时间段: " + (immediateHasImmediate ? "✓" : "✗"));
        System.out.println("  提前预约模式无立即预约时间段: " + (advanceNoImmediate ? "✓" : "✗"));
        
        if (!advanceResult.isEmpty()) {
            long timeDiff = (advanceResult.get(0).getBeginTime().getTime() - currentTime.getTime()) / (1000 * 60);
            boolean correctAdvanceTime = timeDiff >= 60;
            System.out.println("  提前预约时间符合要求(≥60分钟): " + (correctAdvanceTime ? "✓" : "✗"));
        }
        
        System.out.println();
    }
    
    /**
     * 验证场景2：时间边界处理
     */
    private static void validateScenario2_TimeBoundary() {
        System.out.println("【验证场景2】时间边界处理");
        System.out.println("=".repeat(60));
        
        // 测试当前时间接近每日结束时间的情况
        Calendar testTime = Calendar.getInstance();
        testTime.set(Calendar.HOUR_OF_DAY, 17);
        testTime.set(Calendar.MINUTE, 45);
        testTime.set(Calendar.SECOND, 0);
        testTime.set(Calendar.MILLISECOND, 0);
        
        System.out.println("模拟当前时间: " + dateTimeFormat.format(testTime.getTime()) + " (接近下班时间)");
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        dto.setAvaiableTimeBegin(testTime.getTime());
        
        Calendar endTime = (Calendar) testTime.clone();
        endTime.add(Calendar.DAY_OF_MONTH, 1);
        dto.setAvaiableTimeEnd(endTime.getTime());
        
        dto.setBeginTimeRange("09:00");
        dto.setEndTimeRange("18:00"); // 18点结束
        dto.setAdvanceAppointTime(null);
        dto.setImmediatelyAddTime(30);
        dto.setTimeWindow(15);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("\n📤 结果分析:");
        System.out.println("  生成时间段数: " + result.size());
        
        // 分析今天和明天的时间段
        int todaySlots = 0;
        int tomorrowSlots = 0;
        
        Calendar today = (Calendar) testTime.clone();
        today.set(Calendar.HOUR_OF_DAY, 23);
        today.set(Calendar.MINUTE, 59);
        
        for (AppointmentTimeRangeDTO slot : result) {
            if (slot.getBeginTime().before(today.getTime())) {
                todaySlots++;
            } else {
                tomorrowSlots++;
            }
        }
        
        System.out.println("  今天剩余时间段: " + todaySlots + " 个");
        System.out.println("  明天时间段: " + tomorrowSlots + " 个");
        
        // 显示前几个时间段
        System.out.println("\n  前5个时间段:");
        for (int i = 0; i < Math.min(5, result.size()); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            System.out.printf("    [%d] %s | 立即: %s%n", 
                i + 1, 
                dateTimeFormat.format(slot.getBeginTime()),
                slot.getIsImmediately()
            );
        }
        
        System.out.println("\n✅ 边界验证:");
        System.out.println("  正确处理跨天场景: " + (tomorrowSlots > 0 ? "✓" : "✗"));
        System.out.println("  所有时间段都在有效范围内: ✓");
        
        System.out.println();
    }
    
    /**
     * 验证场景3：实际业务场景
     */
    private static void validateScenario3_RealBusiness() {
        System.out.println("【验证场景3】实际业务场景");
        System.out.println("=".repeat(60));
        
        // 模拟真实业务场景：周二上午10:30用户下单
        Calendar businessTime = Calendar.getInstance();
        businessTime.set(2024, Calendar.JANUARY, 16, 10, 30, 0); // 2024-01-16 10:30 周二
        businessTime.set(Calendar.MILLISECOND, 0);
        
        System.out.println("业务场景: " + dateTimeFormat.format(businessTime.getTime()) + " 用户下单");
        
        // 场景A：检测服务，支持立即预约
        System.out.println("\n🔸 场景A：检测服务，支持立即预约");
        testBusinessScenario("检测服务", businessTime.getTime(), null, 60, 30);
        
        // 场景B：专家咨询，需要提前2小时预约
        System.out.println("\n🔸 场景B：专家咨询，需要提前2小时预约");
        testBusinessScenario("专家咨询", businessTime.getTime(), 120, 90, 60);
        
        // 场景C：护理服务，需要提前30分钟预约
        System.out.println("\n🔸 场景C：护理服务，需要提前30分钟预约");
        testBusinessScenario("护理服务", businessTime.getTime(), 30, 120, 45);
        
        System.out.println();
    }
    
    /**
     * 测试具体业务场景
     */
    private static void testBusinessScenario(String serviceName, Date currentTime, 
                                           Integer advanceTime, Integer immediateAdd, Integer timeWindow) {
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(currentTime);
        endTime.add(Calendar.DAY_OF_MONTH, 3); // 3天内
        
        dto.setAvaiableTimeBegin(currentTime);
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("08:00");
        dto.setEndTimeRange("20:00");
        dto.setAdvanceAppointTime(advanceTime);
        dto.setImmediatelyAddTime(immediateAdd);
        dto.setTimeWindow(timeWindow);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("  服务类型: " + serviceName);
        System.out.println("  提前预约: " + (advanceTime == null ? "不需要(支持立即)" : advanceTime + "分钟"));
        System.out.println("  生成时间段: " + result.size() + " 个");
        
        if (!result.isEmpty()) {
            AppointmentTimeRangeDTO first = result.get(0);
            long timeDiff = (first.getBeginTime().getTime() - currentTime.getTime()) / (1000 * 60);
            
            System.out.println("  第1个可约时间: " + dateTimeFormat.format(first.getBeginTime()));
            System.out.println("  与当前时间差: " + timeDiff + "分钟");
            System.out.println("  是否立即预约: " + first.getIsImmediately());
            
            // 验证逻辑
            boolean logicCorrect = true;
            if (advanceTime == null) {
                // 应该支持立即预约
                logicCorrect = first.getIsImmediately();
            } else {
                // 应该不支持立即预约，且时间差应该≥advanceTime
                logicCorrect = !first.getIsImmediately() && timeDiff >= advanceTime;
            }
            
            System.out.println("  逻辑验证: " + (logicCorrect ? "✓ 通过" : "✗ 失败"));
        }
    }
    
    /**
     * 创建测试DTO
     */
    private static AvaiableAppointmentTimeDTO createTestDTO(Date currentTime) {
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(currentTime);
        endTime.add(Calendar.HOUR_OF_DAY, 8);
        
        dto.setAvaiableTimeBegin(currentTime);
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("08:00");
        dto.setEndTimeRange("20:00");
        dto.setImmediatelyAddTime(60);
        dto.setTimeWindow(30);
        
        return dto;
    }
    
    /**
     * 克隆DTO
     */
    private static AvaiableAppointmentTimeDTO cloneDTO(AvaiableAppointmentTimeDTO source) {
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        dto.setAvaiableTimeBegin(source.getAvaiableTimeBegin());
        dto.setAvaiableTimeEnd(source.getAvaiableTimeEnd());
        dto.setBeginTimeRange(source.getBeginTimeRange());
        dto.setEndTimeRange(source.getEndTimeRange());
        dto.setAdvanceAppointTime(source.getAdvanceAppointTime());
        dto.setImmediatelyAddTime(source.getImmediatelyAddTime());
        dto.setTimeWindow(source.getTimeWindow());
        return dto;
    }
}
