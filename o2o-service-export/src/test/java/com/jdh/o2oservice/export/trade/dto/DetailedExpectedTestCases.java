package com.jdh.o2oservice.export.trade.dto;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * calculateAvailableTimeSlots 详细预期测试用例
 * 列出所有预期的可约时段，用于精确验证
 */
public class DetailedExpectedTestCases {
    
    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    public static void main(String[] args) {
        System.out.println("========================================================");
        System.out.println("calculateAvailableTimeSlots 详细预期测试用例");
        System.out.println("========================================================\n");
        
        testCaseA_ImmediateAppointment();
        testCaseB_AdvanceAppointment();
        testCaseC_CrossDayScenario();
        
        System.out.println("========================================================");
        System.out.println("详细预期测试完成");
        System.out.println("========================================================");
    }
    
    /**
     * 测试用例A：立即预约场景 - 完整时段列表
     */
    private static void testCaseA_ImmediateAppointment() {
        System.out.println("【测试用例A】立即预约场景 - advanceAppointTime = null");
        System.out.println("=".repeat(70));
        
        // 固定当前时间：2024-01-15 10:30:00
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 10, 30, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置到当天18:00结束
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.set(Calendar.HOUR_OF_DAY, 18);
        endTime.set(Calendar.MINUTE, 0);
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("08:00");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(null);
        dto.setImmediatelyAddTime(60);
        dto.setTimeWindow(30);
        
        System.out.println("📥 输入参数：");
        System.out.println("  当前时间: " + dateTimeFormat.format(currentTime.getTime()));
        System.out.println("  可预约范围: " + dateTimeFormat.format(dto.getAvaiableTimeBegin()) + 
                          " 到 " + dateTimeFormat.format(dto.getAvaiableTimeEnd()));
        System.out.println("  每日时间: " + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("  advanceAppointTime: " + dto.getAdvanceAppointTime());
        System.out.println("  immediatelyAddTime: " + dto.getImmediatelyAddTime());
        System.out.println("  timeWindow: " + dto.getTimeWindow());
        
        System.out.println("\n🎯 预期输出（所有可约时段）：");
        System.out.println("  [1] 2024-01-15 10:30:00 - 2024-01-15 12:00:00 | 时长:90分钟 | 立即预约:true");
        System.out.println("  [2] 2024-01-15 12:00:00 - 2024-01-15 12:30:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [3] 2024-01-15 12:30:00 - 2024-01-15 13:00:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [4] 2024-01-15 13:00:00 - 2024-01-15 13:30:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [5] 2024-01-15 13:30:00 - 2024-01-15 14:00:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [6] 2024-01-15 14:00:00 - 2024-01-15 14:30:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [7] 2024-01-15 14:30:00 - 2024-01-15 15:00:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [8] 2024-01-15 15:00:00 - 2024-01-15 15:30:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [9] 2024-01-15 15:30:00 - 2024-01-15 16:00:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [10] 2024-01-15 16:00:00 - 2024-01-15 16:30:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [11] 2024-01-15 16:30:00 - 2024-01-15 17:00:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [12] 2024-01-15 17:00:00 - 2024-01-15 17:30:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [13] 2024-01-15 17:30:00 - 2024-01-15 18:00:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  预期总数: 13个时间段");
        
        // 执行实际测试
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("\n📤 实际输出：");
        System.out.println("  总时间段数: " + result.size());
        for (int i = 0; i < result.size(); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s - %s | 时长:%d分钟 | 立即预约:%s%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()),
                dateTimeFormat.format(slot.getEndTime()),
                duration,
                slot.getIsImmediately()
            );
        }
        System.out.println();
    }
    
    /**
     * 测试用例B：提前预约场景 - 完整时段列表
     */
    private static void testCaseB_AdvanceAppointment() {
        System.out.println("【测试用例B】提前预约场景 - advanceAppointTime = 90");
        System.out.println("=".repeat(70));
        
        // 固定当前时间：2024-01-15 14:00:00
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 14, 0, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置到当天20:00结束
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.set(Calendar.HOUR_OF_DAY, 20);
        endTime.set(Calendar.MINUTE, 0);
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("09:00");
        dto.setEndTimeRange("20:00");
        dto.setAdvanceAppointTime(90); // 提前90分钟
        dto.setImmediatelyAddTime(60);
        dto.setTimeWindow(45);
        
        System.out.println("📥 输入参数：");
        System.out.println("  当前时间: " + dateTimeFormat.format(currentTime.getTime()));
        System.out.println("  可预约范围: " + dateTimeFormat.format(dto.getAvaiableTimeBegin()) + 
                          " 到 " + dateTimeFormat.format(dto.getAvaiableTimeEnd()));
        System.out.println("  每日时间: " + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("  advanceAppointTime: " + dto.getAdvanceAppointTime());
        System.out.println("  immediatelyAddTime: " + dto.getImmediatelyAddTime());
        System.out.println("  timeWindow: " + dto.getTimeWindow());
        
        System.out.println("\n🎯 预期输出（所有可约时段）：");
        System.out.println("  计算逻辑: 当前时间14:00 + 提前90分钟 = 15:30，从15:30开始按45分钟窗口生成");
        System.out.println("  [1] 2024-01-15 15:30:00 - 2024-01-15 16:15:00 | 时长:45分钟 | 立即预约:false");
        System.out.println("  [2] 2024-01-15 16:15:00 - 2024-01-15 17:00:00 | 时长:45分钟 | 立即预约:false");
        System.out.println("  [3] 2024-01-15 17:00:00 - 2024-01-15 17:45:00 | 时长:45分钟 | 立即预约:false");
        System.out.println("  [4] 2024-01-15 17:45:00 - 2024-01-15 18:30:00 | 时长:45分钟 | 立即预约:false");
        System.out.println("  [5] 2024-01-15 18:30:00 - 2024-01-15 19:15:00 | 时长:45分钟 | 立即预约:false");
        System.out.println("  [6] 2024-01-15 19:15:00 - 2024-01-15 20:00:00 | 时长:45分钟 | 立即预约:false");
        System.out.println("  预期总数: 6个时间段");
        
        // 执行实际测试
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("\n📤 实际输出：");
        System.out.println("  总时间段数: " + result.size());
        for (int i = 0; i < result.size(); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s - %s | 时长:%d分钟 | 立即预约:%s%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()),
                dateTimeFormat.format(slot.getEndTime()),
                duration,
                slot.getIsImmediately()
            );
        }
        System.out.println();
    }
    
    /**
     * 测试用例C：跨天场景 - 完整时段列表
     */
    private static void testCaseC_CrossDayScenario() {
        System.out.println("【测试用例C】跨天场景 - advanceAppointTime = null");
        System.out.println("=".repeat(70));
        
        // 固定当前时间：2024-01-15 17:30:00 (接近下班)
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 17, 30, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置到次日12:00结束
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.add(Calendar.DAY_OF_MONTH, 1);
        endTime.set(Calendar.HOUR_OF_DAY, 12);
        endTime.set(Calendar.MINUTE, 0);
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("08:00");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(null);
        dto.setImmediatelyAddTime(30);
        dto.setTimeWindow(30);
        
        System.out.println("📥 输入参数：");
        System.out.println("  当前时间: " + dateTimeFormat.format(currentTime.getTime()));
        System.out.println("  可预约范围: " + dateTimeFormat.format(dto.getAvaiableTimeBegin()) + 
                          " 到 " + dateTimeFormat.format(dto.getAvaiableTimeEnd()));
        System.out.println("  每日时间: " + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("  advanceAppointTime: " + dto.getAdvanceAppointTime());
        System.out.println("  immediatelyAddTime: " + dto.getImmediatelyAddTime());
        System.out.println("  timeWindow: " + dto.getTimeWindow());
        
        System.out.println("\n🎯 预期输出（所有可约时段）：");
        System.out.println("  今天剩余时间段（17:30-18:00）：");
        System.out.println("  [1] 2024-01-15 17:30:00 - 2024-01-15 18:00:00 | 时长:60分钟 | 立即预约:true");
        System.out.println("  明天时间段（08:00-12:00）：");
        System.out.println("  [2] 2024-01-16 08:00:00 - 2024-01-16 08:30:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [3] 2024-01-16 08:30:00 - 2024-01-16 09:00:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [4] 2024-01-16 09:00:00 - 2024-01-16 09:30:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [5] 2024-01-16 09:30:00 - 2024-01-16 10:00:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [6] 2024-01-16 10:00:00 - 2024-01-16 10:30:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [7] 2024-01-16 10:30:00 - 2024-01-16 11:00:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [8] 2024-01-16 11:00:00 - 2024-01-16 11:30:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  [9] 2024-01-16 11:30:00 - 2024-01-16 12:00:00 | 时长:30分钟 | 立即预约:false");
        System.out.println("  预期总数: 9个时间段");
        
        // 执行实际测试
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("\n📤 实际输出：");
        System.out.println("  总时间段数: " + result.size());
        for (int i = 0; i < result.size(); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s - %s | 时长:%d分钟 | 立即预约:%s%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()),
                dateTimeFormat.format(slot.getEndTime()),
                duration,
                slot.getIsImmediately()
            );
        }
        System.out.println();
    }
}
