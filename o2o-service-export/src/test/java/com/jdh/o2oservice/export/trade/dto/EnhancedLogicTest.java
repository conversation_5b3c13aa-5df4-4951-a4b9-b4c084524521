package com.jdh.o2oservice.export.trade.dto;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 测试新增的两个逻辑：
 * 1. 输出的可约时段必须大于当前时间
 * 2. advanceAppointTime=null时第一个可约时间为立即预约；advanceAppointTime>0时第一个可约时段是当前时间+advanceAppointTime分钟后
 */
public class EnhancedLogicTest {
    
    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    public static void main(String[] args) {
        System.out.println("========================================================");
        System.out.println("calculateAvailableTimeSlots 新增逻辑验证测试");
        System.out.println("========================================================\n");
        
        testLogic1_TimeSlotsMustBeAfterCurrentTime();
        testLogic2_AdvanceAppointTimeLogic();
        
        System.out.println("========================================================");
        System.out.println("🎉 新增逻辑验证完成！");
        System.out.println("========================================================");
    }
    
    /**
     * 测试逻辑1：输出的可约时段必须大于当前时间
     */
    private static void testLogic1_TimeSlotsMustBeAfterCurrentTime() {
        System.out.println("【逻辑1验证】输出的可约时段必须大于当前时间");
        System.out.println("-".repeat(60));
        
        // 设置测试时间：当前时间
        Date currentTime = new Date();
        System.out.println("当前系统时间: " + dateTimeFormat.format(currentTime));
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置可预约时间范围：从1小时前到2小时后（包含过去时间）
        Calendar startCal = Calendar.getInstance();
        startCal.add(Calendar.HOUR_OF_DAY, -1); // 1小时前
        
        Calendar endCal = Calendar.getInstance();
        endCal.add(Calendar.HOUR_OF_DAY, 2); // 2小时后
        
        dto.setAvaiableTimeBegin(startCal.getTime());
        dto.setAvaiableTimeEnd(endCal.getTime());
        dto.setBeginTimeRange("00:00"); // 全天可预约
        dto.setEndTimeRange("23:59");
        dto.setAdvanceAppointTime(null); // 支持立即预约
        dto.setImmediatelyAddTime(30);
        dto.setTimeWindow(15);
        
        System.out.println("\n📥 入参设置：");
        System.out.println("  可预约开始时间: " + dateTimeFormat.format(dto.getAvaiableTimeBegin()) + " (1小时前)");
        System.out.println("  可预约结束时间: " + dateTimeFormat.format(dto.getAvaiableTimeEnd()) + " (2小时后)");
        System.out.println("  advanceAppointTime: " + dto.getAdvanceAppointTime());
        System.out.println("  timeWindow: " + dto.getTimeWindow() + "分钟");
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("\n📤 验证结果：");
        System.out.println("  生成时间段数量: " + result.size());
        
        boolean allAfterCurrentTime = true;
        int checkedCount = Math.min(5, result.size());
        
        for (int i = 0; i < checkedCount; i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            boolean isAfterCurrent = slot.getBeginTime().after(currentTime);
            
            System.out.printf("  [%d] %s | 大于当前时间: %s | 立即预约: %s%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()),
                isAfterCurrent ? "✓" : "✗",
                slot.getIsImmediately()
            );
            
            if (!isAfterCurrent) {
                allAfterCurrentTime = false;
            }
        }
        
        System.out.println("\n✅ 逻辑1验证结果: " + (allAfterCurrentTime ? "✓ 通过" : "✗ 失败"));
        System.out.println("   所有时间段都大于当前时间: " + allAfterCurrentTime);
        System.out.println();
    }
    
    /**
     * 测试逻辑2：advanceAppointTime的不同值对应的逻辑
     */
    private static void testLogic2_AdvanceAppointTimeLogic() {
        System.out.println("【逻辑2验证】advanceAppointTime逻辑");
        System.out.println("-".repeat(60));
        
        Date currentTime = new Date();
        System.out.println("当前系统时间: " + dateTimeFormat.format(currentTime));
        
        // 测试2.1：advanceAppointTime = null（立即预约）
        System.out.println("\n测试2.1：advanceAppointTime = null（立即预约）");
        testAdvanceAppointTimeNull();
        
        // 测试2.2：advanceAppointTime > 0（提前预约）
        System.out.println("\n测试2.2：advanceAppointTime = 120（提前120分钟预约）");
        testAdvanceAppointTimePositive(120);
        
        // 测试2.3：advanceAppointTime = 0（立即预约，兼容性）
        System.out.println("\n测试2.3：advanceAppointTime = 0（立即预约，兼容性）");
        testAdvanceAppointTimeZero();
    }
    
    /**
     * 测试 advanceAppointTime = null 的情况
     */
    private static void testAdvanceAppointTimeNull() {
        AvaiableAppointmentTimeDTO dto = createBaseDTO();
        dto.setAdvanceAppointTime(null); // 立即预约
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("📥 入参: advanceAppointTime = null");
        System.out.println("📤 结果: 生成 " + result.size() + " 个时间段");
        
        if (!result.isEmpty()) {
            AppointmentTimeRangeDTO first = result.get(0);
            boolean isImmediate = first.getIsImmediately();
            long duration = (first.getEndTime().getTime() - first.getBeginTime().getTime()) / (1000 * 60);
            
            System.out.println("  第1个时间段:");
            System.out.println("    开始时间: " + dateTimeFormat.format(first.getBeginTime()));
            System.out.println("    结束时间: " + dateTimeFormat.format(first.getEndTime()));
            System.out.println("    时长: " + duration + "分钟");
            System.out.println("    立即预约: " + isImmediate);
            
            System.out.println("✅ 验证: 第一个时间段是立即预约 = " + (isImmediate ? "✓ 通过" : "✗ 失败"));
        } else {
            System.out.println("✗ 没有生成时间段");
        }
    }
    
    /**
     * 测试 advanceAppointTime > 0 的情况
     */
    private static void testAdvanceAppointTimePositive(int advanceMinutes) {
        AvaiableAppointmentTimeDTO dto = createBaseDTO();
        dto.setAdvanceAppointTime(advanceMinutes); // 提前预约
        
        Date currentTime = new Date();
        Calendar expectedStart = Calendar.getInstance();
        expectedStart.setTime(currentTime);
        expectedStart.add(Calendar.MINUTE, advanceMinutes);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("📥 入参: advanceAppointTime = " + advanceMinutes);
        System.out.println("📤 结果: 生成 " + result.size() + " 个时间段");
        System.out.println("预期第一个时间段开始时间应该在: " + dateTimeFormat.format(expectedStart.getTime()) + " 之后");
        
        if (!result.isEmpty()) {
            AppointmentTimeRangeDTO first = result.get(0);
            boolean isImmediate = first.getIsImmediately();
            boolean isAfterExpected = first.getBeginTime().after(expectedStart.getTime()) || 
                                    first.getBeginTime().equals(expectedStart.getTime());
            
            System.out.println("  第1个时间段:");
            System.out.println("    开始时间: " + dateTimeFormat.format(first.getBeginTime()));
            System.out.println("    立即预约: " + isImmediate);
            System.out.println("    在预期时间之后: " + isAfterExpected);
            
            System.out.println("✅ 验证:");
            System.out.println("  第一个时间段非立即预约 = " + (!isImmediate ? "✓ 通过" : "✗ 失败"));
            System.out.println("  开始时间符合提前预约要求 = " + (isAfterExpected ? "✓ 通过" : "✗ 失败"));
        } else {
            System.out.println("✗ 没有生成时间段");
        }
    }
    
    /**
     * 测试 advanceAppointTime = 0 的情况（兼容性）
     */
    private static void testAdvanceAppointTimeZero() {
        AvaiableAppointmentTimeDTO dto = createBaseDTO();
        dto.setAdvanceAppointTime(0); // 立即预约（兼容性）
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("📥 入参: advanceAppointTime = 0");
        System.out.println("📤 结果: 生成 " + result.size() + " 个时间段");
        
        if (!result.isEmpty()) {
            AppointmentTimeRangeDTO first = result.get(0);
            boolean isImmediate = first.getIsImmediately();
            
            System.out.println("  第1个时间段:");
            System.out.println("    开始时间: " + dateTimeFormat.format(first.getBeginTime()));
            System.out.println("    立即预约: " + isImmediate);
            
            System.out.println("✅ 验证: 第一个时间段是立即预约 = " + (isImmediate ? "✓ 通过" : "✗ 失败"));
        } else {
            System.out.println("✗ 没有生成时间段");
        }
    }
    
    /**
     * 创建基础DTO配置
     */
    private static AvaiableAppointmentTimeDTO createBaseDTO() {
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置可预约时间范围：当前时间到6小时后
        Date currentTime = new Date();
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(currentTime);
        endCal.add(Calendar.HOUR_OF_DAY, 6);
        
        dto.setAvaiableTimeBegin(currentTime);
        dto.setAvaiableTimeEnd(endCal.getTime());
        dto.setBeginTimeRange("00:00");
        dto.setEndTimeRange("23:59");
        dto.setImmediatelyAddTime(60);
        dto.setTimeWindow(30);
        
        return dto;
    }
}
