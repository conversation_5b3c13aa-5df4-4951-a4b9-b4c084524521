package com.jdh.o2oservice.export.trade.dto;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 快速验证测试用例 - 简洁的输入输出对比
 */
public class QuickValidationTests {
    
    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    public static void main(String[] args) {
        System.out.println("========================================================");
        System.out.println("calculateAvailableTimeSlots 快速验证测试");
        System.out.println("========================================================\n");
        
        quickTest1_Immediate();
        quickTest2_Advance60();
        quickTest3_Advance120();
        quickTest4_TimeAlignment();
        
        System.out.println("========================================================");
        System.out.println("快速验证完成");
        System.out.println("========================================================");
    }
    
    /**
     * 快速测试1：立即预约 - 简单场景
     */
    private static void quickTest1_Immediate() {
        System.out.println("【快速测试1】立即预约 - advanceAppointTime = null");
        System.out.println("-".repeat(60));
        
        // 当前时间：2024-01-15 10:00:00
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 10, 0, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.add(Calendar.HOUR_OF_DAY, 2); // 到12:00
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("09:00");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(null);
        dto.setImmediatelyAddTime(30);
        dto.setTimeWindow(30);
        
        System.out.println("📥 输入: 当前时间10:00, advanceAppointTime=null, immediatelyAddTime=30, timeWindow=30");
        
        System.out.println("🎯 预期输出:");
        System.out.println("  [1] 10:00-11:00 (60分钟, 立即预约)");
        System.out.println("  [2] 11:00-11:30 (30分钟, 常规预约)");
        System.out.println("  [3] 11:30-12:00 (30分钟, 常规预约)");
        System.out.println("  总数: 3个");
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("📤 实际输出:");
        for (int i = 0; i < result.size(); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s-%s (%d分钟, %s)%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()).substring(11, 16),
                dateTimeFormat.format(slot.getEndTime()).substring(11, 16),
                duration,
                slot.getIsImmediately() ? "立即预约" : "常规预约"
            );
        }
        System.out.println("  总数: " + result.size() + "个");
        System.out.println();
    }
    
    /**
     * 快速测试2：提前60分钟预约
     */
    private static void quickTest2_Advance60() {
        System.out.println("【快速测试2】提前预约 - advanceAppointTime = 60");
        System.out.println("-".repeat(60));
        
        // 当前时间：2024-01-15 14:00:00
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 14, 0, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.add(Calendar.HOUR_OF_DAY, 3); // 到17:00
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("09:00");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(60);
        dto.setImmediatelyAddTime(45);
        dto.setTimeWindow(30);
        
        System.out.println("📥 输入: 当前时间14:00, advanceAppointTime=60, timeWindow=30");
        
        System.out.println("🎯 预期输出:");
        System.out.println("  计算: 14:00 + 60分钟 = 15:00开始");
        System.out.println("  [1] 15:00-15:30 (30分钟, 常规预约)");
        System.out.println("  [2] 15:30-16:00 (30分钟, 常规预约)");
        System.out.println("  [3] 16:00-16:30 (30分钟, 常规预约)");
        System.out.println("  [4] 16:30-17:00 (30分钟, 常规预约)");
        System.out.println("  总数: 4个");
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("📤 实际输出:");
        for (int i = 0; i < result.size(); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s-%s (%d分钟, %s)%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()).substring(11, 16),
                dateTimeFormat.format(slot.getEndTime()).substring(11, 16),
                duration,
                slot.getIsImmediately() ? "立即预约" : "常规预约"
            );
        }
        System.out.println("  总数: " + result.size() + "个");
        System.out.println();
    }
    
    /**
     * 快速测试3：提前120分钟预约
     */
    private static void quickTest3_Advance120() {
        System.out.println("【快速测试3】提前预约 - advanceAppointTime = 120");
        System.out.println("-".repeat(60));
        
        // 当前时间：2024-01-15 13:00:00
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 13, 0, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.add(Calendar.HOUR_OF_DAY, 5); // 到18:00
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("09:00");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(120);
        dto.setImmediatelyAddTime(60);
        dto.setTimeWindow(45);
        
        System.out.println("📥 输入: 当前时间13:00, advanceAppointTime=120, timeWindow=45");
        
        System.out.println("🎯 预期输出:");
        System.out.println("  计算: 13:00 + 120分钟 = 15:00开始");
        System.out.println("  [1] 15:00-15:45 (45分钟, 常规预约)");
        System.out.println("  [2] 15:45-16:30 (45分钟, 常规预约)");
        System.out.println("  [3] 16:30-17:15 (45分钟, 常规预约)");
        System.out.println("  [4] 17:15-18:00 (45分钟, 常规预约)");
        System.out.println("  总数: 4个");
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("📤 实际输出:");
        for (int i = 0; i < result.size(); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s-%s (%d分钟, %s)%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()).substring(11, 16),
                dateTimeFormat.format(slot.getEndTime()).substring(11, 16),
                duration,
                slot.getIsImmediately() ? "立即预约" : "常规预约"
            );
        }
        System.out.println("  总数: " + result.size() + "个");
        System.out.println();
    }
    
    /**
     * 快速测试4：时间对齐测试
     */
    private static void quickTest4_TimeAlignment() {
        System.out.println("【快速测试4】时间对齐 - 非整点开始");
        System.out.println("-".repeat(60));
        
        // 当前时间：2024-01-15 10:23:00 (非整点)
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(2024, Calendar.JANUARY, 15, 10, 23, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        Calendar endTime = (Calendar) currentTime.clone();
        endTime.add(Calendar.HOUR_OF_DAY, 2); // 到12:23
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange("09:00");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(null);
        dto.setImmediatelyAddTime(30);
        dto.setTimeWindow(20);
        
        System.out.println("📥 输入: 当前时间10:23, advanceAppointTime=null, timeWindow=20");
        
        System.out.println("🎯 预期输出:");
        System.out.println("  [1] 10:23-10:53 (50分钟, 立即预约) // 30+20=50");
        System.out.println("  [2] 11:00-11:20 (20分钟, 常规预约) // 对齐到20分钟边界");
        System.out.println("  [3] 11:20-11:40 (20分钟, 常规预约)");
        System.out.println("  [4] 11:40-12:00 (20分钟, 常规预约)");
        System.out.println("  [5] 12:00-12:20 (20分钟, 常规预约)");
        System.out.println("  总数: 5个");
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("📤 实际输出:");
        for (int i = 0; i < result.size(); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            System.out.printf("  [%d] %s-%s (%d分钟, %s)%n",
                i + 1,
                dateTimeFormat.format(slot.getBeginTime()).substring(11, 16),
                dateTimeFormat.format(slot.getEndTime()).substring(11, 16),
                duration,
                slot.getIsImmediately() ? "立即预约" : "常规预约"
            );
        }
        System.out.println("  总数: " + result.size() + "个");
        System.out.println();
    }
}
