package com.jdh.o2oservice.export.trade.dto;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 结束时间调整逻辑测试
 * 验证：1. 结束时间以整点或整半点结束  2. 不能超过当天endTimeRange
 */
public class EndTimeAdjustmentTest {
    
    private static final SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    public static void main(String[] args) {
        System.out.println("=== 结束时间调整逻辑测试 ===\n");
        
        testEndTimeAdjustment();
        testEndTimeLimit();
        testCombinedScenarios();
        
        System.out.println("=== 测试完成 ===");
    }
    
    /**
     * 测试结束时间调整到整点或整半点
     */
    private static void testEndTimeAdjustment() {
        System.out.println("【测试1】结束时间调整到整点或整半点");
        System.out.println("=" .repeat(50));
        
        // 测试用例1：立即预约，结束时间需要调整
        testCase("用例1.1", "2024-01-15 10:17:00", "2024-01-15 14:00:00", 
                "08:00", "18:00", null, 60, "检测");
        
        // 测试用例2：立即预约，结束时间需要调整
        testCase("用例1.2", "2024-01-15 10:23:00", "2024-01-15 15:00:00", 
                "08:00", "18:00", null, 120, "护理");
        
        // 测试用例3：提前预约，结束时间需要调整
        testCase("用例1.3", "2024-01-15 10:37:00", "2024-01-15 15:00:00", 
                "08:00", "18:00", 60, 60, "检测");
        
        System.out.println();
    }
    
    /**
     * 测试结束时间不超过当天endTimeRange
     */
    private static void testEndTimeLimit() {
        System.out.println("【测试2】结束时间不超过当天endTimeRange");
        System.out.println("=" .repeat(50));
        
        // 测试用例1：立即预约接近下班时间
        testCase("用例2.1", "2024-01-15 17:45:00", "2024-01-16 10:00:00", 
                "08:00", "18:00", null, 60, "检测");
        
        // 测试用例2：护理服务接近下班时间
        testCase("用例2.2", "2024-01-15 16:30:00", "2024-01-16 10:00:00", 
                "08:00", "18:00", null, 120, "护理");
        
        // 测试用例3：限制时间范围
        testCase("用例2.3", "2024-01-15 14:45:00", "2024-01-15 16:00:00", 
                "09:00", "15:00", null, 60, "检测");
        
        System.out.println();
    }
    
    /**
     * 测试组合场景
     */
    private static void testCombinedScenarios() {
        System.out.println("【测试3】组合场景测试");
        System.out.println("=" .repeat(50));
        
        // 测试用例1：夜间服务
        testCase("用例3.1", "2024-01-15 22:17:00", "2024-01-16 02:00:00", 
                "18:00", "23:59", null, 60, "检测");
        
        // 测试用例2：全天服务
        testCase("用例3.2", "2024-01-15 23:47:00", "2024-01-16 06:00:00", 
                "00:00", "23:59", null, 120, "护理");
        
        // 测试用例3：短时间窗口
        testCase("用例3.3", "2024-01-15 17:51:00", "2024-01-15 18:00:00", 
                "08:00", "18:00", null, 60, "检测");
        
        System.out.println();
    }
    
    private static void testCase(String caseName, String currentTimeStr, String endTimeStr,
                               String beginRange, String endRange, Integer advanceMinutes, 
                               Integer immediateAdd, String serviceType) {
        
        System.out.println(caseName + "：" + serviceType + "服务");
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 解析时间
        Calendar currentTime = parseTime(currentTimeStr);
        Calendar endTime = parseTime(endTimeStr);
        
        dto.setAvaiableTimeBegin(currentTime.getTime());
        dto.setAvaiableTimeEnd(endTime.getTime());
        dto.setBeginTimeRange(beginRange);
        dto.setEndTimeRange(endRange);
        dto.setAdvanceAppointTime(advanceMinutes);
        dto.setImmediatelyAddTime(immediateAdd);
        dto.setTimeWindow(30);
        
        System.out.println("  输入：当前时间=" + currentTimeStr + 
                          ", 每日范围=" + beginRange + "-" + endRange +
                          ", 提前=" + advanceMinutes + 
                          ", 立即+=" + immediateAdd);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("  输出：共" + result.size() + "个时间段");
        
        // 显示前几个时间段并验证结束时间
        for (int i = 0; i < Math.min(3, result.size()); i++) {
            AppointmentTimeRangeDTO slot = result.get(i);
            Calendar endCal = Calendar.getInstance();
            endCal.setTime(slot.getEndTime());
            
            int endMinute = endCal.get(Calendar.MINUTE);
            boolean isHalfHourAligned = (endMinute == 0 || endMinute == 30);
            
            // 检查是否超过当天endTimeRange
            String[] endRangeParts = endRange.split(":");
            int endRangeHour = Integer.parseInt(endRangeParts[0]);
            int endRangeMinute = Integer.parseInt(endRangeParts[1]);
            
            Calendar dayLimit = Calendar.getInstance();
            dayLimit.setTime(slot.getBeginTime());
            dayLimit.set(Calendar.HOUR_OF_DAY, endRangeHour);
            dayLimit.set(Calendar.MINUTE, endRangeMinute);
            dayLimit.set(Calendar.SECOND, 0);
            dayLimit.set(Calendar.MILLISECOND, 0);
            
            boolean withinDayLimit = !slot.getEndTime().after(dayLimit.getTime());
            
            long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            
            System.out.printf("    [%d] %s-%s (%dmin,%s) 整点半点:%s 范围内:%s%n",
                i + 1,
                fmt.format(slot.getBeginTime()).substring(11),
                fmt.format(slot.getEndTime()).substring(11),
                duration,
                slot.getIsImmediately() ? "立即" : "预约",
                isHalfHourAligned ? "✓" : "✗",
                withinDayLimit ? "✓" : "✗"
            );
        }
        
        if (result.size() > 3) {
            System.out.println("    ... 还有" + (result.size() - 3) + "个时间段");
        }
        
        // 验证所有时间段的结束时间
        boolean allEndTimesValid = validateAllEndTimes(result, endRange);
        System.out.println("  验证结果：" + (allEndTimesValid ? "✅ 所有结束时间都符合要求" : "❌ 存在不符合要求的结束时间"));
        
        System.out.println();
    }
    
    private static Calendar parseTime(String timeStr) {
        Calendar cal = Calendar.getInstance();
        String[] parts = timeStr.split(" ");
        String[] dateParts = parts[0].split("-");
        String[] timeParts = parts[1].split(":");
        
        cal.set(Integer.parseInt(dateParts[0]), 
               Integer.parseInt(dateParts[1]) - 1, 
               Integer.parseInt(dateParts[2]),
               Integer.parseInt(timeParts[0]),
               Integer.parseInt(timeParts[1]),
               Integer.parseInt(timeParts[2]));
        cal.set(Calendar.MILLISECOND, 0);
        
        return cal;
    }
    
    private static boolean validateAllEndTimes(List<AppointmentTimeRangeDTO> result, String endRange) {
        String[] endRangeParts = endRange.split(":");
        int endRangeHour = Integer.parseInt(endRangeParts[0]);
        int endRangeMinute = Integer.parseInt(endRangeParts[1]);
        
        for (AppointmentTimeRangeDTO slot : result) {
            Calendar endCal = Calendar.getInstance();
            endCal.setTime(slot.getEndTime());
            
            // 检查是否是整点或整半点
            int endMinute = endCal.get(Calendar.MINUTE);
            if (endMinute != 0 && endMinute != 30) {
                System.out.println("    ❌ 结束时间不是整点或整半点: " + fmt.format(slot.getEndTime()));
                return false;
            }
            
            // 检查是否超过当天endTimeRange
            Calendar dayLimit = Calendar.getInstance();
            dayLimit.setTime(slot.getBeginTime());
            dayLimit.set(Calendar.HOUR_OF_DAY, endRangeHour);
            dayLimit.set(Calendar.MINUTE, endRangeMinute);
            dayLimit.set(Calendar.SECOND, 0);
            dayLimit.set(Calendar.MILLISECOND, 0);
            
            if (slot.getEndTime().after(dayLimit.getTime())) {
                System.out.println("    ❌ 结束时间超过当天限制: " + fmt.format(slot.getEndTime()) + 
                                 " > " + fmt.format(dayLimit.getTime()));
                return false;
            }
        }
        
        return true;
    }
}
