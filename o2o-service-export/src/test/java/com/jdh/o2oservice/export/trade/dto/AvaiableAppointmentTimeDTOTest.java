package com.jdh.o2oservice.export.trade.dto;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * AvaiableAppointmentTimeDTO 测试类
 * 测试新增的 calculateAvailableTimeSlots 方法
 */
class AvaiableAppointmentTimeDTOTest {

    private AvaiableAppointmentTimeDTO dto;
    private Date testStartTime;
    private Date testEndTime;

    @BeforeEach
    void setUp() {
        dto = new AvaiableAppointmentTimeDTO();
        
        // 设置测试时间范围：今天到3天后
        Calendar calendar = Calendar.getInstance();
        testStartTime = calendar.getTime();
        
        calendar.add(Calendar.DAY_OF_MONTH, 3);
        testEndTime = calendar.getTime();
        
        // 基础配置
        dto.setAvaiableTimeBegin(testStartTime);
        dto.setAvaiableTimeEnd(testEndTime);
        dto.setBeginTimeRange("09:00");
        dto.setEndTimeRange("18:00");
        dto.setTimeWindow(30); // 30分钟时间窗口
    }

    @Test
    void testCalculateAvailableTimeSlots_WithImmediateAppointment() {
        // 测试立即预约场景
        dto.setAdvanceAppointTime(null); // null表示支持立即预约
        dto.setImmediatelyAddTime(60); // 立即预约增加60分钟
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // 检查第一个时间段是否为立即预约
        AppointmentTimeRangeDTO firstSlot = result.get(0);
        assertTrue(firstSlot.getIsImmediately());
        
        // 检查立即预约的时长：immediatelyAddTime(60) + timeWindow(30) = 90分钟
        long duration = firstSlot.getEndTime().getTime() - firstSlot.getBeginTime().getTime();
        assertEquals(90 * 60 * 1000, duration); // 90分钟转换为毫秒
    }

    @Test
    void testCalculateAvailableTimeSlots_WithoutImmediateAppointment() {
        // 测试非立即预约场景
        dto.setAdvanceAppointTime(120); // 需要提前120分钟预约
        dto.setImmediatelyAddTime(60);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        assertNotNull(result);
        
        // 检查所有时间段都不是立即预约
        for (AppointmentTimeRangeDTO slot : result) {
            assertFalse(slot.getIsImmediately());
            
            // 检查常规时间段的时长：timeWindow(30分钟)
            long duration = slot.getEndTime().getTime() - slot.getBeginTime().getTime();
            assertEquals(30 * 60 * 1000, duration); // 30分钟转换为毫秒
        }
    }

    @Test
    void testCalculateAvailableTimeSlots_TimeWindowAlignment() {
        // 测试时间窗口对齐
        dto.setAdvanceAppointTime(0); // 支持立即预约
        dto.setImmediatelyAddTime(30);
        dto.setTimeWindow(15); // 15分钟时间窗口
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // 检查时间段是否按15分钟对齐
        for (int i = 1; i < result.size(); i++) {
            AppointmentTimeRangeDTO current = result.get(i);
            if (!current.getIsImmediately()) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(current.getBeginTime());
                int minute = cal.get(Calendar.MINUTE);
                // 分钟数应该是15的倍数
                assertEquals(0, minute % 15);
            }
        }
    }

    @Test
    void testCalculateAvailableTimeSlots_WithinDailyTimeRange() {
        // 测试每日时间范围限制
        dto.setBeginTimeRange("10:30");
        dto.setEndTimeRange("16:45");
        dto.setAdvanceAppointTime(60); // 不支持立即预约，便于测试
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        assertNotNull(result);
        
        // 检查所有时间段都在每日时间范围内
        for (AppointmentTimeRangeDTO slot : result) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(slot.getBeginTime());
            
            int hour = cal.get(Calendar.HOUR_OF_DAY);
            int minute = cal.get(Calendar.MINUTE);
            int totalMinutes = hour * 60 + minute;
            
            // 应该在10:30(630分钟)到16:45(1005分钟)之间
            assertTrue(totalMinutes >= 630); // 10:30
            assertTrue(totalMinutes < 1005); // 16:45
        }
    }

    @Test
    void testCalculateAvailableTimeSlots_InvalidParameters() {
        // 测试无效参数
        dto.setBeginTimeRange(null);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testCalculateAvailableTimeSlots_SpecialEndTime() {
        // 测试特殊结束时间 23:59
        dto.setEndTimeRange("23:59");
        dto.setAdvanceAppointTime(60);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        assertNotNull(result);
        
        // 检查是否正确处理23:59为24:00
        boolean hasLateSlot = false;
        for (AppointmentTimeRangeDTO slot : result) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(slot.getBeginTime());
            if (cal.get(Calendar.HOUR_OF_DAY) >= 23) {
                hasLateSlot = true;
                break;
            }
        }
        assertTrue(hasLateSlot);
    }

    @Test
    void testCalculateAvailableTimeSlots_CrossDayScenario() {
        // 测试跨天场景
        dto.setAdvanceAppointTime(60);
        dto.setTimeWindow(60); // 1小时时间窗口
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        assertNotNull(result);
        
        // 检查是否有多天的时间段
        Calendar firstDay = Calendar.getInstance();
        firstDay.setTime(result.get(0).getBeginTime());
        
        boolean hasMultipleDays = false;
        for (AppointmentTimeRangeDTO slot : result) {
            Calendar slotDay = Calendar.getInstance();
            slotDay.setTime(slot.getBeginTime());
            
            if (slotDay.get(Calendar.DAY_OF_YEAR) != firstDay.get(Calendar.DAY_OF_YEAR)) {
                hasMultipleDays = true;
                break;
            }
        }
        assertTrue(hasMultipleDays);
    }
}
