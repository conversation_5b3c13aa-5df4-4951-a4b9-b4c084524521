package com.jdh.o2oservice.export.trade.dto;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 详细表格测试用例 - 包含完整的列信息
 */
public class DetailedTableTestCases {

    private static final SimpleDateFormat fullFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");

    public static void main(String[] args) {
        System.out.println("=== calculateAvailableTimeSlots 详细表格测试用例 ===\n");

        printTableHeader();

        for (int i = 1; i <= 30; i++) {
            executeAndPrintTestCase(i);
        }

        printTableFooter();
    }

    private static void printTableHeader() {
        System.out.println("┌────┬─────────────────────┬─────────────────────┬─────────────────────┬─────────────┬──────┬──────┬─────────────────────────────────────────────────────────────────────────────────────┐");
        System.out.println("│用例│      当前时间       │      开始时间       │      结束时间       │ 每日时间范围│提前  │类型  │                                可约时段列表                                         │");
        System.out.println("│    │  yyyy-MM-dd HH:mm:ss│  yyyy-MM-dd HH:mm:ss│  yyyy-MM-dd HH:mm:ss│  HH:mm-HH:mm│(小时)│      │                                                                                      │");
        System.out.println("├────┼─────────────────────┼─────────────────────┼─────────────────────┼─────────────┼──────┼──────┼─────────────────────────────────────────────────────────────────────────────────────┤");
    }

    private static void printTableFooter() {
        System.out.println("└────┴─────────────────────┴─────────────────────┴─────────────────────┴─────────────┴──────┴──────┴─────────────────────────────────────────────────────────────────────────────────────┘");
        System.out.println("\n说明：");
        System.out.println("- 提前：null=立即预约, 1/2/3=提前1/2/3小时预约");
        System.out.println("- 类型：检测(60分钟增加), 护理(120分钟增加)");
        System.out.println("- 时间段格式：开始时间-结束时间(时长min,类型)");
    }

    private static void executeAndPrintTestCase(int caseNum) {
        TestCaseData testData = createTestCase(caseNum);
        AvaiableAppointmentTimeDTO dto = testData.dto;

        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();

        // 打印表格行
        System.out.printf("│%2d  │", caseNum);
        System.out.printf("%s│", fullFormat.format(testData.currentTime));
        System.out.printf("%s│", fullFormat.format(dto.getAvaiableTimeBegin()));
        System.out.printf("%s│", fullFormat.format(dto.getAvaiableTimeEnd()));
        System.out.printf("%s-%s│",
            dto.getBeginTimeRange(),
            dto.getEndTimeRange());
        System.out.printf("%4s  │", testData.advanceHours);
        System.out.printf("%4s  │", testData.serviceType);

        // 输出时间段
        if (result.isEmpty()) {
            System.out.printf("%-84s│%n", "无时间段");
        } else {
            StringBuilder output = new StringBuilder();
            for (int i = 0; i < result.size(); i++) {
                AppointmentTimeRangeDTO slot = result.get(i);
                long duration = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);

                if (i > 0) output.append(" ");
                output.append(String.format("%s-%s(%dmin,%s)",
                    timeFormat.format(slot.getBeginTime()),
                    timeFormat.format(slot.getEndTime()),
                    duration,
                    slot.getIsImmediately() ? "立即" : "预约"
                ));

                // 每行最多显示84个字符，超出则换行
                if (output.length() > 80 && i < result.size() - 1) {
                    System.out.printf("%-84s│%n", output.toString());
                    System.out.print("│    │                     │                     │                     │             │      │      │");
                    output = new StringBuilder();
                }
            }
            System.out.printf("%-84s│%n", output.toString());
        }
    }

    private static class TestCaseData {
        Date currentTime;
        AvaiableAppointmentTimeDTO dto;
        String advanceHours;
        String serviceType;
    }

    private static TestCaseData createTestCase(int caseNum) {
        TestCaseData testData = new TestCaseData();
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        Calendar now = Calendar.getInstance();
        Calendar end = Calendar.getInstance();

        switch (caseNum) {
            case 1: // 检测服务立即预约
                now.set(2024, 0, 15, 10, 17, 0);
                end.set(2024, 0, 15, 14, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 2: // 护理服务立即预约
                now.set(2024, 0, 15, 10, 23, 0);
                end.set(2024, 0, 15, 15, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 3: // 检测服务提前1小时
                now.set(2024, 0, 15, 10, 37, 0);
                end.set(2024, 0, 15, 15, 0, 0);
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "1";
                testData.serviceType = "检测";
                break;

            case 4: // 护理服务提前1小时
                now.set(2024, 0, 15, 10, 43, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(60);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "1";
                testData.serviceType = "护理";
                break;

            case 5: // 检测服务提前2小时
                now.set(2024, 0, 15, 10, 7, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(120);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "2";
                testData.serviceType = "检测";
                break;

            case 6: // 护理服务提前2小时
                now.set(2024, 0, 15, 11, 13, 0);
                end.set(2024, 0, 15, 17, 0, 0);
                dto.setAdvanceAppointTime(120);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "2";
                testData.serviceType = "护理";
                break;

            case 7: // 检测服务提前3小时
                now.set(2024, 0, 15, 9, 47, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(180);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "3";
                testData.serviceType = "检测";
                break;

            case 8: // 护理服务提前3小时
                now.set(2024, 0, 15, 10, 13, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(180);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "3";
                testData.serviceType = "护理";
                break;

            case 9: // 检测服务跨天立即预约
                now.set(2024, 0, 15, 17, 27, 0);
                end.set(2024, 0, 16, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 10: // 护理服务跨天立即预约
                now.set(2024, 0, 15, 17, 19, 0);
                end.set(2024, 0, 16, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 11: // 检测服务早上时段
                now.set(2024, 0, 15, 8, 33, 0);
                end.set(2024, 0, 15, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 12: // 护理服务早上时段
                now.set(2024, 0, 15, 8, 41, 0);
                end.set(2024, 0, 15, 13, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 13: // 检测服务中午时段
                now.set(2024, 0, 15, 12, 29, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 14: // 护理服务中午时段
                now.set(2024, 0, 15, 12, 11, 0);
                end.set(2024, 0, 15, 17, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 15: // 检测服务下午时段
                now.set(2024, 0, 15, 14, 13, 0);
                end.set(2024, 0, 15, 17, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 16: // 护理服务下午时段
                now.set(2024, 0, 15, 14, 57, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 17: // 检测服务限制时间范围
                now.set(2024, 0, 15, 10, 21, 0);
                end.set(2024, 0, 15, 15, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("09:00");
                dto.setEndTimeRange("15:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 18: // 护理服务限制时间范围
                now.set(2024, 0, 15, 10, 39, 0);
                end.set(2024, 0, 15, 16, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("09:00");
                dto.setEndTimeRange("15:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 19: // 检测服务夜间时段
                now.set(2024, 0, 15, 20, 47, 0);
                end.set(2024, 0, 16, 2, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("18:00");
                dto.setEndTimeRange("23:59");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 20: // 护理服务夜间时段
                now.set(2024, 0, 15, 21, 13, 0);
                end.set(2024, 0, 16, 3, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("18:00");
                dto.setEndTimeRange("23:59");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 21: // 检测服务全天时段
                now.set(2024, 0, 15, 15, 51, 0);
                end.set(2024, 0, 15, 20, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("00:00");
                dto.setEndTimeRange("23:59");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 22: // 护理服务全天时段
                now.set(2024, 0, 15, 16, 27, 0);
                end.set(2024, 0, 15, 21, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("00:00");
                dto.setEndTimeRange("23:59");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 23: // 检测服务短时间窗口
                now.set(2024, 0, 15, 16, 41, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 24: // 护理服务短时间窗口
                now.set(2024, 0, 15, 15, 3, 0);
                end.set(2024, 0, 15, 17, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 25: // 检测服务边界时间
                now.set(2024, 0, 15, 7, 57, 0);
                end.set(2024, 0, 15, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 26: // 护理服务边界时间
                now.set(2024, 0, 15, 7, 31, 0);
                end.set(2024, 0, 15, 13, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 27: // 检测服务多天范围
                now.set(2024, 0, 15, 16, 17, 0);
                end.set(2024, 0, 17, 10, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 28: // 护理服务多天范围
                now.set(2024, 0, 15, 15, 47, 0);
                end.set(2024, 0, 17, 12, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "护理";
                break;

            case 29: // 检测服务极短窗口
                now.set(2024, 0, 15, 17, 51, 0);
                end.set(2024, 0, 15, 18, 0, 0);
                dto.setAdvanceAppointTime(null);
                dto.setImmediatelyAddTime(60);
                dto.setBeginTimeRange("08:00");
                dto.setEndTimeRange("18:00");
                testData.advanceHours = "null";
                testData.serviceType = "检测";
                break;

            case 30: // 护理服务跨月场景
                now.set(2024, 0, 31, 23, 37, 0);
                end.set(2024, 1, 1, 12, 0, 0);
                dto.setAdvanceAppointTime(1);
                dto.setImmediatelyAddTime(120);
                dto.setBeginTimeRange("06:00");
                dto.setEndTimeRange("23:59");
                testData.advanceHours = "1";
                testData.serviceType = "护理";
                break;
        }

        dto.setAvaiableTimeBegin(now.getTime());
        dto.setAvaiableTimeEnd(end.getTime());
        dto.setTimeWindow(30); // 固定30分钟

        testData.currentTime = now.getTime();
        testData.dto = dto;

        return testData;
    }
}