package com.jdh.o2oservice.export.trade.dto;

import org.junit.jupiter.api.Test;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * AvaiableAppointmentTimeDTO 测试数据验证
 * 使用实际业务场景的测试数据验证 calculateAvailableTimeSlots 方法
 */
public class AvaiableAppointmentTimeDTOTestData {

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private final SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");

    /**
     * 测试场景1：检测服务 - 支持立即预约
     * 业务场景：用户在工作时间内下单，支持立即预约，检测服务时长1小时
     */
    @Test
    public void testScenario1_TestServiceWithImmediateAppointment() {
        System.out.println("=== 测试场景1：检测服务 - 支持立即预约 ===");
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置测试时间：2024-01-15 10:30:00 (周一上午)
        Calendar startCal = Calendar.getInstance();
        startCal.set(2024, Calendar.JANUARY, 15, 10, 30, 0);
        Date testStartTime = startCal.getTime();
        
        // 可预约时间范围：今天到3天后
        Calendar endCal = (Calendar) startCal.clone();
        endCal.add(Calendar.DAY_OF_MONTH, 3);
        Date testEndTime = endCal.getTime();
        
        // 配置参数
        dto.setAvaiableTimeBegin(testStartTime);
        dto.setAvaiableTimeEnd(testEndTime);
        dto.setBeginTimeRange("08:00");        // 每天8点开始
        dto.setEndTimeRange("20:00");          // 每天20点结束
        dto.setAdvanceAppointTime(null);       // 支持立即预约
        dto.setImmediatelyAddTime(60);         // 检测服务增加60分钟
        dto.setTimeWindow(30);                 // 30分钟时间窗口
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("配置信息：");
        System.out.println("- 开始时间：" + dateFormat.format(testStartTime));
        System.out.println("- 结束时间：" + dateFormat.format(testEndTime));
        System.out.println("- 每日时间：" + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("- 立即预约：支持（检测服务+60分钟）");
        System.out.println("- 时间窗口：" + dto.getTimeWindow() + "分钟");
        System.out.println();
        
        printTimeSlots(result, 10); // 打印前10个时间段
        System.out.println();
    }

    /**
     * 测试场景2：护理服务 - 支持立即预约
     * 业务场景：护理服务，立即预约增加2小时服务时长
     */
    @Test
    public void testScenario2_CareServiceWithImmediateAppointment() {
        System.out.println("=== 测试场景2：护理服务 - 支持立即预约 ===");
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置测试时间：2024-01-15 14:15:00 (周一下午)
        Calendar startCal = Calendar.getInstance();
        startCal.set(2024, Calendar.JANUARY, 15, 14, 15, 0);
        Date testStartTime = startCal.getTime();
        
        Calendar endCal = (Calendar) startCal.clone();
        endCal.add(Calendar.DAY_OF_MONTH, 2);
        Date testEndTime = endCal.getTime();
        
        dto.setAvaiableTimeBegin(testStartTime);
        dto.setAvaiableTimeEnd(testEndTime);
        dto.setBeginTimeRange("09:00");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(0);          // 支持立即预约
        dto.setImmediatelyAddTime(120);        // 护理服务增加120分钟
        dto.setTimeWindow(60);                 // 60分钟时间窗口
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("配置信息：");
        System.out.println("- 开始时间：" + dateFormat.format(testStartTime));
        System.out.println("- 结束时间：" + dateFormat.format(testEndTime));
        System.out.println("- 每日时间：" + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("- 立即预约：支持（护理服务+120分钟）");
        System.out.println("- 时间窗口：" + dto.getTimeWindow() + "分钟");
        System.out.println();
        
        printTimeSlots(result, 8);
        System.out.println();
    }

    /**
     * 测试场景3：预约制服务 - 需要提前预约
     * 业务场景：需要提前2小时预约，不支持立即预约
     */
    @Test
    public void testScenario3_AdvanceAppointmentRequired() {
        System.out.println("=== 测试场景3：预约制服务 - 需要提前预约 ===");
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置测试时间：2024-01-15 16:45:00 (周一下午)
        Calendar startCal = Calendar.getInstance();
        startCal.set(2024, Calendar.JANUARY, 15, 16, 45, 0);
        Date testStartTime = startCal.getTime();
        
        Calendar endCal = (Calendar) startCal.clone();
        endCal.add(Calendar.DAY_OF_MONTH, 5);
        Date testEndTime = endCal.getTime();
        
        dto.setAvaiableTimeBegin(testStartTime);
        dto.setAvaiableTimeEnd(testEndTime);
        dto.setBeginTimeRange("08:30");
        dto.setEndTimeRange("17:30");
        dto.setAdvanceAppointTime(120);        // 需要提前120分钟预约
        dto.setImmediatelyAddTime(90);         // 这个参数在非立即预约场景下不生效
        dto.setTimeWindow(45);                 // 45分钟时间窗口
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("配置信息：");
        System.out.println("- 开始时间：" + dateFormat.format(testStartTime));
        System.out.println("- 结束时间：" + dateFormat.format(testEndTime));
        System.out.println("- 每日时间：" + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("- 立即预约：不支持（需提前" + dto.getAdvanceAppointTime() + "分钟）");
        System.out.println("- 时间窗口：" + dto.getTimeWindow() + "分钟");
        System.out.println();
        
        printTimeSlots(result, 12);
        System.out.println();
    }

    /**
     * 测试场景4：夜间服务 - 跨天预约
     * 业务场景：支持夜间服务，时间范围跨天
     */
    @Test
    public void testScenario4_NightServiceCrossDays() {
        System.out.println("=== 测试场景4：夜间服务 - 跨天预约 ===");
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置测试时间：2024-01-15 22:00:00 (周一晚上)
        Calendar startCal = Calendar.getInstance();
        startCal.set(2024, Calendar.JANUARY, 15, 22, 0, 0);
        Date testStartTime = startCal.getTime();
        
        Calendar endCal = (Calendar) startCal.clone();
        endCal.add(Calendar.DAY_OF_MONTH, 2);
        Date testEndTime = endCal.getTime();
        
        dto.setAvaiableTimeBegin(testStartTime);
        dto.setAvaiableTimeEnd(testEndTime);
        dto.setBeginTimeRange("06:00");
        dto.setEndTimeRange("23:59");          // 特殊结束时间
        dto.setAdvanceAppointTime(null);       // 支持立即预约
        dto.setImmediatelyAddTime(30);
        dto.setTimeWindow(30);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("配置信息：");
        System.out.println("- 开始时间：" + dateFormat.format(testStartTime));
        System.out.println("- 结束时间：" + dateFormat.format(testEndTime));
        System.out.println("- 每日时间：" + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("- 立即预约：支持");
        System.out.println("- 时间窗口：" + dto.getTimeWindow() + "分钟");
        System.out.println();
        
        printTimeSlots(result, 15);
        System.out.println();
    }

    /**
     * 测试场景5：15分钟快速服务
     * 业务场景：快速服务，15分钟时间窗口，高频次预约
     */
    @Test
    public void testScenario5_QuickService15Minutes() {
        System.out.println("=== 测试场景5：15分钟快速服务 ===");
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置测试时间：当前时间
        Date testStartTime = new Date();
        
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(testStartTime);
        endCal.add(Calendar.HOUR_OF_DAY, 8); // 8小时内
        Date testEndTime = endCal.getTime();
        
        dto.setAvaiableTimeBegin(testStartTime);
        dto.setAvaiableTimeEnd(testEndTime);
        dto.setBeginTimeRange("07:00");
        dto.setEndTimeRange("22:00");
        dto.setAdvanceAppointTime(0);
        dto.setImmediatelyAddTime(15);         // 快速服务增加15分钟
        dto.setTimeWindow(15);                 // 15分钟时间窗口
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("配置信息：");
        System.out.println("- 开始时间：" + dateFormat.format(testStartTime));
        System.out.println("- 结束时间：" + dateFormat.format(testEndTime));
        System.out.println("- 每日时间：" + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("- 立即预约：支持（快速服务+15分钟）");
        System.out.println("- 时间窗口：" + dto.getTimeWindow() + "分钟");
        System.out.println();
        
        printTimeSlots(result, 20);
        System.out.println();
    }

    /**
     * 打印时间段信息
     */
    private void printTimeSlots(List<AppointmentTimeRangeDTO> timeSlots, int maxCount) {
        System.out.println("生成的时间段（前" + Math.min(maxCount, timeSlots.size()) + "个）：");
        
        for (int i = 0; i < Math.min(maxCount, timeSlots.size()); i++) {
            AppointmentTimeRangeDTO slot = timeSlots.get(i);
            
            String type = slot.getIsImmediately() ? "[立即预约]" : "[常规预约]";
            String beginTime = dateFormat.format(slot.getBeginTime());
            String endTime = dateFormat.format(slot.getEndTime());
            
            // 计算时长
            long durationMinutes = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            
            System.out.printf("%2d. %s %s - %s (时长:%d分钟)%n", 
                i + 1, type, beginTime, endTime, durationMinutes);
        }
        
        if (timeSlots.size() > maxCount) {
            System.out.println("... 还有 " + (timeSlots.size() - maxCount) + " 个时间段");
        }
        
        System.out.println("总计生成 " + timeSlots.size() + " 个时间段");
    }

    /**
     * 运行所有测试场景
     */
    @Test
    public void runAllTestScenarios() {
        System.out.println("开始运行所有测试场景...\n");
        
        testScenario1_TestServiceWithImmediateAppointment();
        testScenario2_CareServiceWithImmediateAppointment();
        testScenario3_AdvanceAppointmentRequired();
        testScenario4_NightServiceCrossDays();
        testScenario5_QuickService15Minutes();
        
        System.out.println("所有测试场景运行完成！");
    }
}
