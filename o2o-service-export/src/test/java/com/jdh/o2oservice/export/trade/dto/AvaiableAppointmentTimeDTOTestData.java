package com.jdh.o2oservice.export.trade.dto;

import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * AvaiableAppointmentTimeDTO 测试数据验证
 * 使用实际业务场景的测试数据验证 calculateAvailableTimeSlots 方法
 */
public class AvaiableAppointmentTimeDTOTestData {

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private final SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");

    /**
     * 测试场景1：检测服务 - 支持立即预约
     * 业务场景：用户在工作时间内下单，支持立即预约，检测服务时长1小时
     */
    @Test
    public void testScenario1_TestServiceWithImmediateAppointment() {
        System.out.println("=== 测试场景1：检测服务 - 支持立即预约 ===");
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置测试时间：2024-01-15 10:30:00 (周一上午)
        Calendar startCal = Calendar.getInstance();
        startCal.set(2024, Calendar.JANUARY, 15, 10, 30, 0);
        Date testStartTime = startCal.getTime();
        
        // 可预约时间范围：今天到3天后
        Calendar endCal = (Calendar) startCal.clone();
        endCal.add(Calendar.DAY_OF_MONTH, 3);
        Date testEndTime = endCal.getTime();
        
        // 配置参数
        dto.setAvaiableTimeBegin(testStartTime);
        dto.setAvaiableTimeEnd(testEndTime);
        dto.setBeginTimeRange("08:00");        // 每天8点开始
        dto.setEndTimeRange("20:00");          // 每天20点结束
        dto.setAdvanceAppointTime(null);       // 支持立即预约
        dto.setImmediatelyAddTime(60);         // 检测服务增加60分钟
        dto.setTimeWindow(30);                 // 30分钟时间窗口
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("配置信息：");
        System.out.println("- 开始时间：" + dateFormat.format(testStartTime));
        System.out.println("- 结束时间：" + dateFormat.format(testEndTime));
        System.out.println("- 每日时间：" + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("- 立即预约：支持（检测服务+60分钟）");
        System.out.println("- 时间窗口：" + dto.getTimeWindow() + "分钟");
        System.out.println();
        
        printTimeSlots(result, 10); // 打印前10个时间段
        System.out.println();
    }

    /**
     * 测试场景2：护理服务 - 支持立即预约
     * 业务场景：护理服务，立即预约增加2小时服务时长
     */
    @Test
    public void testScenario2_CareServiceWithImmediateAppointment() {
        System.out.println("=== 测试场景2：护理服务 - 支持立即预约 ===");
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置测试时间：2024-01-15 14:15:00 (周一下午)
        Calendar startCal = Calendar.getInstance();
        startCal.set(2024, Calendar.JANUARY, 15, 14, 15, 0);
        Date testStartTime = startCal.getTime();
        
        Calendar endCal = (Calendar) startCal.clone();
        endCal.add(Calendar.DAY_OF_MONTH, 2);
        Date testEndTime = endCal.getTime();
        
        dto.setAvaiableTimeBegin(testStartTime);
        dto.setAvaiableTimeEnd(testEndTime);
        dto.setBeginTimeRange("09:00");
        dto.setEndTimeRange("18:00");
        dto.setAdvanceAppointTime(0);          // 支持立即预约
        dto.setImmediatelyAddTime(120);        // 护理服务增加120分钟
        dto.setTimeWindow(60);                 // 60分钟时间窗口
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("配置信息：");
        System.out.println("- 开始时间：" + dateFormat.format(testStartTime));
        System.out.println("- 结束时间：" + dateFormat.format(testEndTime));
        System.out.println("- 每日时间：" + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("- 立即预约：支持（护理服务+120分钟）");
        System.out.println("- 时间窗口：" + dto.getTimeWindow() + "分钟");
        System.out.println();
        
        printTimeSlots(result, 8);
        System.out.println();
    }

    /**
     * 测试场景3：预约制服务 - 需要提前预约
     * 业务场景：需要提前2小时预约，不支持立即预约
     */
    @Test
    public void testScenario3_AdvanceAppointmentRequired() {
        System.out.println("=== 测试场景3：预约制服务 - 需要提前预约 ===");
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置测试时间：2024-01-15 16:45:00 (周一下午)
        Calendar startCal = Calendar.getInstance();
        startCal.set(2024, Calendar.JANUARY, 15, 16, 45, 0);
        Date testStartTime = startCal.getTime();
        
        Calendar endCal = (Calendar) startCal.clone();
        endCal.add(Calendar.DAY_OF_MONTH, 5);
        Date testEndTime = endCal.getTime();
        
        dto.setAvaiableTimeBegin(testStartTime);
        dto.setAvaiableTimeEnd(testEndTime);
        dto.setBeginTimeRange("08:30");
        dto.setEndTimeRange("17:30");
        dto.setAdvanceAppointTime(120);        // 需要提前120分钟预约
        dto.setImmediatelyAddTime(90);         // 这个参数在非立即预约场景下不生效
        dto.setTimeWindow(45);                 // 45分钟时间窗口
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("配置信息：");
        System.out.println("- 开始时间：" + dateFormat.format(testStartTime));
        System.out.println("- 结束时间：" + dateFormat.format(testEndTime));
        System.out.println("- 每日时间：" + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("- 立即预约：不支持（需提前" + dto.getAdvanceAppointTime() + "分钟）");
        System.out.println("- 时间窗口：" + dto.getTimeWindow() + "分钟");
        System.out.println();
        
        printTimeSlots(result, 12);
        System.out.println();
    }

    /**
     * 测试场景4：夜间服务 - 跨天预约
     * 业务场景：支持夜间服务，时间范围跨天
     */
    @Test
    public void testScenario4_NightServiceCrossDays() {
        System.out.println("=== 测试场景4：夜间服务 - 跨天预约 ===");
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置测试时间：2024-01-15 22:00:00 (周一晚上)
        Calendar startCal = Calendar.getInstance();
        startCal.set(2024, Calendar.JANUARY, 15, 22, 0, 0);
        Date testStartTime = startCal.getTime();
        
        Calendar endCal = (Calendar) startCal.clone();
        endCal.add(Calendar.DAY_OF_MONTH, 2);
        Date testEndTime = endCal.getTime();
        
        dto.setAvaiableTimeBegin(testStartTime);
        dto.setAvaiableTimeEnd(testEndTime);
        dto.setBeginTimeRange("06:00");
        dto.setEndTimeRange("23:59");          // 特殊结束时间
        dto.setAdvanceAppointTime(null);       // 支持立即预约
        dto.setImmediatelyAddTime(30);
        dto.setTimeWindow(30);
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("配置信息：");
        System.out.println("- 开始时间：" + dateFormat.format(testStartTime));
        System.out.println("- 结束时间：" + dateFormat.format(testEndTime));
        System.out.println("- 每日时间：" + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("- 立即预约：支持");
        System.out.println("- 时间窗口：" + dto.getTimeWindow() + "分钟");
        System.out.println();
        
        printTimeSlots(result, 15);
        System.out.println();
    }

    /**
     * 测试场景5：15分钟快速服务
     * 业务场景：快速服务，15分钟时间窗口，高频次预约
     */
    @Test
    public void testScenario5_QuickService15Minutes() {
        System.out.println("=== 测试场景5：15分钟快速服务 ===");
        
        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        
        // 设置测试时间：当前时间
        Date testStartTime = new Date();
        
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(testStartTime);
        endCal.add(Calendar.HOUR_OF_DAY, 8); // 8小时内
        Date testEndTime = endCal.getTime();
        
        dto.setAvaiableTimeBegin(testStartTime);
        dto.setAvaiableTimeEnd(testEndTime);
        dto.setBeginTimeRange("07:00");
        dto.setEndTimeRange("22:00");
        dto.setAdvanceAppointTime(0);
        dto.setImmediatelyAddTime(15);         // 快速服务增加15分钟
        dto.setTimeWindow(15);                 // 15分钟时间窗口
        
        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();
        
        System.out.println("配置信息：");
        System.out.println("- 开始时间：" + dateFormat.format(testStartTime));
        System.out.println("- 结束时间：" + dateFormat.format(testEndTime));
        System.out.println("- 每日时间：" + dto.getBeginTimeRange() + " - " + dto.getEndTimeRange());
        System.out.println("- 立即预约：支持（快速服务+15分钟）");
        System.out.println("- 时间窗口：" + dto.getTimeWindow() + "分钟");
        System.out.println();
        
        printTimeSlots(result, 20);
        System.out.println();
    }

    /**
     * 打印时间段信息
     */
    private void printTimeSlots(List<AppointmentTimeRangeDTO> timeSlots, int maxCount) {
        System.out.println("生成的时间段（前" + Math.min(maxCount, timeSlots.size()) + "个）：");
        
        for (int i = 0; i < Math.min(maxCount, timeSlots.size()); i++) {
            AppointmentTimeRangeDTO slot = timeSlots.get(i);
            
            String type = slot.getIsImmediately() ? "[立即预约]" : "[常规预约]";
            String beginTime = dateFormat.format(slot.getBeginTime());
            String endTime = dateFormat.format(slot.getEndTime());
            
            // 计算时长
            long durationMinutes = (slot.getEndTime().getTime() - slot.getBeginTime().getTime()) / (1000 * 60);
            
            System.out.printf("%2d. %s %s - %s (时长:%d分钟)%n", 
                i + 1, type, beginTime, endTime, durationMinutes);
        }
        
        if (timeSlots.size() > maxCount) {
            System.out.println("... 还有 " + (timeSlots.size() - maxCount) + " 个时间段");
        }
        
        System.out.println("总计生成 " + timeSlots.size() + " 个时间段");
    }

    /**
     * 测试场景6：边界情况测试
     * 业务场景：测试各种边界情况和异常参数
     */
    @Test
    public void testScenario6_BoundaryConditions() {
        System.out.println("=== 测试场景6：边界情况测试 ===");

        // 测试1：无效时间范围格式
        System.out.println("测试1：无效时间范围格式");
        AvaiableAppointmentTimeDTO dto1 = new AvaiableAppointmentTimeDTO();
        dto1.setAvaiableTimeBegin(new Date());
        Calendar endCal = Calendar.getInstance();
        endCal.add(Calendar.DAY_OF_MONTH, 1);
        dto1.setAvaiableTimeEnd(endCal.getTime());
        dto1.setBeginTimeRange("invalid");  // 无效格式
        dto1.setEndTimeRange("18:00");
        dto1.setAdvanceAppointTime(null);
        dto1.setTimeWindow(30);

        List<AppointmentTimeRangeDTO> result1 = dto1.calculateAvailableTimeSlots();
        System.out.println("结果：生成 " + result1.size() + " 个时间段（应该为0）");
        System.out.println();

        // 测试2：null参数
        System.out.println("测试2：null参数");
        AvaiableAppointmentTimeDTO dto2 = new AvaiableAppointmentTimeDTO();
        dto2.setAvaiableTimeBegin(null);  // null开始时间
        dto2.setAvaiableTimeEnd(new Date());
        dto2.setBeginTimeRange("09:00");
        dto2.setEndTimeRange("18:00");

        List<AppointmentTimeRangeDTO> result2 = dto2.calculateAvailableTimeSlots();
        System.out.println("结果：生成 " + result2.size() + " 个时间段（应该为0）");
        System.out.println();

        // 测试3：时间范围颠倒
        System.out.println("测试3：开始时间晚于结束时间");
        AvaiableAppointmentTimeDTO dto3 = new AvaiableAppointmentTimeDTO();
        Calendar startCal = Calendar.getInstance();
        Calendar endCal3 = Calendar.getInstance();
        endCal3.add(Calendar.DAY_OF_MONTH, -1); // 结束时间早于开始时间

        dto3.setAvaiableTimeBegin(startCal.getTime());
        dto3.setAvaiableTimeEnd(endCal3.getTime());
        dto3.setBeginTimeRange("09:00");
        dto3.setEndTimeRange("18:00");
        dto3.setAdvanceAppointTime(null);
        dto3.setTimeWindow(30);

        List<AppointmentTimeRangeDTO> result3 = dto3.calculateAvailableTimeSlots();
        System.out.println("结果：生成 " + result3.size() + " 个时间段（应该为0）");
        System.out.println();

        // 测试4：极小时间窗口
        System.out.println("测试4：极小时间窗口（5分钟）");
        AvaiableAppointmentTimeDTO dto4 = new AvaiableAppointmentTimeDTO();
        dto4.setAvaiableTimeBegin(new Date());
        Calendar endCal4 = Calendar.getInstance();
        endCal4.add(Calendar.HOUR_OF_DAY, 2);
        dto4.setAvaiableTimeEnd(endCal4.getTime());
        dto4.setBeginTimeRange("10:00");
        dto4.setEndTimeRange("12:00");
        dto4.setAdvanceAppointTime(null);
        dto4.setImmediatelyAddTime(10);
        dto4.setTimeWindow(5);  // 5分钟窗口

        List<AppointmentTimeRangeDTO> result4 = dto4.calculateAvailableTimeSlots();
        System.out.println("结果：生成 " + result4.size() + " 个时间段");
        if (!result4.isEmpty()) {
            AppointmentTimeRangeDTO first = result4.get(0);
            long duration = (first.getEndTime().getTime() - first.getBeginTime().getTime()) / (1000 * 60);
            System.out.println("第一个时间段时长：" + duration + "分钟（立即预约应该是15分钟）");
        }
        System.out.println();
    }

    /**
     * 测试场景7：实际业务数据验证
     * 使用真实的业务参数进行验证
     */
    @Test
    public void testScenario7_RealBusinessData() {
        System.out.println("=== 测试场景7：实际业务数据验证 ===");

        // 模拟真实业务场景：周三下午3点用户下单检测服务
        Calendar realTime = Calendar.getInstance();
        realTime.set(2024, Calendar.JANUARY, 17, 15, 0, 0); // 2024-01-17 15:00:00 周三

        AvaiableAppointmentTimeDTO dto = new AvaiableAppointmentTimeDTO();
        dto.setAvaiableTimeBegin(realTime.getTime());

        Calendar endTime = (Calendar) realTime.clone();
        endTime.add(Calendar.DAY_OF_MONTH, 7); // 一周内
        dto.setAvaiableTimeEnd(endTime.getTime());

        dto.setBeginTimeRange("08:00");
        dto.setEndTimeRange("20:00");
        dto.setAdvanceAppointTime(null);       // 支持立即预约
        dto.setImmediatelyAddTime(60);         // 检测服务1小时
        dto.setTimeWindow(30);                 // 30分钟间隔

        List<AppointmentTimeRangeDTO> result = dto.calculateAvailableTimeSlots();

        System.out.println("业务场景：周三下午3点用户下单检测服务");
        System.out.println("配置：支持立即预约，检测时长1小时，30分钟间隔");
        System.out.println("时间范围：" + dateFormat.format(dto.getAvaiableTimeBegin()) +
                          " 到 " + dateFormat.format(dto.getAvaiableTimeEnd()));
        System.out.println();

        // 分析结果
        int immediateCount = 0;
        int regularCount = 0;

        for (AppointmentTimeRangeDTO slot : result) {
            if (slot.getIsImmediately()) {
                immediateCount++;
            } else {
                regularCount++;
            }
        }

        System.out.println("结果分析：");
        System.out.println("- 总时间段数：" + result.size());
        System.out.println("- 立即预约：" + immediateCount + " 个");
        System.out.println("- 常规预约：" + regularCount + " 个");
        System.out.println();

        // 显示今天剩余时间段
        System.out.println("今天剩余可预约时间段：");
        Calendar today = (Calendar) realTime.clone();
        today.set(Calendar.HOUR_OF_DAY, 23);
        today.set(Calendar.MINUTE, 59);

        int todayCount = 0;
        for (AppointmentTimeRangeDTO slot : result) {
            if (slot.getBeginTime().before(today.getTime())) {
                todayCount++;
                if (todayCount <= 10) { // 只显示前10个
                    String type = slot.getIsImmediately() ? "[立即]" : "[预约]";
                    System.out.println(type + " " + timeFormat.format(slot.getBeginTime()) +
                                     "-" + timeFormat.format(slot.getEndTime()));
                }
            }
        }
        System.out.println("今天共 " + todayCount + " 个时间段");
        System.out.println();
    }

    /**
     * 运行所有测试场景
     */
    @Test
    public void runAllTestScenarios() {
        System.out.println("开始运行所有测试场景...\n");

        testScenario1_TestServiceWithImmediateAppointment();
        testScenario2_CareServiceWithImmediateAppointment();
        testScenario3_AdvanceAppointmentRequired();
        testScenario4_NightServiceCrossDays();
        testScenario5_QuickService15Minutes();
        testScenario6_BoundaryConditions();
        testScenario7_RealBusinessData();

        System.out.println("所有测试场景运行完成！");
    }
}
