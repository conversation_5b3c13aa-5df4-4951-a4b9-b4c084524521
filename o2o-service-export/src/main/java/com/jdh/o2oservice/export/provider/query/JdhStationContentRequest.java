package com.jdh.o2oservice.export.provider.query;

import com.jdh.o2oservice.common.result.request.AbstractQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class JdhStationContentRequest extends AbstractQuery implements Serializable {
    private int pageNum = 1;

    private int pageSize = 10;
    /**
     * 内容id
     */
    private Long contentBizId;

    /**
     * 内容名称
     */
    private String contentName;

    /**
     * <pre>
     * 京东门店id
     * </pre>
     */
    private String stationId;

    /**
     * <pre>
     * 京东门店名称
     * </pre>
     */
    private String stationName;

    /**
     * <pre>
     * 项目id
     * </pre>
     */
    private Long serviceItemId;

    /**
     * <pre>
     * 项目名称
     * </pre>
     */
    private String serviceItemName;

    /**
     * <pre>
     * 实验室项目
     * </pre>
     */
    private List<JdhStationServiceItemRelRequest> serviceItemRels;

    /**
     * 创建人
     */
    private String createUser;
}