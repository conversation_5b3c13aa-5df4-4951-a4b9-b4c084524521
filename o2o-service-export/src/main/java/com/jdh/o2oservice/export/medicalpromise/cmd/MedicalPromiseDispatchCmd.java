package com.jdh.o2oservice.export.medicalpromise.cmd;

import com.jdh.o2oservice.common.result.request.AbstractBusinessIdentity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * @Description: 实验室派单CMD
 * @Author: wangpengfei144
 * @Date: 2024/4/15
 */
@Data
public class MedicalPromiseDispatchCmd extends AbstractBusinessIdentity implements Serializable {
    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 7032236311110608834L;

    /**
     * 业务身份
     */
    private String businessModeCode;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 地址ID
     */
    private String addressId;

    /**
     * 派送起始地址
     */
    private String startAddress;

    /**
     * 派送起始地址经度
     */
    private BigDecimal longitude;
    /**
     * 派送起始地址纬度
     */
    private BigDecimal latitude;
    /**
     * 派送起始地址省ID，京标
     */
    private Integer provinceId;

    /**
     * 派送起始地址城市ID,京标
     */
    private Integer cityId;

    /**
     * 履约人唯一IDList
     */
    private List<Long> promisePatientIdList;

    /**
     * 服务站Id
     */
    private String angelStationId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 预约天 yyyy-MM-dd
     */
    private String scheduleDay;

    /**
     * 预约时间 09:00-10:00
     */
    private String bookTimeSpan;
    /**
     * skuId
     */
    private Set<Long> serviceIdSet;

    /**
     * 操作信息
     */
    private String msg;
}
