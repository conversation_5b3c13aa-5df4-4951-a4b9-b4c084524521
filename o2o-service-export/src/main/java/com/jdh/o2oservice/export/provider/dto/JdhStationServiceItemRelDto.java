package com.jdh.o2oservice.export.provider.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <pre>
 *  门店项目关系表
 * </pre>
 */
@Data
public class JdhStationServiceItemRelDto {
    
    /**
     * <pre>
     * 京东门店id
     * </pre>
     */
    private String stationId;

    /**
     * 门店所在省份
     */
    private Integer provinceId;

    /**
     * 门店所在城市
     */
    private Integer cityId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;
    
    /**
     * <pre>
     * 项目id
     * </pre>
     */
    private Long serviceItemId;

    /**
     * <pre>
     * 项目名
     * </pre>
     */
    private String serviceItemName;

    /**
     * <pre>
     * 项目英文名
     * </pre>
     */
    private String serviceItemNameEn;
    
    /**
     * <pre>
     * 实验室结算价格,单位:元
     * </pre>
     */
    private BigDecimal settlementPrice;
    
    /**
     * <pre>
     * 耗材包id
     * </pre>
     */
    private Long materialPackageId;

    /**
     * <pre>
     * 耗材包id
     * </pre>
     */
    private String materialPackageName;
    
    /**
     * <pre>
     * 采样方式
     * </pre>
     */
    private String samplingWay;
    
    /**
     * <pre>
     * 样本类型 1鼻咽拭子采样、2唾液、3痰液、4粪便、5肛周拭子、6尿液、7指尖血、8干血斑、9阴道/宫颈采样拭子、10C13吹气袋（幽门螺旋杆菌检测）、11头发（带毛囊）、12静脉血
     * </pre>
     */
    private Integer sampleType;
    
    /**
     * <pre>
     * 检测方法学
     * </pre>
     */
    private Integer testWay;
    
    /**
     * <pre>
     * 样本保存时长,单位小时
     * </pre>
     */
    private Integer simplePreserveDuration;
    
    /**
     * <pre>
     * 样本量,数值及单位
     * </pre>
     */
    private String simpleNum;
    
    /**
     * <pre>
     * 服务时长,单位分钟
     * </pre>
     */
    private Integer serviceDuration;
    
    /**
     * <pre>
     * 样本存放要求
     * </pre>
     */
    private String simplePreserveCondition;
    
    /**
     * <pre>
     * 检测时长,单位分钟
     * </pre>
     */
    private Integer testDuration;
    
    /**
     * <pre>
     * 服务要求
     * </pre>
     */
    private String serviceCondition;
    
    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;


    /**
     * 渠道编号
     */
    private Long channelNo;

    /**
     * 服务门店状态（是否可用）
     */
    private Integer stationStatus;

    /**
     * 门店地址
     */
    private String stationAddr;

    /** 经度 */
    private String stationLng;

    /** 纬度 */
    private String stationLat;

    /** 门店营业时间 */
    private String stationHours;
    /**
     * 门店电话,可能多个
     */
    private String storePhone;

    /**
     * 天算渠道ID
     */
    private String channelRuleCode;

    /**
     * <pre>
     * 京东门店名称
     * </pre>
     */
    private String stationName;

    /**
     * 上下架状态
     */
    private Integer onOffShelf;

    /**
     * 上下架状态
     */
    private String onOffShelfName;

    /**
     * 设备业务序列id
     */
    private Long equipmentBizId;

    /**
     * 厂商id
     */
    private Long manufacturerId;

    /**
     * 设备型号
     */
    private String equipmentModel;

    /**
     * 内容业务序列id
     */
    private Long contentBizId;

    /**
     * 内容名称
     */
    private String contentName;
}