package com.jdh.o2oservice.export.trade.query;

import com.jdh.o2oservice.common.result.request.AbstractQuery;
import lombok.Data;

import java.util.List;

/**
 * OrderInfoQueryParam 订单信息入参
 *
 * <AUTHOR>
 * @version 2024/4/23 23:26
 **/
@Data
public class JdOrderSaveParam extends AbstractQuery {

    /**
     * 必需, 订单号
     */
    private String orderId;
    /**
     * 是否查询预售信息
     */
    private boolean searchPresaleInfo;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 合作方来源
     */
    private Integer partnerSource;

    /**
     * 外部渠道的订单号
     */
    private String partnerSourceOrderId;

    /**
     * 预约人信息
     */
    private List<Long> patientIds;

    /**
     * 预约时间
     */
    private AppointmentTimeParam appointmentTimeParam;

    /**
     * 渠道
     */
    private String channelName;

    /**
     * 意向护士
     */
    private IntendedNurseParam intendedNurse;

    /**
     * 服务人员升级是否选中 true-选中 false-未选中
     */
    private Boolean serviceUpgradeSelected = false;

    /**
     * 是否已有采样盒
     */
    private Boolean preSampleFlag = false;

}
