package com.jdh.o2oservice.export.via.query;

import com.jdh.o2oservice.common.result.request.AbstractQuery;
import lombok.Data;

/**
 * 页面配置查询
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
@Data
public class ViaPromiseDistanceRequest extends AbstractQuery {


    /**
     * 页面场景
     */
    private String scene;
    /**
     *
     */
    private String orderId;
    private String promiseId;

    private String aggregateStatus;

    private Long medicalPromiseId;//运单id
}
