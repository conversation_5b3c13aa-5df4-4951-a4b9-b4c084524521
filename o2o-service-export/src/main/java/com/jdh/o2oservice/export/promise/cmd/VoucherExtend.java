package com.jdh.o2oservice.export.promise.cmd;

import lombok.Data;

/**
 * VoucherExtend
 *
 * <AUTHOR>
 * @date 2024/01/17
 */
@Data
public class VoucherExtend {

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * autoPromise
     */
    private Boolean autoPromise;

    /**
     * skuId
     */
    private String skuId;

    /**
     * skuName
     */
    private String skuName;

    /**
     * 商家ID
     */
    private String venderId;

    /**
     * 草稿id
     */
    private String draftId;

    /**
     * 收获地址手机号
     */
    private String orderPhone;

    /**
     * 下单备注
     */
    private String orderRemark;

    /**
     * 履约人数
     */
    private Integer promisePatientNum;
    /**
     * 是否包含加项
     */
    private Integer hasAdded;

    /**
     * 赠品对应主商品sku
     */
    private Long mainSkuId;

    /**
     * 赠品对应主商品名称
     */
    private String mainSkuName;

    /**
     * 意向护士
     */
    private IntendedNurse intendedNurse;

    /**
     * 是否已有采样盒
     */
    private Boolean preSampleFlag;

    /**
     * 实验室开放平台扩展字段
     */
    private String openTestInfo;

}
