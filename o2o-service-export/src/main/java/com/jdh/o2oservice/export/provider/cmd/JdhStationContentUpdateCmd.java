package com.jdh.o2oservice.export.provider.cmd;

import com.jdh.o2oservice.export.provider.dto.ProviderContentStageDto;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <pre>
 *  实验项目内容表
 * </pre>
 */
@Data
public class JdhStationContentUpdateCmd {

    /**
     * 内容业务序列id
     */
    @NotNull(message = "内容ID不允许为空")
    private Long contentBizId;

    /**
     * 内容名称
     */
    @NotNull(message = "内容名称不允许为空")
    private String contentName;

    /**
     * 检测阶段
     */
    private ProviderContentStageDto checkStageDto;

    /**
     * 处理阶段
     */
    private ProviderContentStageDto processStageDto;

    /**
     * 上机阶段
     */
    private ProviderContentStageDto machineStageDto;

    /**
     * 审核阶段
     */
    private ProviderContentStageDto auditStageDto;

    /**
     * 报告阶段
     */
    private ProviderContentStageDto reportStageDto;

    /**
     * 修改人pin
     */
    private String updateUser;
}