package com.jdh.o2oservice.export.trade.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AppointmentTimeDTO 可预约上门时间
 *
 * <AUTHOR>
 * @version 2024/4/25 17:07
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AvaiableAppointmentTimeDTO implements Serializable {

    /**
     * 开始时间
     */
    private Date avaiableTimeBegin;
    /**
     * 结束时间
     */
    private Date avaiableTimeEnd;
    /**
     * 每日开始时段 格式 HH:mm
     */
    private String beginTimeRange;
    /**
     * 每日结束时段 格式 HH:mm
     */
    private String endTimeRange;
    /**
     * 提前预约时间
     */
    private Integer advanceAppointTime;

    /**
     * 立即预约场景下增加的时间，单位分钟；检测60分钟，护理120分钟
     */
    private Integer immediatelyAddTime;

    /**
     * 时间段窗口时间，单位分钟
     */
    private Integer timeWindow = 30;

    /**
     * 前端展示时间
     */
    private Map<String, Map<String, List<AppointmentTimeRangeDTO>>> displayDate;

    /**
     * 前端展示时间
     */
    private  List<XfylAppointDateDTO> compatibleGroupDTO;



    /**
     * 可约时间列表
     *
     * @return
     */
    public  List<AppointmentTimeRangeDTO> getAvaiableDateList(Boolean isCare, Integer timeWindow) {
        List<AppointmentTimeRangeDTO> result = new ArrayList<>();
        // 初始化每天的开始时段
        int initBeginHour = 0;
        // 初始化每天的结束时段
        int initEndHour = 24;
        if (beginTimeRange.matches("\\d{1,2}:\\d{1,2}")) {
            initBeginHour = Integer.parseInt(beginTimeRange.split(":")[0]);
        }
        if (endTimeRange.matches("\\d{1,2}:\\d{1,2}")) {
            initEndHour = Integer.parseInt(endTimeRange.split(":")[0]);
        }
        //特殊数据处理：23:59 默认处理为24
        if ("23:59".equals(endTimeRange)) {
            initEndHour = 24;
        }
        // 天纬度的循环
        Date cursor = avaiableTimeBegin;
        AppointmentTimeRangeDTO appointmentTimeRangeDTO = null;
        do {
            LocalDateTime cursorLocalTime = LocalDateTime.ofInstant(cursor.toInstant(), ZoneId.systemDefault());
            Calendar c = Calendar.getInstance();
            c.setTime(cursor);
            c.set(Calendar.SECOND, 0);
            // 今天
            if (cursor.getTime() == avaiableTimeBegin.getTime()) {
                // 立即预约在每日的开始结束时间内的情况；需要手工将立即预约放入时段，不通过循环加入
                appointmentTimeRangeDTO = new AppointmentTimeRangeDTO();
                if(isCare) {
                    c.add(Calendar.MINUTE, timeWindow - c.get(Calendar.MINUTE) % timeWindow);
                    if(c.get(Calendar.HOUR_OF_DAY) >= initBeginHour && c.get(Calendar.HOUR_OF_DAY) < initEndHour && (Objects.isNull(advanceAppointTime) || advanceAppointTime.intValue() == 0)){
                        appointmentTimeRangeDTO.setBeginTime(c.getTime());
                        appointmentTimeRangeDTO.setIsImmediately(true);
                        appointmentTimeRangeDTO.setBeginTime(c.getTime());
                        result.add(appointmentTimeRangeDTO);
                        c.add(Calendar.HOUR_OF_DAY, 2);
                    }else if(c.get(Calendar.HOUR_OF_DAY) < initBeginHour) {
                        c.set(Calendar.MINUTE, 0);
                    }
                }else {
                    if(c.get(Calendar.HOUR_OF_DAY) >= initBeginHour && c.get(Calendar.HOUR_OF_DAY) < initEndHour && (Objects.isNull(advanceAppointTime) || advanceAppointTime.intValue() == 0)){
                        appointmentTimeRangeDTO = new AppointmentTimeRangeDTO();
                        appointmentTimeRangeDTO.setIsImmediately(true);
                        appointmentTimeRangeDTO.setBeginTime(c.getTime());
                        result.add(appointmentTimeRangeDTO);
                        c.add(Calendar.HOUR_OF_DAY, 1);
                    }
                    c.set(Calendar.MINUTE, 0);
                }
            }

            //处理跨天的情况，如果跨天了直接切游标
            if(c.get(Calendar.DAY_OF_MONTH) <= cursorLocalTime.getDayOfMonth()) {
                int beginHourEveryDay = c.get(Calendar.HOUR_OF_DAY);
                if (beginHourEveryDay < initBeginHour) {
                    beginHourEveryDay = initBeginHour;
                }
                // 时段纬度的循环
                for (int i = beginHourEveryDay; i < initEndHour; i++) {
                    c.set(Calendar.HOUR_OF_DAY, i);
                    appointmentTimeRangeDTO = new AppointmentTimeRangeDTO();
                    appointmentTimeRangeDTO.setBeginTime(c.getTime());
                    result.add(appointmentTimeRangeDTO);
                }

                //护士护理场景处理最后一小时
                if(isCare && cursor.getTime() == avaiableTimeBegin.getTime()) {
                    if(c.get(Calendar.HOUR_OF_DAY) == initEndHour && c.get(Calendar.MINUTE) == 0) {
                        c.set(Calendar.HOUR_OF_DAY, initEndHour);
                        appointmentTimeRangeDTO = new AppointmentTimeRangeDTO();
                        appointmentTimeRangeDTO.setBeginTime(c.getTime());
                        result.add(appointmentTimeRangeDTO);
                    }
                }

                // 游标切到明天
                c.add(Calendar.DATE, 1);
                c.set(Calendar.HOUR_OF_DAY, 0);
                c.set(Calendar.MINUTE, 0);
                c.set(Calendar.SECOND, 0);
            }else if(c.get(Calendar.HOUR_OF_DAY) < initBeginHour){
                //跨天的情况判断下一个时间段是不是已经超过了起始时间
                c.set(Calendar.HOUR_OF_DAY, initBeginHour);
                c.set(Calendar.MINUTE, 0);
                c.set(Calendar.SECOND, 0);
            }else if(c.get(Calendar.HOUR_OF_DAY) > initEndHour){
                c.add(Calendar.DATE, 1);
                c.set(Calendar.HOUR_OF_DAY, initBeginHour);
                c.set(Calendar.MINUTE, 0);
                c.set(Calendar.SECOND, 0);
            }
            cursor = c.getTime();
        } while (cursor.getTime() < avaiableTimeEnd.getTime());
        return result;
    }

    /**
     * 可约时间列表
     *
     * @return
     */
    public static List<Date> getAvaiableDateList(String beginTimeRange, String endTimeRange, Date avaiableTimeBegin, Date avaiableTimeEnd) {
        List<Date> result = new ArrayList<>();
        // 初始化每天的开始时段
        int initBeginHour = 0;
        // 初始化每天的结束时段
        int initEndHour = 24;
        if (beginTimeRange.matches("\\d{1,2}:\\d{1,2}")) {
            initBeginHour = Integer.parseInt(beginTimeRange.split(":")[0]);
        }
        if (endTimeRange.matches("\\d{1,2}:\\d{1,2}")) {
            initEndHour = Integer.parseInt(endTimeRange.split(":")[0]);
        }
        // 天纬度的循环
        Date cursor = avaiableTimeBegin;
        do {
            Calendar c = Calendar.getInstance();
            c.setTime(cursor);

            // 今天
            if (cursor.getTime() == avaiableTimeBegin.getTime()) {
                // 首日的非整点(立即预约模式)，
                if (c.get(Calendar.MINUTE) > 0) {
                    // 立即预约在每日的开始结束时间内的情况；需要手工将立即预约放入时段，不通过循环加入
                    if (c.get(Calendar.HOUR_OF_DAY) >= initBeginHour && c.get(Calendar.HOUR_OF_DAY) < initEndHour) {
                        result.add(c.getTime());
                        // 重置开始时间为2小时后
                        c.add(Calendar.HOUR_OF_DAY, 2);
                    } else {
                        // 重置开始时间为2小时后
                        c.add(Calendar.HOUR_OF_DAY, 1);
                    }
                    c.set(Calendar.MINUTE, 0);
                    c.set(Calendar.SECOND, 0);
                }
            }
            int beginHourEveryDay = c.get(Calendar.HOUR_OF_DAY);
            if (beginHourEveryDay < initBeginHour) {
                beginHourEveryDay = initBeginHour;
            }
            // 时段纬度的循环
            for (int i = beginHourEveryDay; i < initEndHour; i++) {
                c.set(Calendar.HOUR_OF_DAY, i);
                result.add(c.getTime());
            }
            // 游标切到明天
            c.add(Calendar.DATE, 1);
            c.set(Calendar.HOUR_OF_DAY, 0);
            c.set(Calendar.MINUTE, 0);
            c.set(Calendar.SECOND, 0);
            cursor = c.getTime();
        } while (cursor.getTime() < avaiableTimeEnd.getTime());
        return result;
    }

    /**
     * 根据类中字段计算可约时间列表
     *
     * @return 可约时间列表
     */
    public List<AppointmentTimeRangeDTO> calculateAvailableTimeSlots() {
        List<AppointmentTimeRangeDTO> result = new ArrayList<>();

        // 参数校验
        if (avaiableTimeBegin == null || avaiableTimeEnd == null ||
            beginTimeRange == null || endTimeRange == null) {
            return result;
        }

        // 解析每日开始和结束时间
        int[] beginTime = parseTimeRange(beginTimeRange);
        int[] endTime = parseTimeRange(endTimeRange);

        if (beginTime == null || endTime == null) {
            return result;
        }

        int beginHour = beginTime[0];
        int beginMinute = beginTime[1];
        int endHour = endTime[0];
        int endMinute = endTime[1];

        // 特殊处理：23:59 视为24:00
        if (endHour == 23 && endMinute == 59) {
            endHour = 24;
            endMinute = 0;
        }

        // 使用系统当前时间作为计算起点
        Date currentTime = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentTime);

        // 如果当前时间早于可预约开始时间，则从可预约开始时间开始
        if (currentTime.before(avaiableTimeBegin)) {
            calendar.setTime(avaiableTimeBegin);
        }

        // 重置秒和毫秒
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // 根据advanceAppointTime决定处理逻辑
        if (advanceAppointTime == null) {
            // advanceAppointTime=null时，第一个可约时间为立即预约
            AppointmentTimeRangeDTO immediateSlot = createImmediateAppointmentSlot(calendar, beginHour, beginMinute, endHour, endMinute);
            if (immediateSlot != null) {
                result.add(immediateSlot);
                // 立即预约后，调整开始时间到立即预约结束时间
                calendar.setTime(immediateSlot.getEndTime());
            }
            // 生成后续常规时间段
            generateRegularTimeSlots(result, calendar, beginHour, beginMinute, endHour, endMinute);
        } else if (advanceAppointTime > 0) {
            // advanceAppointTime>0时，第一个可约时段是当前时间+advanceAppointTime分钟后的可约时段
            Calendar advanceCalendar = (Calendar) calendar.clone();
            advanceCalendar.add(Calendar.MINUTE, advanceAppointTime);

            // 生成从提前预约时间开始的常规时间段（非立即预约）
            generateRegularTimeSlots(result, advanceCalendar, beginHour, beginMinute, endHour, endMinute);
        } else {
            // advanceAppointTime=0时，支持立即预约（保持原有逻辑兼容性）
            AppointmentTimeRangeDTO immediateSlot = createImmediateAppointmentSlot(calendar, beginHour, beginMinute, endHour, endMinute);
            if (immediateSlot != null) {
                result.add(immediateSlot);
                calendar.setTime(immediateSlot.getEndTime());
            }
            generateRegularTimeSlots(result, calendar, beginHour, beginMinute, endHour, endMinute);
        }

        return result;
    }

    /**
     * 创建立即预约时间段
     */
    private AppointmentTimeRangeDTO createImmediateAppointmentSlot(Calendar calendar,
                                                                  int beginHour, int beginMinute,
                                                                  int endHour, int endMinute) {
        int currentHour = calendar.get(Calendar.HOUR_OF_DAY);
        int currentMinute = calendar.get(Calendar.MINUTE);
        Date currentTime = new Date();

        // 检查当前时间是否在每日可预约时间范围内
        if (!isTimeInRange(currentHour, currentMinute, beginHour, beginMinute, endHour, endMinute)) {
            return null;
        }

        // 确保立即预约时间段的开始时间大于当前时间
        if (!calendar.getTime().after(currentTime)) {
            return null;
        }

        AppointmentTimeRangeDTO immediateSlot = new AppointmentTimeRangeDTO();
        immediateSlot.setIsImmediately(true);
        immediateSlot.setBeginTime(calendar.getTime());

        // 计算结束时间：立即预约场景下，开始结束时间相差 immediatelyAddTime + timeWindow
        int totalMinutes = (immediatelyAddTime != null ? immediatelyAddTime : 0) +
                          (timeWindow != null ? timeWindow : 30);

        Calendar endCalendar = (Calendar) calendar.clone();
        endCalendar.add(Calendar.MINUTE, totalMinutes);
        immediateSlot.setEndTime(endCalendar.getTime());

        return immediateSlot;
    }

    /**
     * 生成常规时间段
     */
    private void generateRegularTimeSlots(List<AppointmentTimeRangeDTO> result, Calendar startCalendar,
                                        int beginHour, int beginMinute, int endHour, int endMinute) {
        Calendar calendar = (Calendar) startCalendar.clone();
        Date currentTime = new Date(); // 获取当前时间用于比较

        // 调整到下一个时间窗口的开始
        int windowMinutes = timeWindow != null ? timeWindow : 30;
        adjustToNextTimeWindow(calendar, windowMinutes);

        while (calendar.getTime().before(avaiableTimeEnd)) {
            int currentHour = calendar.get(Calendar.HOUR_OF_DAY);
            int currentMinute = calendar.get(Calendar.MINUTE);

            // 检查是否在每日可预约时间范围内
            if (isTimeInRange(currentHour, currentMinute, beginHour, beginMinute, endHour, endMinute)) {
                // 确保输出的可约时段必须大于当前时间
                if (calendar.getTime().after(currentTime)) {
                    AppointmentTimeRangeDTO timeSlot = new AppointmentTimeRangeDTO();
                    timeSlot.setIsImmediately(false);
                    timeSlot.setBeginTime(calendar.getTime());

                    // 计算结束时间：开始时间和结束时间相差 timeWindow
                    Calendar endCalendar = (Calendar) calendar.clone();
                    endCalendar.add(Calendar.MINUTE, windowMinutes);
                    timeSlot.setEndTime(endCalendar.getTime());

                    result.add(timeSlot);
                }
            }

            // 移动到下一个时间窗口
            calendar.add(Calendar.MINUTE, windowMinutes);

            // 如果超出了当天的结束时间，跳到下一天的开始时间
            if (!isTimeInRange(calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE),
                              beginHour, beginMinute, endHour, endMinute)) {
                moveToNextDayStart(calendar, beginHour, beginMinute);
            }
        }
    }

    /**
     * 解析时间范围字符串 (HH:mm)
     */
    private int[] parseTimeRange(String timeRange) {
        if (timeRange == null || !timeRange.matches("\\d{1,2}:\\d{2}")) {
            return null;
        }

        String[] parts = timeRange.split(":");
        try {
            int hour = Integer.parseInt(parts[0]);
            int minute = Integer.parseInt(parts[1]);
            return new int[]{hour, minute};
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 检查时间是否在指定范围内
     */
    private boolean isTimeInRange(int hour, int minute, int beginHour, int beginMinute, int endHour, int endMinute) {
        int currentMinutes = hour * 60 + minute;
        int beginMinutes = beginHour * 60 + beginMinute;
        int endMinutes = endHour * 60 + endMinute;

        return currentMinutes >= beginMinutes && currentMinutes < endMinutes;
    }

    /**
     * 调整到下一个时间窗口的开始
     */
    private void adjustToNextTimeWindow(Calendar calendar, int windowMinutes) {
        int currentMinute = calendar.get(Calendar.MINUTE);
        int remainder = currentMinute % windowMinutes;

        if (remainder != 0) {
            calendar.add(Calendar.MINUTE, windowMinutes - remainder);
        }
    }

    /**
     * 移动到下一天的开始时间
     */
    private void moveToNextDayStart(Calendar calendar, int beginHour, int beginMinute) {
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, beginHour);
        calendar.set(Calendar.MINUTE, beginMinute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }

    /**
     * 过滤时间
     * @param showTimeType
     */
    public void filterShowTime(Integer showTimeType) {
        if(CollectionUtils.isEmpty(compatibleGroupDTO)){
            return;
        }
        Iterator<XfylAppointDateDTO> dayIterator = compatibleGroupDTO.iterator();
        while (dayIterator.hasNext()) {
            XfylAppointDateDTO curDay = dayIterator.next();
            //遍历天
            if(CollectionUtils.isEmpty(curDay.getAppointDateTimeGroupDTOList())){
                //天没有上午/下午数据,则删除天
                dayIterator.remove();
                continue;
            }

            Iterator<XfylAppointDateTimeGroupDTO> iterator = curDay.getAppointDateTimeGroupDTOList().iterator();

            while(iterator.hasNext()){
                XfylAppointDateTimeGroupDTO curGroup = iterator.next();
                //遍历上午和下午
                if(CollectionUtils.isEmpty(curGroup.getDateTimeDTOList())){
                    //上午/下午没有可约时间,则删除上午/下午
                    iterator.remove();
                    continue;
                }
                //删除不可约数据
                curGroup.setDateTimeDTOList(curGroup.getDateTimeDTOList().stream().filter(t->t.getStatus().equals(showTimeType)).collect(Collectors.toList()));
                if(CollectionUtils.isEmpty(curGroup.getDateTimeDTOList())){
                    //上午/下午没有可约时间,则删除上午/下午
                    iterator.remove();
                }
            }

            if(CollectionUtils.isEmpty(curDay.getAppointDateTimeGroupDTOList())){
                //天没有上午/下午数据,则删除天
                dayIterator.remove();
            }
        }
    }
}