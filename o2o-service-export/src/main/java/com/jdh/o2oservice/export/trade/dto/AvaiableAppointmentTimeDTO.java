package com.jdh.o2oservice.export.trade.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AppointmentTimeDTO 可预约上门时间
 *
 * <AUTHOR>
 * @version 2024/4/25 17:07
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AvaiableAppointmentTimeDTO implements Serializable {

    /**
     * 开始时间
     */
    private Date avaiableTimeBegin;
    /**
     * 结束时间
     */
    private Date avaiableTimeEnd;
    /**
     * 每日开始时段 格式 HH:mm
     */
    private String beginTimeRange;
    /**
     * 每日结束时段 格式 HH:mm
     */
    private String endTimeRange;
    /**
     * 提前预约时间
     */
    private Integer advanceAppointTime;

    /**
     * 立即预约场景下增加的时间；检测1小时，护理2小时
     */
    private Integer immediatelyAddTime;

    /**
     * 时间段相差时间，单位分钟
     */
    private Integer timeWindow = 30;

    /**
     * 前端展示时间
     */
    private Map<String, Map<String, List<AppointmentTimeRangeDTO>>> displayDate;

    /**
     * 前端展示时间
     */
    private  List<XfylAppointDateDTO> compatibleGroupDTO;



    /**
     * 可约时间列表
     *
     * @return
     */
    public  List<AppointmentTimeRangeDTO> getAvaiableDateList(Boolean isCare, Integer timeWindow) {
        List<AppointmentTimeRangeDTO> result = new ArrayList<>();
        // 初始化每天的开始时段
        int initBeginHour = 0;
        // 初始化每天的结束时段
        int initEndHour = 24;
        if (beginTimeRange.matches("\\d{1,2}:\\d{1,2}")) {
            initBeginHour = Integer.parseInt(beginTimeRange.split(":")[0]);
        }
        if (endTimeRange.matches("\\d{1,2}:\\d{1,2}")) {
            initEndHour = Integer.parseInt(endTimeRange.split(":")[0]);
        }
        //特殊数据处理：23:59 默认处理为24
        if ("23:59".equals(endTimeRange)) {
            initEndHour = 24;
        }
        // 天纬度的循环
        Date cursor = avaiableTimeBegin;
        AppointmentTimeRangeDTO appointmentTimeRangeDTO = null;
        do {
            LocalDateTime cursorLocalTime = LocalDateTime.ofInstant(cursor.toInstant(), ZoneId.systemDefault());
            Calendar c = Calendar.getInstance();
            c.setTime(cursor);
            c.set(Calendar.SECOND, 0);
            // 今天
            if (cursor.getTime() == avaiableTimeBegin.getTime()) {
                // 立即预约在每日的开始结束时间内的情况；需要手工将立即预约放入时段，不通过循环加入
                appointmentTimeRangeDTO = new AppointmentTimeRangeDTO();
                if(isCare) {
                    c.add(Calendar.MINUTE, timeWindow - c.get(Calendar.MINUTE) % timeWindow);
                    if(c.get(Calendar.HOUR_OF_DAY) >= initBeginHour && c.get(Calendar.HOUR_OF_DAY) < initEndHour && (Objects.isNull(advanceAppointTime) || advanceAppointTime.intValue() == 0)){
                        appointmentTimeRangeDTO.setBeginTime(c.getTime());
                        appointmentTimeRangeDTO.setIsImmediately(true);
                        appointmentTimeRangeDTO.setBeginTime(c.getTime());
                        result.add(appointmentTimeRangeDTO);
                        c.add(Calendar.HOUR_OF_DAY, 2);
                    }else if(c.get(Calendar.HOUR_OF_DAY) < initBeginHour) {
                        c.set(Calendar.MINUTE, 0);
                    }
                }else {
                    if(c.get(Calendar.HOUR_OF_DAY) >= initBeginHour && c.get(Calendar.HOUR_OF_DAY) < initEndHour && (Objects.isNull(advanceAppointTime) || advanceAppointTime.intValue() == 0)){
                        appointmentTimeRangeDTO = new AppointmentTimeRangeDTO();
                        appointmentTimeRangeDTO.setIsImmediately(true);
                        appointmentTimeRangeDTO.setBeginTime(c.getTime());
                        result.add(appointmentTimeRangeDTO);
                        c.add(Calendar.HOUR_OF_DAY, 1);
                    }
                    c.set(Calendar.MINUTE, 0);
                }
            }

            //处理跨天的情况，如果跨天了直接切游标
            if(c.get(Calendar.DAY_OF_MONTH) <= cursorLocalTime.getDayOfMonth()) {
                int beginHourEveryDay = c.get(Calendar.HOUR_OF_DAY);
                if (beginHourEveryDay < initBeginHour) {
                    beginHourEveryDay = initBeginHour;
                }
                // 时段纬度的循环
                for (int i = beginHourEveryDay; i < initEndHour; i++) {
                    c.set(Calendar.HOUR_OF_DAY, i);
                    appointmentTimeRangeDTO = new AppointmentTimeRangeDTO();
                    appointmentTimeRangeDTO.setBeginTime(c.getTime());
                    result.add(appointmentTimeRangeDTO);
                }

                //护士护理场景处理最后一小时
                if(isCare && cursor.getTime() == avaiableTimeBegin.getTime()) {
                    if(c.get(Calendar.HOUR_OF_DAY) == initEndHour && c.get(Calendar.MINUTE) == 0) {
                        c.set(Calendar.HOUR_OF_DAY, initEndHour);
                        appointmentTimeRangeDTO = new AppointmentTimeRangeDTO();
                        appointmentTimeRangeDTO.setBeginTime(c.getTime());
                        result.add(appointmentTimeRangeDTO);
                    }
                }

                // 游标切到明天
                c.add(Calendar.DATE, 1);
                c.set(Calendar.HOUR_OF_DAY, 0);
                c.set(Calendar.MINUTE, 0);
                c.set(Calendar.SECOND, 0);
            }else if(c.get(Calendar.HOUR_OF_DAY) < initBeginHour){
                //跨天的情况判断下一个时间段是不是已经超过了起始时间
                c.set(Calendar.HOUR_OF_DAY, initBeginHour);
                c.set(Calendar.MINUTE, 0);
                c.set(Calendar.SECOND, 0);
            }else if(c.get(Calendar.HOUR_OF_DAY) > initEndHour){
                c.add(Calendar.DATE, 1);
                c.set(Calendar.HOUR_OF_DAY, initBeginHour);
                c.set(Calendar.MINUTE, 0);
                c.set(Calendar.SECOND, 0);
            }
            cursor = c.getTime();
        } while (cursor.getTime() < avaiableTimeEnd.getTime());
        return result;
    }

    /**
     * 可约时间列表
     *
     * @return
     */
    public static List<Date> getAvaiableDateList(String beginTimeRange, String endTimeRange, Date avaiableTimeBegin, Date avaiableTimeEnd) {
        List<Date> result = new ArrayList<>();
        // 初始化每天的开始时段
        int initBeginHour = 0;
        // 初始化每天的结束时段
        int initEndHour = 24;
        if (beginTimeRange.matches("\\d{1,2}:\\d{1,2}")) {
            initBeginHour = Integer.parseInt(beginTimeRange.split(":")[0]);
        }
        if (endTimeRange.matches("\\d{1,2}:\\d{1,2}")) {
            initEndHour = Integer.parseInt(endTimeRange.split(":")[0]);
        }
        // 天纬度的循环
        Date cursor = avaiableTimeBegin;
        do {
            Calendar c = Calendar.getInstance();
            c.setTime(cursor);

            // 今天
            if (cursor.getTime() == avaiableTimeBegin.getTime()) {
                // 首日的非整点(立即预约模式)，
                if (c.get(Calendar.MINUTE) > 0) {
                    // 立即预约在每日的开始结束时间内的情况；需要手工将立即预约放入时段，不通过循环加入
                    if (c.get(Calendar.HOUR_OF_DAY) >= initBeginHour && c.get(Calendar.HOUR_OF_DAY) < initEndHour) {
                        result.add(c.getTime());
                        // 重置开始时间为2小时后
                        c.add(Calendar.HOUR_OF_DAY, 2);
                    } else {
                        // 重置开始时间为2小时后
                        c.add(Calendar.HOUR_OF_DAY, 1);
                    }
                    c.set(Calendar.MINUTE, 0);
                    c.set(Calendar.SECOND, 0);
                }
            }
            int beginHourEveryDay = c.get(Calendar.HOUR_OF_DAY);
            if (beginHourEveryDay < initBeginHour) {
                beginHourEveryDay = initBeginHour;
            }
            // 时段纬度的循环
            for (int i = beginHourEveryDay; i < initEndHour; i++) {
                c.set(Calendar.HOUR_OF_DAY, i);
                result.add(c.getTime());
            }
            // 游标切到明天
            c.add(Calendar.DATE, 1);
            c.set(Calendar.HOUR_OF_DAY, 0);
            c.set(Calendar.MINUTE, 0);
            c.set(Calendar.SECOND, 0);
            cursor = c.getTime();
        } while (cursor.getTime() < avaiableTimeEnd.getTime());
        return result;
    }

    /**
     * 过滤时间
     * @param showTimeType
     */
    public void filterShowTime(Integer showTimeType) {
        if(CollectionUtils.isEmpty(compatibleGroupDTO)){
            return;
        }
        Iterator<XfylAppointDateDTO> dayIterator = compatibleGroupDTO.iterator();
        while (dayIterator.hasNext()) {
            XfylAppointDateDTO curDay = dayIterator.next();
            //遍历天
            if(CollectionUtils.isEmpty(curDay.getAppointDateTimeGroupDTOList())){
                //天没有上午/下午数据,则删除天
                dayIterator.remove();
                continue;
            }

            Iterator<XfylAppointDateTimeGroupDTO> iterator = curDay.getAppointDateTimeGroupDTOList().iterator();

            while(iterator.hasNext()){
                XfylAppointDateTimeGroupDTO curGroup = iterator.next();
                //遍历上午和下午
                if(CollectionUtils.isEmpty(curGroup.getDateTimeDTOList())){
                    //上午/下午没有可约时间,则删除上午/下午
                    iterator.remove();
                    continue;
                }
                //删除不可约数据
                curGroup.setDateTimeDTOList(curGroup.getDateTimeDTOList().stream().filter(t->t.getStatus().equals(showTimeType)).collect(Collectors.toList()));
                if(CollectionUtils.isEmpty(curGroup.getDateTimeDTOList())){
                    //上午/下午没有可约时间,则删除上午/下午
                    iterator.remove();
                }
            }

            if(CollectionUtils.isEmpty(curDay.getAppointDateTimeGroupDTOList())){
                //天没有上午/下午数据,则删除天
                dayIterator.remove();
            }
        }
    }
}