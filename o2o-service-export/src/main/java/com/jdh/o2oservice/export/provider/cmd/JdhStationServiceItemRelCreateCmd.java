package com.jdh.o2oservice.export.provider.cmd;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <pre>
 *  门店项目关系表
 * </pre>
 */
@Data
public class JdhStationServiceItemRelCreateCmd {
    
    /**
     * <pre>
     * 京东门店id
     * </pre>
     */
    @NotNull(message = "门店id不允许为空")
    private String stationId;
    
    /**
     * <pre>
     * 项目id
     * </pre>
     */
    @NotNull(message = "项目id不允许为空")
    private Long serviceItemId;

    /**
     * <pre>
     * 项目名
     * </pre>
     */
    private String serviceItemName;

    /**
     * <pre>
     * 项目英文名
     * </pre>
     */
    private String serviceItemNameEn;
    
    /**
     * <pre>
     * 实验室结算价格,单位:元
     * </pre>
     */
    @NotNull(message = "实验室结算价格不允许为空")
    @Min(value = 0, message = "实验室结算价格不允许为负数")
    private BigDecimal settlementPrice;
    
    /**
     * <pre>
     * 耗材包id
     * </pre>
     */
    @NotNull(message = "耗材包不允许为空")
    private Long materialPackageId;
    
    /**
     * <pre>
     * 采样方式
     * </pre>
     */
    @NotNull(message = "采样方式不允许为空")
    private String samplingWay;
    
    /**
     * <pre>
     * 样本类型 1鼻咽拭子采样、2唾液、3痰液、4粪便、5肛周拭子、6尿液、7指尖血、8干血斑、9阴道/宫颈采样拭子、10C13吹气袋（幽门螺旋杆菌检测）、11头发（带毛囊）、12静脉血
     * </pre>
     */
    @NotNull(message = "样本类型不允许为空")
    private Integer sampleType;
    
    /**
     * <pre>
     * 检测方法学
     * </pre>
     */
    @NotNull(message = "检测方法学不允许为空")
    private Integer testWay;
    
    /**
     * <pre>
     * 样本保存时长,单位小时
     * </pre>
     */
    @NotNull(message = "样本保存时长不允许为空")
    @Min(value = 1, message = "样本保存时长必须为正整数")
    private Integer simplePreserveDuration;
    
    /**
     * <pre>
     * 样本量,数值及单位
     * </pre>
     */
    @NotNull(message = "样本量不允许为空")
    private String simpleNum;
    
    /**
     * <pre>
     * 服务时长,单位分钟
     * </pre>
     */
    @NotNull(message = "服务时长不允许为空")
    @Min(value = 1, message = "服务时长必须为正整数")
    private Integer serviceDuration;
    
    /**
     * <pre>
     * 样本存放要求
     * </pre>
     */
    @NotNull(message = "样本存放要求不允许为空")
    private String simplePreserveCondition;
    
    /**
     * <pre>
     * 检测时长,单位分钟
     * </pre>
     */
    @NotNull(message = "检测时长不允许为空")
    @Min(value = 1, message = "检测时长必须为正整数")
    private Integer testDuration;
    
    /**
     * <pre>
     * 服务要求
     * </pre>
     */
    @NotNull(message = "服务要求不允许为空")
    private String serviceCondition;
    
    /**
     * <pre>
     * 备注
     * </pre>
     */
    @NotNull(message = "备注不允许为空")
    private String remark;

    /**
     * 上下架状态
     */
    private Integer onOffShelf;

    /**
     * 设备业务序列id
     */
    private Long equipmentBizId;

    /**
     * 内容业务序列id
     */
    private Long contentBizId;
}