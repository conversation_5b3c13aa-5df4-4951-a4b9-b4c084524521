package com.jdh.o2oservice.export.medicalpromise.dto;

import com.jdh.o2oservice.export.angelpromise.dto.AngelShipDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 履约检测单DTO
 * @date 2024-04-15 16:12
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MedicalPromiseDTO {
    /**
     * 检测单ID
     */
    private Long medicalPromiseId;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 服务ID，快检情况下是skuNo
     */
    private Long serviceId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务单ID
     */
    private Long voucherId;

    /**
     * 患者ID
     */
    private Long promisePatientId;

    /**
     * 检测单状态：详见 MedicalPromiseStatusEnum
     */
    private Integer status;

    /**
     * 样本条码
     */
    private String specimenCode;

    /**
     * 检测项目信息
     */
    private String serviceItemId;

    /**
     * 检测项目名称
     */
    private String serviceItemName;

    /**
     * 供应商渠道编码
     */
    private Long providerId;

    /**
     * 实验室id
     */
    private String stationId;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 实验室联系方式
     */
    private String stationPhone;

    /**
     * 服务地点详细地址
     */
    private String stationAddress;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 检测时间
     */
    private Date checkTime;

    /**
     * 是否冻结，0.不冻结，1.冻结
     */
    private Integer freeze;

    /**
     * <pre>
     * 预计检测时长,单位分钟
     * </pre>
     */
    private Integer testDuration;

    /**
     * 报告状态，1.已出报告，0.未出报告
     */
    private Integer reportStatus;

    /**
     * 是否是测试单
     */
    private Boolean devMedicalPromise = Boolean.FALSE;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 婚否
     */
    private Integer marriage;

    /**
     * 姓名
     */
    private String name;

    /**
     * phone
     */
    private String phone;

    /**
     * 亲属类型:1本人 21-父母 22-配偶 23-子女 34-其他
     */
    private Integer relativesType;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * appointmentAddress
     */
    private String appointmentAddress;
    /**
     * 结算状态 0-结算，1-已结算
     */
    private Integer settleStatus;
    /**
     *
     */
    private Date reportTime;

    /**
     * 服务站Id
     */
    private String angelStationId;

    /**
     * 预约开始时间
     * 格式 yyyy-MM-dd HH:mm
     */
    protected Date appointmentStartTime;

    /**
     * 预约结束时间
     * 格式 yyyy-MM-dd HH:mm
     */
    protected Date appointmentEndTime;

    /**
     * 检测单特殊标记（0.普通订单，1.加项订单）
     */
    private String flag;

    /**
     * 检测单特殊标记（0.普通订单，1.加项订单）
     */
    private String flagDesc;

    /** 合管检测单ID */
    private Long mergeMedicalId;

    /**
     * 录音详情
     */
    private Boolean recordingDetails = false;

    /**
     * 是否测试单 1是 0 null 不是
     */
    private Integer isTest;

    /**
     * 待收样超时绝对时间
     */
    private Date waitingTestTimeOutDate;

    /**
     * 是否待收样超时
     * 0 - 否 1 - 是
     */
    private Integer waitingTestTimeOutStatus;

    /**
     * 检测超时绝对时间
     */
    private Date testingTimeOutDate;

    /**
     * 是否检测超时
     * 0 - 否 1 - 是
     */
    private Integer testingTimeOutStatus;
    /**
     * eta数据
     */
    private MedPromiseEtaDTO medPromiseEtaDTO;

    private String statusDesc;

    /**
     * <pre>
     * 采样方式
     * </pre>
     */
    private String samplingWay;

    /**
     * <pre>
     * 样本类型 1鼻咽拭子采样、2唾液、3痰液、4粪便、5肛周拭子、6尿液、7指尖血、8干血斑、9阴道/宫颈采样拭子、10C13吹气袋（幽门螺旋杆菌检测）、11头发（带毛囊）、12静脉血
     * </pre>
     */
    private Integer sampleType;

    /**
     * 报告显示类型,1.结构化，2.PDF
     */
    private Integer reportShowType;

    /**
     * 检测单配送步骤
     */
    private List<MedPromiseDeliveryStepDTO> deliveryStepFlow;

    /**
     * 采样人
     */
    private String samplingTechnician;

    /**
     * 采样时间
     */
    private Date samplingTime;

    /**
     * 开单人
     */
    private String orderingPhysician;

    /**
     * 开单时间
     */
    private Date orderingTime;

    private String itemNameToC;//项目C端展示名称


    //
    private Integer normalFlag;

    private AngelShipDto angelShipDto;//运单数据

    /**
     * 流转码
     */
    private String circulationCode;

    /**
     * 检测单的子状态
     */
    private Integer subStatus;

    /**
     * 异常记录信息
     */
    private String exceptionRecord;

}
