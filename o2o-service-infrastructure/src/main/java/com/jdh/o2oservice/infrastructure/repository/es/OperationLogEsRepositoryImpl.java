package com.jdh.o2oservice.infrastructure.repository.es;

import com.jdh.o2oservice.base.enums.EnvEnum;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicatorES;
import com.jdh.o2oservice.core.domain.support.operationlog.model.OperationLogEs;
import com.jdh.o2oservice.core.domain.support.operationlog.repository.OperationLogEsRepository;
import com.jdh.o2oservice.core.domain.trade.repository.es.JdhReportIndicatorEsRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 18:13
 * @Desc :
 */
@Slf4j
@Repository
public class OperationLogEsRepositoryImpl implements OperationLogEsRepository {

    /**
     * defaultIndexName
     */
    private final static String defaultIndexName = "operation_log_index";

    /**
     * indexName
     */
    private static String indexName = "operation_log_index";
    /**
     * typeName
     */
    private final static String typeName = "doc";

    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        if(!EnvEnum.PRODUCTION.getCode().equals(value)){
            indexName = defaultIndexName + "_" +value;
        } else {
            indexName = defaultIndexName;
        }
    }

    @Resource
    private EsClientFactoryHealthcare esClientFactoryHealthcare;

    /**
     * 保存
     *
     * @param medicalReportIndicatorES@return
     */
    @Override
    public Boolean save(OperationLogEs operationLogEs) {
        try {
            return true;
        }catch (Exception e){
            log.error("OperationLogEsRepositoryImpl.save error",e);
            throw new RuntimeException(e.getMessage());
        }
    }
}
