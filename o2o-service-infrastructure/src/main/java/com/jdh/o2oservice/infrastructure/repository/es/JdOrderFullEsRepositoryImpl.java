package com.jdh.o2oservice.infrastructure.repository.es;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.EnvEnum;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderFullPageContext;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderIdContext;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderFull;
import com.jdh.o2oservice.core.domain.trade.repository.es.JdOrderFullEsRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.client.transport.TransportClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 18:13
 * @Desc :
 */
@Slf4j
@Repository
public class JdOrderFullEsRepositoryImpl implements JdOrderFullEsRepository {

    /**
     * defaultIndexName
     */
    private final static String defaultIndexName = "jd_order_full_index";

    /**
     * indexName
     */
    private static String indexName = "jd_order_full_index";
    /**
     * typeName
     */
    private final static String typeName = "doc";

    /**
     * commonDuccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        if(!EnvEnum.PRODUCTION.getCode().equals(value)){
            indexName = defaultIndexName + "_" +value;
        } else {
            indexName = defaultIndexName;
        }
    }

    @Autowired
    private EsClientFactoryHealthcare esClientFactoryHealthcare;

    @Override
    public List<JdOrderFull> queryByOrderId(JdOrderIdContext jdOrderIdContext) {
        //组装查询条件
        QueryBuilder queryBuilder = buildQueryBuilder(jdOrderIdContext);

        //执行查询条件
        SearchRequestBuilder requestBuilder = esClientFactoryHealthcare.getClient().prepareSearch(indexName).setTypes(typeName)
                .setSearchType(SearchType.QUERY_THEN_FETCH).setQuery(queryBuilder).setFrom(0).setSize(200)
                .setTimeout(new TimeValue(EsClientFactoryHealthcare.TIMEOUT_MILLIS, TimeUnit.MILLISECONDS));
        if(CollectionUtils.isNotEmpty(jdOrderIdContext.getIncludes())){
            // 指定返回的字段
            String[] includes = jdOrderIdContext.getIncludes().toArray(new String[0]);
            requestBuilder.setFetchSource(includes, null);
        }

        SearchResponse response = requestBuilder.execute().actionGet(EsClientFactoryHealthcare.TIMEOUT_MILLIS);
        //拼装返回值
        return convert2JdOrderFullList(response);
    }

    @Override
    public Boolean save(JdOrderFull jdOrderFull) {
        try {
            jdOrderFull.setEsModified(new Date());
            esClientFactoryHealthcare.updateOrInsertDocument( indexName, typeName, jdOrderFull.getId(), jdOrderFull);
            return true;
        }catch (Exception e){
            log.error("JdOrderFullEsRepositoryImpl.save error",e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public JdOrderFull getById(String id) {
        try{
            GetResponse response = esClientFactoryHealthcare.getDocument(indexName, typeName, id);
            String json = response.getSourceAsString();
            return JSON.parseObject(json, JdOrderFull.class);
        }catch (Exception e){
            log.error("JdOrderFullEsRepositoryImpl.getById error",e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public void deleteById(List<String> orderEsIdList) {
        if(CollectionUtils.isEmpty(orderEsIdList)){
            return;
        }
        try{
            Boolean res = esClientFactoryHealthcare.deleteBatch(indexName, typeName, orderEsIdList);
            log.info("JdOrderFullEsRepositoryImpl.deleteById res={}",res);
        }catch (Exception e){
            log.error("JdOrderFullEsRepositoryImpl.deleteById error",e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public PageDto<JdOrderFull> queryPage(JdOrderFullPageContext jdOrderFullPageContext) {
        log.info("JdOrderFullEsRepositoryImpl.queryPage --> indexName={}", indexName);
        if(jdOrderFullPageContext.getPageNum() * jdOrderFullPageContext.getPageSize() > 8000){
            throw new RuntimeException("不允许深度分页，请调整分页参数");
        }

        int from = (jdOrderFullPageContext.getPageNum() - 1) * jdOrderFullPageContext.getPageSize();

        //组装查询条件
        QueryBuilder queryBuilder = buildPageQueryBuilder(jdOrderFullPageContext);

        //执行查询条件
        SearchRequestBuilder requestBuilder = esClientFactoryHealthcare.getClient().prepareSearch(indexName).setTypes(typeName)
                .setSearchType(SearchType.QUERY_THEN_FETCH).setQuery(queryBuilder).setFrom(from).setSize(jdOrderFullPageContext.getPageSize())
                .setTimeout(new TimeValue(EsClientFactoryHealthcare.TIMEOUT_MILLIS, TimeUnit.MILLISECONDS));
        requestBuilder.addSort("orderCreateTime", SortOrder.DESC);
        log.info("JdOrderFullEsRepositoryImpl.queryPage --> indexName={} requestBuilder={}", indexName, requestBuilder.toString());

        SearchResponse response = requestBuilder.execute().actionGet(EsClientFactoryHealthcare.TIMEOUT_MILLIS);
        //拼装返回值
        return convert2JdOrderFullPage(response,jdOrderFullPageContext.getPageNum(), jdOrderFullPageContext.getPageSize());
    }

    @Override
    public List<JdOrderFull> queryByPromiseId(String promiseId) {
        if(StringUtils.isBlank(promiseId)){
            return Lists.newArrayList();
        }
        //组装查询条件
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("promiseId", promiseId));//promiseId

        //执行查询条件
        SearchRequestBuilder requestBuilder = esClientFactoryHealthcare.getClient().prepareSearch(indexName).setTypes(typeName)
                .setSearchType(SearchType.QUERY_THEN_FETCH).setQuery(queryBuilder).setFrom(0).setSize(200)
                .setTimeout(new TimeValue(EsClientFactoryHealthcare.TIMEOUT_MILLIS, TimeUnit.MILLISECONDS));

        SearchResponse response = requestBuilder.execute().actionGet(EsClientFactoryHealthcare.TIMEOUT_MILLIS);
        //拼装返回值
        return convert2JdOrderFullList(response);
    }

    /**
     * 滚动查询
     *
     * @param jdOrderFullPageContext
     * @return
     */
    @Override
    public List<JdOrderFull> scrollQuery(JdOrderFullPageContext jdOrderFullPageContext) {
        TransportClient client = null;
        String scrollId = null;
        try {
            //组装查询条件
            QueryBuilder queryBuilder = buildPageQueryBuilder(jdOrderFullPageContext);
            log.info("JdOrderFullApplicationImpl->scrollQuery,queryBuilder={}", queryBuilder.toString());

            TimeValue keepAlive = TimeValue.timeValueMinutes(1);
            client = esClientFactoryHealthcare.getClient();

            //执行查询条件
            SearchResponse scrollResp = client.prepareSearch(indexName).setTypes(typeName)
                    .setSearchType(SearchType.QUERY_THEN_FETCH).setQuery(queryBuilder).addSort("orderCreateTime", SortOrder.DESC).setScroll(keepAlive).get();
            long count = scrollResp.getHits().getTotalHits();
            log.info("JdOrderFullApplicationImpl->scrollQuery,count={}", count);
            if (count > duccConfig.getExportMedicalPromiseInfoLimit()) {
                throw new BusinessException(SupportErrorCode.SUPPORT_FILE_DOWN_LIMIT.formatDescription(duccConfig.getExportMedicalPromiseInfoLimit()));
            }
            scrollId = scrollResp.getScrollId();
            List<Future<Boolean>> futures = new ArrayList<>();
            CopyOnWriteArrayList<JdOrderFull> res = new CopyOnWriteArrayList<>();
            while (count > 0) {
                SearchHits searchHits = scrollResp.getHits();
                log.info("JdOrderFullEsRepositoryImpl -> scrollQuery searchHits.length={} ", searchHits.getHits().length);
                Future<Boolean> future = CompletableFuture.supplyAsync(() ->  buildEsOrder(searchHits, res));
                futures.add(future);
                scrollResp = client.prepareSearchScroll(scrollId)
                        .setScroll(keepAlive)
                        .get();
                count = scrollResp.getHits().getHits().length;
            }

            // 等待全部处理结束
            for (Future<Boolean> f : futures){
                f.get();
            }
            return res;
        } catch (Exception e) {
            log.error("JdOrderFullEsRepositoryImpl.scrollQuery error", e);
            throw new BusinessException(SystemErrorCode.SYSTEM_ERROR);
        } finally {
            if(Objects.nonNull(client)){
                client.prepareClearScroll().addScrollId(scrollId).get();
            }
        }
    }

    /**
     * 校验导出数量
     *
     * @param jdOrderFullPageContext
     * @return
     */
    @Override
    public Long queryCount(JdOrderFullPageContext jdOrderFullPageContext) {
        //组装查询条件
        QueryBuilder queryBuilder = buildPageQueryBuilder(jdOrderFullPageContext);
        //执行查询条件
        SearchRequestBuilder requestBuilder = esClientFactoryHealthcare.getClient().prepareSearch(indexName).setTypes(typeName)
                .setSearchType(SearchType.QUERY_THEN_FETCH).setQuery(queryBuilder).setFrom(0).setSize(1)
                .setTimeout(new TimeValue(EsClientFactoryHealthcare.TIMEOUT_MILLIS, TimeUnit.MILLISECONDS));
        SearchResponse response = requestBuilder.execute().actionGet(EsClientFactoryHealthcare.TIMEOUT_MILLIS);
        return response.getHits().getTotalHits();
    }

    private PageDto<JdOrderFull> convert2JdOrderFullPage(SearchResponse response, int pageNum, int pageSize) {
        PageDto<JdOrderFull> resultPage = new PageDto<>();
        resultPage.setPageNum(pageNum);
        resultPage.setPageSize(pageSize);
        List<JdOrderFull> dtoList = convert2JdOrderFullList(response);
        resultPage.setTotalCount(response.getHits().totalHits);
        long totalPage = response.getHits().totalHits % pageSize == 0 ? response.getHits().totalHits / pageSize : response.getHits().totalHits / pageSize + 1 ;
        resultPage.setTotalPage(totalPage);
        resultPage.setList(dtoList);
        return resultPage;
    }

    private QueryBuilder buildPageQueryBuilder(JdOrderFullPageContext context) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if(StringUtils.isNotBlank(context.getPartnerSource())){
            queryBuilder.must(QueryBuilders.termQuery("partnerSource", context.getPartnerSource()));
        }
        if(StringUtils.isNotBlank(context.getVerticalCode())){
            queryBuilder.must(QueryBuilders.termQuery("verticalCode", context.getVerticalCode()));
        }
        if(StringUtils.isNotBlank(context.getServiceType())){

            //1 骑手上门检测 2 护士上门检测  3 护士上门护理服务
            if("3".equals(context.getServiceType())){
                queryBuilder.must(QueryBuilders.termQuery("serviceType", ServiceHomeTypeEnum.NH_HOME_CARE2.getServiceType()));
            }else if("1".equals(context.getServiceType())){
                queryBuilder.must(QueryBuilders.termsQuery("verticalCode", ServiceHomeTypeEnum.XFYL_HOME_TEST_PHASE1.getVerticalCode(),
                         ServiceHomeTypeEnum.NH_HOME_TEST_PHASE1.getVerticalCode()));
                queryBuilder.must(QueryBuilders.termQuery("serviceType", ServiceHomeTypeEnum.XFYL_HOME_TEST_PHASE1.getServiceType()));
            }else if("2".equals(context.getServiceType())){
                queryBuilder.must(QueryBuilders.termsQuery("verticalCode", ServiceHomeTypeEnum.XFYL_HOME_TEST1.getVerticalCode(),
                        ServiceHomeTypeEnum.NH_HOME_TEST1.getVerticalCode(), ServiceHomeTypeEnum.HOSPITAL_PAID_GUIDANCE_A_HOME_TEST.getVerticalCode()));
                queryBuilder.must(QueryBuilders.termQuery("serviceType", ServiceHomeTypeEnum.NH_HOME_TEST_PHASE1.getServiceType()));
            }else if("4".equals(context.getServiceType())){
                queryBuilder.must(QueryBuilders.termsQuery("verticalCode", ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getVerticalCode()));
                queryBuilder.must(QueryBuilders.termQuery("serviceType", ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getServiceType()));
            }
        }
        if(StringUtils.isNotBlank(context.getOrderId())){//查询订单或父订单
            BoolQueryBuilder subQueryBuilder = QueryBuilders.boolQuery();
            subQueryBuilder.should(QueryBuilders.termQuery("orderId", context.getOrderId()));
            subQueryBuilder.should(QueryBuilders.termQuery("parentOrderId", context.getOrderId()));
            queryBuilder.must(subQueryBuilder);
        }
        if(StringUtils.isNotBlank(context.getMedicalPromiseId())){
            queryBuilder.must(QueryBuilders.termQuery("medicalPromiseId", context.getMedicalPromiseId()));
        }
        if(StringUtils.isNotBlank(context.getPromiseId())){
            queryBuilder.must(QueryBuilders.termQuery("promiseId", context.getPromiseId()));
        }
        if(StringUtils.isNotBlank(context.getSpecimenCode())){
            queryBuilder.must(QueryBuilders.termQuery("specimenCode", context.getSpecimenCode()));
        }
        if(StringUtils.isNotBlank(context.getTaskBizExtStatus())){
            //是否上传服务记录：  全部、是、否
            //1 是  0 否
            if("1".equals(context.getTaskBizExtStatus())){
                queryBuilder.must(QueryBuilders.termsQuery("uploadServiceRecord", Lists.newArrayList("1","2")));
            }else if("0".equals(context.getTaskBizExtStatus())){
                queryBuilder.mustNot(QueryBuilders.termsQuery("uploadServiceRecord", Lists.newArrayList("1","2")));
            }
        }
        if(StringUtils.isNotBlank(context.getUserPin())){
            queryBuilder.must(QueryBuilders.termQuery("userPin", context.getUserPin()));
        }
        if(StringUtils.isNotBlank(context.getCommonStatus())){
            /**
             *
             7退款中 jdOrderFull.getOrderStatus() == 8
             8已退款  jdOrderFull.getOrderStatus() == 9
             9送检中 jdOrderFull.getMedicalPromiseStatus() ==3
             10已送达 jdOrderFull.getMedicalPromiseStatus() ==4
             11检测中 jdOrderFull.getMedicalPromiseStatus() ==4
             12已出报告 jdOrderFull.getMedicalPromiseStatus() ==5
             1 待预约 jdOrderFull.getPromiseStatus()==1;   jdOrderFull.getOrderStatus() != 7\8\9
             2待接单  jdOrderFull.getPromiseStatus()==2;
             3已接单 jdOrderFull.getPromiseStatus()==3;
               没有取消中 @张鑫
             4开始上门 jdOrderFull.getPromiseStatus()==15;
             5服务中 jdOrderFull.getPromiseStatus()==16;
             6采样完成/服务完成 jdOrderFull.getPromiseStatus()==17
             */
            //待预约
            if("1".equals(context.getCommonStatus())){
                queryBuilder.must(QueryBuilders.termQuery("promiseStatus", JdhPromiseStatusEnum.WAIT_PROMISE.getStatus().toString()));
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));
                queryBuilder.must(QueryBuilders.termsQuery("medicalPromiseStatus", Lists.newArrayList("1")));
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
            } else if("2".equals(context.getCommonStatus())){
                //待接单
                queryBuilder.must(QueryBuilders.termsQuery("promiseStatus", Lists.newArrayList(JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus().toString(), JdhPromiseStatusEnum.MODIFY_ING.getStatus().toString())));
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));
                queryBuilder.must(QueryBuilders.termsQuery("medicalPromiseStatus", Lists.newArrayList("1")));
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
            } else if("3".equals(context.getCommonStatus())){
                //已接单
                queryBuilder.must(QueryBuilders.termsQuery("promiseStatus", Lists.newArrayList(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS.getStatus().toString(), JdhPromiseStatusEnum.MODIFY_SUCCESS.getStatus().toString())));
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));//订单状态不能是 已取消 退款中 已退款
                queryBuilder.must(QueryBuilders.termsQuery("medicalPromiseStatus", Lists.newArrayList("1")));//检测项状态不能是 7 冻结 8 已作废
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
            }else if("4".equals(context.getCommonStatus())){
                //开始上门
                queryBuilder.must(QueryBuilders.termQuery("promiseStatus", JdhPromiseStatusEnum.SERVICE_READY.getStatus().toString()));
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));
                queryBuilder.must(QueryBuilders.termsQuery("medicalPromiseStatus", Lists.newArrayList("1")));
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
            } else if("5".equals(context.getCommonStatus())){
                //服务中
                queryBuilder.must(QueryBuilders.termQuery("promiseStatus", JdhPromiseStatusEnum.SERVICING.getStatus().toString()));
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));
                queryBuilder.must(QueryBuilders.termsQuery("medicalPromiseStatus", Lists.newArrayList("1")));
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
            } else if("6".equals(context.getCommonStatus())){
                //采样完成
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));
                queryBuilder.must(QueryBuilders.termsQuery("medicalPromiseStatus", Lists.newArrayList("2")));
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
                queryBuilder.must(QueryBuilders.termQuery("serviceType", ServiceTypeEnum.TEST.getServiceType()));
            } else if("7".equals(context.getCommonStatus())){
                //退款中
                BoolQueryBuilder sub = QueryBuilders.boolQuery();
                sub.should(QueryBuilders.termQuery("orderStatus", OrderStatusEnum.ORDER_REFUNDING.getStatus().toString()));
                sub.should(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
                queryBuilder.mustNot(QueryBuilders.termQuery("medicalPromiseStatus", MedicalPromiseStatusEnum.INVALID.getStatus().toString()));
                queryBuilder.must(sub);
            } else if("8".equals(context.getCommonStatus())){
                //已退款
//                BoolQueryBuilder sub = QueryBuilders.boolQuery();
//                sub.should(QueryBuilders.termQuery("orderStatus", OrderStatusEnum.ORDER_REFUND.getStatus().toString()));
//                sub.should(QueryBuilders.termQuery("medicalPromiseStatus", MedicalPromiseStatusEnum.INVALID.getStatus().toString()));
//                queryBuilder.must(sub);
                queryBuilder.must(QueryBuilders.termQuery("medicalPromiseStatus", MedicalPromiseStatusEnum.INVALID.getStatus().toString()));
            } else if("9".equals(context.getCommonStatus())){
                //送检中
                queryBuilder.must(QueryBuilders.termQuery("medicalPromiseStatus", MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus().toString()));
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));//订单状态不能是 已取消 退款中 已退款
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
                queryBuilder.must(QueryBuilders.termQuery("serviceType", ServiceTypeEnum.TEST.getServiceType()));
            } else if("10".equals(context.getCommonStatus())){
                //已送达
                queryBuilder.must(QueryBuilders.termQuery("medicalPromiseStatus", MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus().toString()));
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));//订单状态不能是 已取消 退款中 已退款
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
                queryBuilder.must(QueryBuilders.termQuery("serviceType", ServiceTypeEnum.TEST.getServiceType()));
                queryBuilder.must(QueryBuilders.termQuery("workStatus", AngelWorkStatusEnum.COMPLETED.getType().toString()));
            } else if("11".equals(context.getCommonStatus())){
                //检测中
                queryBuilder.must(QueryBuilders.termQuery("medicalPromiseStatus", MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus().toString()));
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));//订单状态不能是 已取消 退款中 已退款
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
                queryBuilder.must(QueryBuilders.termQuery("serviceType", ServiceTypeEnum.TEST.getServiceType()));
            } else if("12".equals(context.getCommonStatus())){
                //已出报告
                queryBuilder.must(QueryBuilders.termsQuery("medicalPromiseStatus",  MedicalPromiseStatusEnum.COMPLETED.getStatus().toString()));
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));//订单状态不能是 已取消 退款中 已退款
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
                queryBuilder.must(QueryBuilders.termQuery("serviceType", ServiceTypeEnum.TEST.getServiceType()));
            } else if("13".equals(context.getCommonStatus())){
                //服务完成
                queryBuilder.must(QueryBuilders.termsQuery("medicalPromiseStatus",  MedicalPromiseStatusEnum.COMPLETED.getStatus().toString()));
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));//订单状态不能是 已取消 退款中 已退款
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
                queryBuilder.must(QueryBuilders.termQuery("serviceType", ServiceTypeEnum.CARE.getServiceType()));
            }else if("14".equals(context.getCommonStatus())){
                //取消预约中
                queryBuilder.must(QueryBuilders.termsQuery("promiseStatus",  JdhPromiseStatusEnum.CANCEL_ING.getStatus().toString()));
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));//订单状态不能是 已取消 退款中 已退款
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
//                queryBuilder.must(QueryBuilders.termQuery("serviceType", ServiceTypeEnum.CARE.getServiceType()));
            }else if("15".equals(context.getCommonStatus())){
                //取消预约成功
                queryBuilder.must(QueryBuilders.termsQuery("promiseStatus",  JdhPromiseStatusEnum.CANCEL_SUCCESS.getStatus().toString()));
                queryBuilder.mustNot(QueryBuilders.termsQuery("orderStatus", Lists.newArrayList("7","8","9")));//订单状态不能是 已取消 退款中 已退款
                queryBuilder.mustNot(QueryBuilders.termQuery("medPromiseFreeze", JdhFreezeEnum.FREEZE.getStatus().toString()));
//                queryBuilder.must(QueryBuilders.termQuery("serviceType", ServiceTypeEnum.CARE.getServiceType()));
            }
        }
        if(StringUtils.isNotBlank(context.getAngelName())){//短语匹配查询 text类型
            queryBuilder.must(QueryBuilders.matchPhraseQuery("angelName", context.getAngelName()));
        }
        if(StringUtils.isNotBlank(context.getUserPhone())){
            queryBuilder.must(QueryBuilders.termQuery("userPhone", context.getUserPhone()));
        }
        if(StringUtils.isNotBlank(context.getLaboratoryStationName())){//短语匹配查询 text类型
            queryBuilder.must(QueryBuilders.matchPhraseQuery("laboratoryStationName", context.getLaboratoryStationName()));
        }
        if(context.getOrderCreateTimeStart() != null){
            queryBuilder.must(QueryBuilders.rangeQuery("orderCreateTime").gte(context.getOrderCreateTimeStart().getTime()));
        }
        if(context.getOrderCreateTimeEnd() != null){
            queryBuilder.must(QueryBuilders.rangeQuery("orderCreateTime").lte(context.getOrderCreateTimeEnd().getTime()));
        }
        if(context.getAppointmentStartTime() != null){
            queryBuilder.must(QueryBuilders.rangeQuery("appointmentStartTime").gte(context.getAppointmentStartTime().getTime()));
        }
        if(context.getAppointmentEndTime() != null){
            queryBuilder.must(QueryBuilders.rangeQuery("appointmentEndTime").lte(context.getAppointmentEndTime().getTime()));
        }
        if(context.getCheckStartTime() != null){
            queryBuilder.must(QueryBuilders.rangeQuery("checkTime").gte(context.getCheckStartTime().getTime()));
        }
        if(context.getCheckEndTime() != null){
            queryBuilder.must(QueryBuilders.rangeQuery("checkTime").lte(context.getCheckEndTime().getTime()));
        }
        if(Objects.nonNull(context.getWareType())){
            queryBuilder.must(QueryBuilders.termQuery("wareType",context.getWareType()));
        }
        if(Objects.nonNull(context.getIncludeAddSku())){
            queryBuilder.must(QueryBuilders.termQuery("includeAddSku",context.getIncludeAddSku()));
        }
        if(StrUtil.isNotBlank(context.getOrderType())){
            queryBuilder.must(QueryBuilders.termQuery("orderType",context.getOrderType()));
        }
        //根据实验室id查询
        if(StrUtil.isNotBlank(context.getStationId())){
            queryBuilder.must(QueryBuilders.termQuery("laboratoryStationId",context.getStationId()));
        }
        //根据检测单状态查询
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(context.getMedicalPromiseStatus())){
            List<String> ss = new ArrayList<>();
            for(Integer status:context.getMedicalPromiseStatus()){
                ss.add(status+"");
            }
            queryBuilder.must(QueryBuilders.termsQuery("medicalPromiseStatus", ss));
        }

        //根据实验室id查询
        if(StrUtil.isNotBlank(context.getOutShipId())){
            queryBuilder.must(QueryBuilders.termQuery("outShipId",context.getOutShipId()));
        }
        return queryBuilder;
    }

    private List<JdOrderFull> convert2JdOrderFullList(SearchResponse response) {
        List<JdOrderFull> resList = Lists.newArrayList();
        for (SearchHit hit : response.getHits()) {
            String json = hit.getSourceAsString();
            JdOrderFull res = JSON.parseObject(json, JdOrderFull.class);
            res.setId(hit.getId());
            resList.add(res);
        }
        return resList;
    }

    private QueryBuilder buildQueryBuilder(JdOrderIdContext jdOrderIdContext) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("orderId",jdOrderIdContext.getOrderId()));
        return queryBuilder;
    }

    private Boolean buildEsOrder(SearchHits searchHits, CopyOnWriteArrayList<JdOrderFull> res) {
        searchHits.forEach(e ->{
            log.info("getSourceAsString={}", e.getSourceAsString());
            // 导出场景下可以过滤一下字段
            JdOrderFull dto = JsonUtil.parseObject(e.getSourceAsString(), JdOrderFull.class);
            res.add(dto);
        });
        return true;
    }

}
