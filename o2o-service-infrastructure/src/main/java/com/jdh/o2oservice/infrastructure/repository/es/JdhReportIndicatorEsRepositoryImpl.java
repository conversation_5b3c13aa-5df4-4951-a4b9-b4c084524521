package com.jdh.o2oservice.infrastructure.repository.es;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.EnvEnum;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.report.ReportErrorCode;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicatorES;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderFullPageContext;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderIdContext;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderFull;
import com.jdh.o2oservice.core.domain.trade.repository.es.JdhReportIndicatorEsRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.client.transport.TransportClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 18:13
 * @Desc :
 */
@Slf4j
@Repository
public class JdhReportIndicatorEsRepositoryImpl implements JdhReportIndicatorEsRepository {

    /**
     * defaultIndexName
     */
    private final static String defaultIndexName = "medical_report_indicator_index";

    /**
     * indexName
     */
    private static String indexName = "medical_report_indicator_index";
    /**
     * typeName
     */
    private final static String typeName = "doc";

    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        if(!EnvEnum.PRODUCTION.getCode().equals(value)){
            indexName = defaultIndexName + "_" +value;
        } else {
            indexName = defaultIndexName;
        }
    }

    @Resource
    private EsClientFactoryHealthcare esClientFactoryHealthcare;

    /**
     * 保存
     *
     * @param medicalReportIndicatorES@return
     */
    @Override
    public Boolean save(MedicalReportIndicatorES medicalReportIndicatorES) {
        try {
            AssertUtils.hasText(medicalReportIndicatorES.getReportId(), SystemErrorCode.PARAM_NULL_ERROR.formatDescription("报告ID"));
            AssertUtils.nonNull(medicalReportIndicatorES.getIndicatorId(), SystemErrorCode.PARAM_NULL_ERROR.formatDescription("指标ID"));
            esClientFactoryHealthcare.updateOrInsertDocument(indexName, typeName, medicalReportIndicatorES.getReportId() + "_" + medicalReportIndicatorES.getIndicatorId(), medicalReportIndicatorES);
            return true;
        }catch (Exception e){
            log.error("JdhReportIndicatorEsRepositoryImpl.save error",e);
            throw new RuntimeException(e.getMessage());
        }
    }
}
