package com.jdh.o2oservice.infrastructure.rpc.convert;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.order.sdk.domain.param.OrderInfoQueryVoParam;
import com.jd.order.sdk.domain.result.OrderInfoQueryResult;
import com.jd.order.sdk.domain.vo.*;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.OrderTypeEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TdeClientUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.settlement.bo.JdOrderMoneyBo;
import com.jdh.o2oservice.core.domain.settlement.bo.VoucherBo;
import com.jdh.o2oservice.core.domain.trade.context.AppointmentTimeContext;
import com.jdh.o2oservice.core.domain.trade.context.IntendedNurseConText;
import com.jdh.o2oservice.core.domain.trade.context.OrderInfoQueryContext;
import com.jdh.o2oservice.core.domain.trade.enums.OrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.*;
import com.jdh.o2oservice.core.domain.trade.vo.*;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderMoneyDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.jdh.o2oservice.base.util.TimeFormat.LONG_PATTERN_LINE_NO_S;

/**
 * OrderInfoConverter 订单信息转化
 *
 * <AUTHOR>
 * @version 2024/4/23 17:03
 **/
@Mapper
public interface OrderInfoConverter {


    OrderInfoConverter convertor = Mappers.getMapper(OrderInfoConverter.class);

    @Mapping(source = "userPin", target = "pin")
    OrderInfoQueryVoParam orderInfoQueryContext2Param(OrderInfoQueryContext context);

    static int COUPON_ASSET_TYPE = 5;

    OrderAppointmentTimeValueObject converterOrderAppointmentTimeValueObject(AppointmentTimeContext appointmentTimeContext);

    default AddressInfoValueObject addrInfoVo2AddressInfo(AddrInfoVo addrInfoVo,Map<String, String> extTagMap) {
        if (addrInfoVo == null) {
            return null;
        }
        AddressInfoValueObject addressInfoValueObject = new AddressInfoValueObject();
        if(CollUtil.isNotEmpty(extTagMap) && extTagMap.containsKey("addressId")){
            addressInfoValueObject.setId(Long.parseLong(extTagMap.get("addressId")));
        }
        addressInfoValueObject.setName(SpringUtil.getBean(TdeClientUtil.class).decrypt(addrInfoVo.getCustomerName().getEnc()));
        Long[] areas = addrInfoVo.getAreas();
        if (areas.length >= 3) {
            addressInfoValueObject.setProvinceId(areas[0].intValue());
            addressInfoValueObject.setCityId(areas[1].intValue());
            addressInfoValueObject.setCountyId(areas[2].intValue());
        }
        if (areas.length == 4) {
            addressInfoValueObject.setTownId(areas[3].intValue());
        }
        addressInfoValueObject.setFullAddress(SpringUtil.getBean(TdeClientUtil.class).decrypt(addrInfoVo.getAddress().getEnc()));
        addressInfoValueObject.setMobile(SpringUtil.getBean(TdeClientUtil.class).decrypt(addrInfoVo.getMobile().getEnc()));
        return addressInfoValueObject;
    }

    default JdOrder orderInfoQueryResult2JdOrder(OrderInfoQueryResult result, OrderInfoQueryContext context) {
        JdOrder jdOrder = new JdOrder();
        OrderPriceVo priceVo = result.getOrderPriceVo();
        if (priceVo == null) {
            priceVo = new OrderPriceVo();
        }
        Map<String, String> priceVoExtendMap = priceVo.getExtendMap();
        List<FeeVo> feeVos = priceVo.getFeeVOs();
        if (feeVos == null) {
            feeVos = new ArrayList<>();
        }
        BaseInfoVo baseInfoVo = result.getBaseInfoVo();
        if (baseInfoVo == null) {
            baseInfoVo = new BaseInfoVo();
        }
        if (priceVo.getPrice().matches("\\d+\\.?\\d*")) {
            jdOrder.setOrderTotalAmount(new BigDecimal(priceVo.getPrice()));
        }
        DisplayPayInfoVo displayPayInfoVo = result.getDisplayPayInfoVo();
        jdOrder.setId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        if(MapUtils.isNotEmpty(priceVoExtendMap)){
            if(priceVoExtendMap.containsKey("promotiondiscount")){
                jdOrder.setOrderDiscount(new BigDecimal(priceVoExtendMap.get("promotiondiscount")));
            }
            if(priceVoExtendMap.containsKey("OUTER_orderPrice")){
                jdOrder.setOrderTotalAmount(new BigDecimal(priceVoExtendMap.get("OUTER_orderPrice")));
            }
        }
        if(CollectionUtils.isNotEmpty(baseInfoVo.getAssetVos())){
            Optional<AssetVo> assetVoOptional = baseInfoVo.getAssetVos().stream().filter(s -> Objects.nonNull(s.getAssetType()) && s.getAssetType().intValue() == COUPON_ASSET_TYPE).findFirst();
            if (Objects.nonNull(assetVoOptional) && assetVoOptional.isPresent() && Objects.nonNull(assetVoOptional.get())){
                jdOrder.setOrderCoupon(assetVoOptional.get().getBigDiscount());
            }
        }
        jdOrder.setOrderId(result.getOrderid());
        jdOrder.setOrderType(baseInfoVo.getOrderType());
        // 4号单 不记录 父单号
        if(OrderTypeEnum.XFYL_VTP_VIRTUAL_ORDER_TYPE.getType().equals(jdOrder.getOrderType())){
            jdOrder.setParentId(0L);
        } else {
            jdOrder.setParentId(baseInfoVo.getParentId());
        }
        jdOrder.setOriginParentOrderId(baseInfoVo.getParentId());
        jdOrder.setOrderStatus(orderInfoQueryResult2JdOrderStatus(result.getOrderStatusInfoVo()));
        jdOrder.setSendPay(baseInfoVo.getSendPay());
        jdOrder.setUserPin(baseInfoVo.getPin());
        if (displayPayInfoVo.getPayDate() != null && displayPayInfoVo.getPayDate().longValue() > 0) {
            jdOrder.setPaymentTime(new Date(displayPayInfoVo.getPayDate()));
        }
        jdOrder.setPaymentWay("0");
        jdOrder.setPayType(baseInfoVo.getIdPaymentType());
        jdOrder.setRemark(context.getRemark());
        jdOrder.setPartnerSource(context.getPartnerSource());
        jdOrder.setPartnerSourceOrderId(context.getPartnerSourceOrderId());
        jdOrder.setOrderAmount(priceVo.getActualPay());

        BigDecimal serviceFee = new BigDecimal(feeVos.stream().filter(item -> item.getFeeBiz().intValue() == 2).map(item -> Double.valueOf(item.getFee())).mapToDouble(Double::doubleValue).sum());
        jdOrder.setServiceFee(serviceFee);
        if (displayPayInfoVo.getDateSubmit() != null && displayPayInfoVo.getDateSubmit().longValue() > 0) {
            jdOrder.setCreateTime(new Date(displayPayInfoVo.getDateSubmit()));
        }
        if (displayPayInfoVo.getPaySureDate() != null && displayPayInfoVo.getPaySureDate().longValue() > 0) {
            jdOrder.setPaymentTime(new Date(displayPayInfoVo.getPaySureDate()));
        }

        orderInfoQueryResult2ChannelName(jdOrder, context);
        orderInfoQueryResult2JdOrderItems(jdOrder, result);
        orderInfoQueryResult2JdOrderStatuses(jdOrder, result);
        orderInfoQueryResult2JdOrderMoneys(jdOrder, result);
        orderInfoQueryResult2JdOrderExt(jdOrder, result, context);
        orderInfoQueryResult2JdOrderServiceFee(jdOrder, result);
        orderInfoQueryResult2JdOrderAssetDetail(jdOrder, baseInfoVo.getAssetWithDetailVos());
        orderInfoQueryResult2JdOrderAppointmentInfo(jdOrder, result, context);
        orderInfoQueryResult2JdOrderIntendedNurse(jdOrder, context);
        orderInfoQueryResult2SaleChannelId(jdOrder, context.getSaleChannelId());
        orderInfoQueryResult2JdOrderEnvType(jdOrder, context);
        if(StringUtils.isNotBlank(baseInfoVo.getSendPayMap())) {
            jdOrder.setSendPayMap(JSON.parseObject(baseInfoVo.getSendPayMap(), new TypeReference<Map<String, String>>() {
            }));
        }
        return jdOrder;
    }

    default void orderInfoQueryResult2JdOrderItems(JdOrder jdOrder, OrderInfoQueryResult result) {
        RelationVo relationVo = result.getRelationVo();
        if (relationVo == null) {
            return;
        }
        List<SkuInfoVo> skuInfoVos = relationVo.getSkuInfoVos();
        if (skuInfoVos == null || skuInfoVos.size() == 0) {
            return;
        }
        List<JdOrderItem> jdOrderItemList = new ArrayList<>();
        skuInfoVos.forEach(item -> {
            JdOrderItem jdOrderItem = new JdOrderItem();
            jdOrderItem.setOrderItemId(SpringUtil.getBean(GenerateIdFactory.class).getId());
            jdOrderItem.setOrderId(jdOrder.getOrderId());
            jdOrderItem.setItemAmount(item.getSingleShouldPrice());
            jdOrderItem.setItemDiscount(item.getDiscountPrice());
            jdOrderItem.setCid1(item.getCidFirst());
            jdOrderItem.setCid2(item.getCid2());
            jdOrderItem.setCid3(item.getCid());
            jdOrderItem.setCreateTime(jdOrder.getCreateTime());
            jdOrderItem.setItemTotalAmount(item.getPrice());
            jdOrderItem.setSkuId(item.getProductId());
            jdOrderItem.setSkuImage(item.getImgPath());
            jdOrderItem.setSkuNum(item.getNum());
            jdOrderItem.setSkuName(item.getName());
            jdOrderItem.setUserPin(jdOrder.getUserPin());
            jdOrderItem.setSkuExpireDate(DateUtil.addDays(new Date(), 365));
            orderInfoQueryResult2WareType(jdOrder, item.getExtendMap().get("wareType"), jdOrderItem);
            if(Objects.nonNull(item.getExtendMap()) && Objects.nonNull(item.getExtendMap().get("shopId"))){
                jdOrder.setVenderId(item.getExtendMap().get("shopId"));
            }
            jdOrderItemList.add(jdOrderItem);
        });
        jdOrder.setJdOrderItemList(jdOrderItemList);
    }

    default void orderInfoQueryResult2WareType(JdOrder jdOrder, String wareType, JdOrderItem jdOrderItem){
        if(Objects.isNull(jdOrder) || !StringUtils.isNumeric(wareType)){
            return;
        }
        JdOrderExtendVo jdOrderExtendVo = new JdOrderExtendVo();
        if (StringUtils.isNotBlank(jdOrder.getExtend())) {
            jdOrderExtendVo = JSON.parseObject(jdOrder.getExtend(), JdOrderExtendVo.class);
        }
        jdOrderExtendVo.setWareType(Integer.valueOf(wareType));
        jdOrder.setExtend(JSON.toJSONString(jdOrderExtendVo));
    }

    default void orderInfoQueryResult2SaleChannelId(JdOrder jdOrder, String saleChannelId){
        if(StringUtils.isBlank(saleChannelId)){
            return;
        }
        JdOrderExtendVo jdOrderExtendVo = new JdOrderExtendVo();
        if (StringUtils.isNotBlank(jdOrder.getExtend())) {
            jdOrderExtendVo = JSON.parseObject(jdOrder.getExtend(), JdOrderExtendVo.class);
        }
        jdOrderExtendVo.setSaleChannelId(saleChannelId);
        jdOrder.setExtend(JSON.toJSONString(jdOrderExtendVo));
    }

    default void orderInfoQueryResult2ChannelName(JdOrder jdOrder, OrderInfoQueryContext context){
        if(Objects.isNull(jdOrder) || Objects.isNull(context) || StringUtil.isEmpty(context.getChannelName()) ){
            return;
        }
        JdOrderExtendVo jdOrderExtendVo = new JdOrderExtendVo();
        if (StringUtils.isNotBlank(jdOrder.getExtend())) {
            jdOrderExtendVo = JSON.parseObject(jdOrder.getExtend(), JdOrderExtendVo.class);
        }
        jdOrderExtendVo.setChannelName(context.getChannelName());
        jdOrder.setExtend(JSON.toJSONString(jdOrderExtendVo));
    }

    default void orderInfoQueryResult2JdOrderStatuses(JdOrder jdOrder, OrderInfoQueryResult result) {
        JdOrderStatus jdOrderStatus = JdOrderStatus.builder().build();
        jdOrderStatus.setOrderId(jdOrder.getOrderId());
        jdOrderStatus.setStatus(jdOrder.getOrderStatus());
        jdOrderStatus.setVersion(NumConstant.NUM_1);
        jdOrderStatus.setYn(YnStatusEnum.YES.getCode());
        jdOrderStatus.setStatusTime(new Date());
        jdOrderStatus.setCreateTime(new Date());
        jdOrderStatus.setUpdateTime(new Date());
        List<JdOrderStatus> jdOrderStatusList = new ArrayList<>();
        jdOrderStatusList.add(jdOrderStatus);
        jdOrder.setJdOrderStatusList(jdOrderStatusList);
    }

    default void orderInfoQueryResult2JdOrderMoneys(JdOrder jdOrder, OrderInfoQueryResult result) {
    }

    default void orderInfoQueryResult2JdOrderAssetDetail(JdOrder jdOrder, List<AssetWithDetailVo> assetWithDetailVoList) {
        if(CollectionUtils.isEmpty(assetWithDetailVoList)){
            return;
        }
        Optional<AssetWithDetailVo> assetDetailVoOptional = assetWithDetailVoList.stream().filter(s -> Objects.nonNull(s.getAssetType()) && s.getAssetType().intValue() == COUPON_ASSET_TYPE).findFirst();
        if (Objects.isNull(assetDetailVoOptional) || !assetDetailVoOptional.isPresent() || Objects.isNull(assetDetailVoOptional.get())){
            return;
        }
        List<JdOrderExt> jdOrderExtList = jdOrder.getJdOrderExtList();
        if (jdOrderExtList == null) {
            jdOrderExtList = Lists.newArrayList();
        }
        JdOrderExt jdOrderExt = new JdOrderExt();
        jdOrderExtList.add(jdOrderExt);
        jdOrderExt.setExtType(OrderExtTypeEnum.COUPON_INFO.getType());
        jdOrderExt.setExtContext(JSON.toJSONString(assetDetailVoOptional.get().getAssetDetails()));
        jdOrderExt.setYn(YnStatusEnum.YES.getCode());
        jdOrderExt.setOrderId(jdOrder.getOrderId());
        jdOrderExt.setExtId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        jdOrderExt.setVersion(NumConstant.NUM_1);
        jdOrderExt.setCreateTime(new Date());
        jdOrderExt.setUpdateTime(new Date());
        jdOrder.setJdOrderExtList(jdOrderExtList);
    }

    default void orderInfoQueryResult2JdOrderExt(JdOrder jdOrder, OrderInfoQueryResult result, OrderInfoQueryContext context) {
        Map<String, String> orderExtMap = result.getExtTagMap();
        if (MapUtils.isEmpty(orderExtMap)) {
            return;
        }

        List<JdOrderExt> jdOrderExtList = jdOrder.getJdOrderExtList();
        if (jdOrderExtList == null) {
            jdOrderExtList = Lists.newArrayList();
        }
        Iterator<Map.Entry<String, String>> iterator = orderExtMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> next = iterator.next();
            // 仅保存定义了扩展类型
            if (!OrderExtTypeEnum.exist(next.getKey())) {
                continue;
            }
            //如果是立即预约，修改预约时间为订单创建时间
            if (OrderExtTypeEnum.APPOINTMENT_INFO.getType().equals(next.getKey())){
                OrderAppointmentInfoValueObject orderAppointmentInfoValueObject=JSON.parseObject(next.getValue(),OrderAppointmentInfoValueObject.class);
                if (Objects.nonNull(orderAppointmentInfoValueObject.getAppointmentTime())&&orderAppointmentInfoValueObject.getAppointmentTime().getIsImmediately()){
                    orderAppointmentInfoValueObject.getAppointmentTime().setAppointmentStartTime(TimeUtils.dateTimeToStr(DateUtil.addMinutes(new Date(),1),LONG_PATTERN_LINE_NO_S));
                    next.setValue(JSON.toJSONString(orderAppointmentInfoValueObject));
                }
            }
            JdOrderExt jdOrderExt = new JdOrderExt();
            jdOrderExt.setExtType(next.getKey());
            jdOrderExt.setExtContext(next.getValue());
            jdOrderExt.setYn(YnStatusEnum.YES.getCode());
            jdOrderExt.setOrderId(jdOrder.getOrderId());
            jdOrderExt.setExtId(SpringUtil.getBean(GenerateIdFactory.class).getId());
            jdOrderExt.setVersion(NumConstant.NUM_1);
            jdOrderExt.setCreateTime(new Date());
            jdOrderExt.setUpdateTime(new Date());
            jdOrderExtList.add(jdOrderExt);
        }
        jdOrder.setJdOrderExtList(jdOrderExtList);
    }

    default void orderInfoQueryResult2JdOrderServiceFee(JdOrder jdOrder, OrderInfoQueryResult result) {
        if (result.getOrderPriceVo() == null || result.getOrderPriceVo().getFeeVOs() == null) {
            return;
        }
        List<FeeVo> feeVos = result.getOrderPriceVo().getFeeVOs();
        if (feeVos == null || feeVos.size() == 0) {
            return;
        }
        List<JdOrderExt> jdOrderExtList = jdOrder.getJdOrderExtList();
        if (jdOrderExtList == null) {
            jdOrderExtList = Lists.newArrayList();
        }
        List<FeeVo> filterFeevos = feeVos.stream().filter(item -> item.getFeeBiz().intValue() == 2).collect(Collectors.toList());
        if (filterFeevos == null || filterFeevos.size() == 0) {
            return;
        }
        JdOrderExt jdOrderExt = new JdOrderExt();
        jdOrderExtList.add(jdOrderExt);
        jdOrderExt.setExtType(OrderExtTypeEnum.SERVICE_FEE_INFO.getType());
        jdOrderExt.setExtContext(JSON.toJSONString(converterJdOrderServiceFeeInfos(filterFeevos)));
        jdOrderExt.setYn(YnStatusEnum.YES.getCode());
        jdOrderExt.setOrderId(jdOrder.getOrderId());
        jdOrderExt.setExtId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        jdOrderExt.setVersion(NumConstant.NUM_1);
        jdOrderExt.setCreateTime(new Date());
        jdOrderExt.setUpdateTime(new Date());
        jdOrder.setJdOrderExtList(jdOrderExtList);
    }

    default void orderInfoQueryResult2JdOrderAppointmentInfo(JdOrder jdOrder, OrderInfoQueryResult result, OrderInfoQueryContext context) {
        if (result.getBaseInfoVo() == null || result.getBaseInfoVo().getAddrInfoVo() == null) {
            return;
        }
        AddrInfoVo addrInfoVo = result.getBaseInfoVo().getAddrInfoVo();
        AddressInfoValueObject addressInfo = addrInfoVo2AddressInfo(addrInfoVo,result.getExtTagMap());
        OrderAppointmentInfoValueObject orderAppointmentInfoValueObject = new OrderAppointmentInfoValueObject();
        orderAppointmentInfoValueObject.setAddressInfo(addressInfo);
        orderAppointmentInfoValueObject.setAppointmentTime(converterOrderAppointmentTimeValueObject(context.getAppointmentTimeContext()));
        orderAppointmentInfoValueObject.setPatients(context.getPatients());
        orderAppointmentInfoValueObject.setPreSampleFlag(context.getPreSampleFlag());
        List<JdOrderExt> jdOrderExtList = jdOrder.getJdOrderExtList();
        if (jdOrderExtList == null) {
            jdOrderExtList = Lists.newArrayList();
        }
        JdOrderExt jdOrderExt = new JdOrderExt();
        jdOrderExtList.add(jdOrderExt);
        jdOrderExt.setExtType(OrderExtTypeEnum.APPOINTMENT_INFO.getType());
        jdOrderExt.setExtContext(JSON.toJSONString(orderAppointmentInfoValueObject));
        jdOrderExt.setYn(YnStatusEnum.YES.getCode());
        jdOrderExt.setOrderId(jdOrder.getOrderId());
        jdOrderExt.setExtId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        jdOrderExt.setVersion(NumConstant.NUM_1);
        jdOrderExt.setCreateTime(new Date());
        jdOrderExt.setUpdateTime(new Date());
        jdOrder.setJdOrderExtList(jdOrderExtList);
    }

    default void orderInfoQueryResult2JdOrderIntendedNurse(JdOrder jdOrder, OrderInfoQueryContext context) {
        IntendedNurseConText intendedNurse = context.getIntendedNurse();
        if (intendedNurse == null) {
            return;
        }
        List<JdOrderExt> jdOrderExtList = jdOrder.getJdOrderExtList();
        if (jdOrderExtList == null) {
            jdOrderExtList = Lists.newArrayList();
        }
        JdOrderExt jdOrderExt = new JdOrderExt();
        jdOrderExtList.add(jdOrderExt);
        jdOrderExt.setExtType(OrderExtTypeEnum.INTENDED_NURSE.getType());
        jdOrderExt.setExtContext(JSON.toJSONString(intendedNurse));
        jdOrderExt.setYn(YnStatusEnum.YES.getCode());
        jdOrderExt.setOrderId(jdOrder.getOrderId());
        jdOrderExt.setExtId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        jdOrderExt.setVersion(NumConstant.NUM_1);
        jdOrderExt.setCreateTime(new Date());
        jdOrderExt.setUpdateTime(new Date());
        jdOrder.setJdOrderExtList(jdOrderExtList);
    }

    default void orderInfoQueryResult2JdOrderEnvType(JdOrder jdOrder, OrderInfoQueryContext context) {
        if (StringUtils.isBlank(context.getEnvType())){
            return;
        }
        List<JdOrderExt> jdOrderExtList = jdOrder.getJdOrderExtList();
        if (jdOrderExtList == null) {
            jdOrderExtList = Lists.newArrayList();
        }
        JdOrderExt jdOrderExt = new JdOrderExt();
        jdOrderExtList.add(jdOrderExt);

        jdOrderExt.setExtType(OrderExtTypeEnum.ENV_TYPE.getType());
        jdOrderExt.setExtContext(context.getEnvType());
        jdOrderExt.setYn(YnStatusEnum.YES.getCode());
        jdOrderExt.setOrderId(jdOrder.getOrderId());
        jdOrderExt.setExtId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        jdOrderExt.setVersion(NumConstant.NUM_1);
        jdOrderExt.setCreateTime(new Date());
        jdOrderExt.setUpdateTime(new Date());
        jdOrder.setJdOrderExtList(jdOrderExtList);
    }

    default List<JdOrderServiceFeeInfo> converterJdOrderServiceFeeInfos(List<FeeVo> feeVos) {
        List<JdOrderServiceFeeInfo> result = new ArrayList<>();
        try {
            feeVos.stream().forEach(item -> {
                item.getFeeVOs().stream().forEach(i -> {
                    i.getFeeVOs().stream().forEach(j -> {
                        JdOrderServiceFeeInfo jdOrderServiceFeeInfo = JdOrderServiceFeeInfo.builder().build();
                        jdOrderServiceFeeInfo.setAggregateType(item.getType());
                        jdOrderServiceFeeInfo.setAggregateSubType(Integer.valueOf(j.getUid()));
                        String skuFeeList = j.getExtendMap().getOrDefault("skuFeeList", "[]");
                        jdOrderServiceFeeInfo.setServiceFee(new BigDecimal(String.valueOf(JSON.parseArray(skuFeeList).getJSONObject(0).get("preFee"))));
                        result.add(jdOrderServiceFeeInfo);
                    });
                });
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    default Integer orderInfoQueryResult2JdOrderStatus(OrderStatusInfoVo orderStatusInfoVo){
        if(Objects.nonNull(orderStatusInfoVo) && Objects.nonNull(orderStatusInfoVo.getOrderState())){
            int orderState = orderStatusInfoVo.getOrderState().intValue();
            // 新订单0,等待付款1
            if(orderState == 0 || orderState == 1){
                return OrderStatusEnum.ORDER_WAIT_PAY.getStatus();
            } else if(orderState == 2 || orderState == 3 || orderState == 4){
                return OrderStatusEnum.ORDER_PAID.getStatus();
            } else if(orderState == 18){ //plus积分兑换后orderState=18
                return OrderStatusEnum.ORDER_PAID.getStatus();
            }
        }
        return OrderStatusEnum.ORDER_WAIT_PAY.getStatus();
    }

    List<JdOrder> jdOrderDTOToList(List<JdOrderDTO> jdOrderList);

    List<JdOrderMoneyBo> jdOrderMoneyDTOToList(List<JdOrderMoneyDTO> jdOrderList);

    /**
     *
     * @param voucherDto
     * @return
     */
    VoucherBo converterVoucherBo(VoucherDto voucherDto);
}