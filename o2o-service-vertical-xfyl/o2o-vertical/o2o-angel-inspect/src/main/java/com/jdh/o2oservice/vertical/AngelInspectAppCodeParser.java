package com.jdh.o2oservice.vertical;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.jd.matrix.sdk.base.BizCodeParser;
import com.jd.matrix.sdk.base.DomainModel;
import com.jdh.o2oservice.common.ext.O2oBusinessIdentifier;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.Set;


/**
 * @ClassName:TransferAppCodeParser
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/14 22:01
 * @Vserion: 1.0
 **/
@Slf4j
public class AngelInspectAppCodeParser implements BizCodeParser {

    private Set<String> businessModeCodeSet = Sets.newHashSet("angelTest");

    private Set<Integer> angelTypeSet = Sets.newHashSet(2);

    @Override
    public boolean filter(DomainModel domainModel) {
        log.info("执行一检测上门的Parser：{}", JSON.toJSONString(domainModel));
        if(domainModel instanceof O2oBusinessIdentifier) {
            O2oBusinessIdentifier identity = (O2oBusinessIdentifier) domainModel;
            return businessModeCodeSet.contains(identity.getBusinessMode()) && (Objects.isNull(identity.getAngelType()) || angelTypeSet.contains(identity.getAngelType()));
        }
        return false;
    }

    /**
     * 返回当前命中的垂直场景（用于扩展点实现在业务身份维度下的更细粒度的命中）
     *
     * @param domainModel
     * @return
     */
    @Override
    public List<String> parseScenario(DomainModel domainModel) {
        return null;
    }
}
